#!/bin/bash

echo "Bandana Service Migration: Neo4j to MongoDB + Lombok to Modern Java"
echo "================================================================"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.6+ and try again"
    exit 1
fi

# Get the current directory (should be the project root)
PROJECT_ROOT=$(pwd)

echo "Project Root: $PROJECT_ROOT"
echo

# Check if bandana-service directory exists
if [ ! -d "$PROJECT_ROOT/bandana-service" ]; then
    echo "Error: bandana-service directory not found in current directory"
    echo "Please run this script from the project root directory"
    exit 1
fi

echo "Starting migration..."
echo

# Run the migration script
python3 bandana-migration-script.py "$PROJECT_ROOT"

if [ $? -ne 0 ]; then
    echo
    echo "Migration failed! Please check the error messages above."
    exit 1
fi

echo
echo "Migration completed successfully!"
echo
echo "Next steps:"
echo "1. Review the changes in your IDE"
echo "2. Run: mvn clean compile"
echo "3. Test with MongoDB"
echo
