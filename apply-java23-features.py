#!/usr/bin/env python3
"""
Apply cutting-edge Java 23 features to the Bandana Service codebase
- String Templates (Preview)
- Pattern Matching for switch (Standard)
- Record Patterns (Standard)
- Unnamed Patterns and Variables (Preview)
- Sequenced Collections
- Virtual Threads
- Structured Concurrency (Preview)
"""

import os
import re
from pathlib import Path

class Java23Modernizer:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.bandana_service = self.project_root / "bandana-service"
        self.src_main_java = self.bandana_service / "src" / "main" / "java"

    def modernize_project(self):
        """Apply Java 23 features across the project"""
        print("Applying Java 23 cutting-edge features...")
        
        # 1. Update Response record with String Templates
        self.modernize_response_record()
        
        # 2. Create modern service with Virtual Threads
        self.create_virtual_thread_service()
        
        # 3. Apply pattern matching to controllers
        self.modernize_controllers()
        
        # 4. Create examples of latest features
        self.create_java23_examples()
        
        print("Java 23 modernization completed!")

    def modernize_response_record(self):
        """Update Response record with Java 23 String Templates"""
        response_file = self.src_main_java / "lk" / "bandana" / "core" / "entity" / "Response.java"
        
        if not response_file.exists():
            print("Response.java not found")
            return
        
        modern_response = '''package lk.bandana.core.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import java.time.Instant;
import java.time.format.DateTimeFormatter;

/**
 * Response record using Java 23 String Templates and modern patterns
 */
@Document
public record Response<T>(
    @Id String id,
    boolean success,
    String message,
    T data,
    String errorCode,
    Instant timestamp
) {
    
    // Compact constructor with validation using pattern matching
    public Response {
        timestamp = switch (timestamp) {
            case null -> Instant.now();
            case Instant t -> t;
        };
        
        message = switch (message) {
            case null when success -> "Operation completed successfully";
            case null when !success -> "Operation failed";
            case String m -> m;
        };
    }
    
    // Static factory methods using String Templates (Java 23 Preview)
    public static <T> Response<T> success(T data) {
        return new Response<>(
            null,
            true,
            STR."Successfully processed data of type \\{data.getClass().getSimpleName()}",
            data,
            null,
            Instant.now()
        );
    }
    
    public static <T> Response<T> success(String operation, T data) {
        return new Response<>(
            null,
            true,
            STR."\\{operation} completed successfully",
            data,
            null,
            Instant.now()
        );
    }
    
    public static <T> Response<T> error(String message, String errorCode) {
        return new Response<>(
            null,
            false,
            STR."Error [\\{errorCode}]: \\{message}",
            null,
            errorCode,
            Instant.now()
        );
    }
    
    // Pattern matching methods
    public String getFormattedMessage() {
        return switch (this) {
            case Response(_, true, var msg, _, _, var time) -> 
                STR."✓ \\{msg} at \\{DateTimeFormatter.ISO_INSTANT.format(time)}";
            case Response(_, false, var msg, _, var code, var time) -> 
                STR."✗ [\\{code}] \\{msg} at \\{DateTimeFormatter.ISO_INSTANT.format(time)}";
        };
    }
    
    // Unnamed patterns for when we don't need all fields
    public boolean hasValidData() {
        return switch (this) {
            case Response(_, true, _, var data, _, _) when data != null -> true;
            case Response(_, _, _, _, _, _) -> false;
        };
    }
    
    // Modern toString using String Templates
    @Override
    public String toString() {
        return STR."""
            Response {
                success: \\{success}
                message: "\\{message}"
                hasData: \\{data != null}
                timestamp: \\{timestamp}
            }
            """;
    }
}
'''
        
        with open(response_file, 'w', encoding='utf-8') as f:
            f.write(modern_response)
        
        print("✓ Updated Response.java with Java 23 String Templates")

    def create_virtual_thread_service(self):
        """Create a service using Virtual Threads (Java 23)"""
        service_dir = self.src_main_java / "lk" / "bandana" / "core" / "service" / "async"
        service_dir.mkdir(parents=True, exist_ok=True)
        
        virtual_thread_service = '''package lk.bandana.core.service.async;

import org.springframework.stereotype.Service;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.StructuredTaskScope;
import java.util.List;
import java.util.concurrent.Future;

/**
 * Modern async service using Java 23 Virtual Threads and Structured Concurrency
 */
@Service
public class VirtualThreadMatchingService {
    
    private final java.util.concurrent.ExecutorService virtualExecutor = 
        Executors.newVirtualThreadPerTaskExecutor();
    
    /**
     * Process multiple profile matches concurrently using Virtual Threads
     */
    public CompletableFuture<List<MatchResult>> processMatchesConcurrently(
            List<String> profileIds) {
        
        return CompletableFuture.supplyAsync(() -> {
            try (var scope = new StructuredTaskScope.ShutdownOnFailure()) {
                
                // Submit all matching tasks concurrently
                var matchTasks = profileIds.stream()
                    .map(profileId -> scope.fork(() -> processMatch(profileId)))
                    .toList();
                
                // Wait for all tasks to complete
                scope.join();
                scope.throwIfFailed();
                
                // Collect results using pattern matching
                return matchTasks.stream()
                    .map(Future::resultNow)
                    .toList();
                    
            } catch (Exception e) {
                throw new RuntimeException(STR."Failed to process matches: \\{e.getMessage()}", e);
            }
        }, virtualExecutor);
    }
    
    /**
     * Process a single match using modern pattern matching
     */
    private MatchResult processMatch(String profileId) {
        // Simulate processing time
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return new MatchResult.Failed(profileId, "Interrupted");
        }
        
        // Use pattern matching for result processing
        return switch (profileId.length() % 3) {
            case 0 -> new MatchResult.HighCompatibility(profileId, 95);
            case 1 -> new MatchResult.MediumCompatibility(profileId, 75);
            case 2 -> new MatchResult.LowCompatibility(profileId, 45);
            default -> new MatchResult.Failed(profileId, "Unknown error");
        };
    }
    
    /**
     * Sealed interface for match results using modern Java patterns
     */
    public sealed interface MatchResult 
        permits MatchResult.HighCompatibility, 
                MatchResult.MediumCompatibility, 
                MatchResult.LowCompatibility, 
                MatchResult.Failed {
        
        String profileId();
        
        record HighCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return STR."High compatibility match (\\{score}%) for profile \\{profileId}";
            }
        }
        
        record MediumCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return STR."Medium compatibility match (\\{score}%) for profile \\{profileId}";
            }
        }
        
        record LowCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return STR."Low compatibility match (\\{score}%) for profile \\{profileId}";
            }
        }
        
        record Failed(String profileId, String reason) implements MatchResult {
            public String getDescription() {
                return STR."Match failed for profile \\{profileId}: \\{reason}";
            }
        }
        
        // Pattern matching helper
        default String getFormattedResult() {
            return switch (this) {
                case HighCompatibility(var id, var score) -> 
                    STR."🎯 Excellent match for \\{id} (\\{score}%)";
                case MediumCompatibility(var id, var score) -> 
                    STR."👍 Good match for \\{id} (\\{score}%)";
                case LowCompatibility(var id, var score) -> 
                    STR."👌 Possible match for \\{id} (\\{score}%)";
                case Failed(var id, var reason) -> 
                    STR."❌ Failed to match \\{id}: \\{reason}";
            };
        }
    }
}
'''
        
        with open(service_dir / "VirtualThreadMatchingService.java", 'w', encoding='utf-8') as f:
            f.write(virtual_thread_service)
        
        print("✓ Created VirtualThreadMatchingService with Java 23 features")

    def modernize_controllers(self):
        """Apply Java 23 patterns to controllers"""
        controller_files = [
            self.src_main_java / "lk" / "bandana" / "general" / "controller" / "GeneralProfileController.java"
        ]
        
        for controller_file in controller_files:
            if controller_file.exists():
                self.apply_modern_patterns_to_controller(controller_file)

    def apply_modern_patterns_to_controller(self, controller_file):
        """Apply modern Java 23 patterns to a controller"""
        try:
            with open(controller_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add modern error handling method
            modern_error_handling = '''
    
    /**
     * Modern error handling using Java 23 pattern matching and String Templates
     */
    private ResponseEntity<Response<Object>> handleError(Exception e, String operation) {
        var errorResponse = switch (e) {
            case IllegalArgumentException iae -> 
                Response.error(STR."Invalid argument in \\{operation}: \\{iae.getMessage()}", "INVALID_ARGUMENT");
            case SecurityException se -> 
                Response.error(STR."Access denied for \\{operation}: \\{se.getMessage()}", "ACCESS_DENIED");
            case RuntimeException re -> 
                Response.error(STR."Runtime error in \\{operation}: \\{re.getMessage()}", "RUNTIME_ERROR");
            case Exception ex -> 
                Response.error(STR."Unexpected error in \\{operation}: \\{ex.getMessage()}", "UNKNOWN_ERROR");
        };
        
        var httpStatus = switch (e) {
            case IllegalArgumentException _ -> HttpStatus.BAD_REQUEST;
            case SecurityException _ -> HttpStatus.FORBIDDEN;
            case RuntimeException _ -> HttpStatus.INTERNAL_SERVER_ERROR;
            case Exception _ -> HttpStatus.INTERNAL_SERVER_ERROR;
        };
        
        return ResponseEntity.status(httpStatus).body(errorResponse);
    }
    
    /**
     * Validate request using pattern matching
     */
    private ValidationResult validateRequest(Object request) {
        return switch (request) {
            case null -> new ValidationResult(false, "Request cannot be null");
            case UserProfile profile when profile.getFirstName() == null -> 
                new ValidationResult(false, "First name is required");
            case UserProfile profile when profile.getLastName() == null -> 
                new ValidationResult(false, "Last name is required");
            case UserProfile _ -> new ValidationResult(true, "Valid profile");
            case Map<?, ?> map when map.isEmpty() -> 
                new ValidationResult(false, "Request data cannot be empty");
            case Map<?, ?> _ -> new ValidationResult(true, "Valid request");
            default -> new ValidationResult(false, STR."Unsupported request type: \\{request.getClass().getSimpleName()}");
        };
    }
    
    record ValidationResult(boolean isValid, String message) {}'''
            
            # Add before the last closing brace
            content = content.rstrip()
            if content.endswith('}'):
                content = content[:-1] + modern_error_handling + '\n}'
            
            with open(controller_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✓ Applied Java 23 patterns to {controller_file.name}")
        
        except Exception as e:
            print(f"✗ Error modernizing {controller_file.name}: {e}")

    def create_java23_examples(self):
        """Create examples showcasing Java 23 features"""
        examples_dir = self.src_main_java / "lk" / "bandana" / "examples"
        examples_dir.mkdir(parents=True, exist_ok=True)
        
        # Create String Templates example
        string_templates_example = '''package lk.bandana.examples;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Examples of Java 23 String Templates (Preview Feature)
 */
public class StringTemplateExamples {
    
    public static void demonstrateStringTemplates() {
        var userName = "Kasun Perera";
        var age = 28;
        var location = "Colombo, Sri Lanka";
        var timestamp = LocalDateTime.now();
        
        // Basic String Template
        var greeting = STR."Hello \\{userName}, welcome to Bandana!";
        
        // Multi-line String Template
        var profileSummary = STR."""
            Profile Summary:
            Name: \\{userName}
            Age: \\{age} years old
            Location: \\{location}
            Last updated: \\{timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)}
            """;
        
        // String Template with expressions
        var ageCategory = STR."Age Category: \\{age < 25 ? "Young Adult" : age < 35 ? "Adult" : "Mature Adult"}";
        
        // String Template with method calls
        var formattedProfile = STR."""
            \\{userName.toUpperCase()} - \\{location.substring(0, location.indexOf(","))}
            Profile created on: \\{formatDate(timestamp)}
            """;
        
        System.out.println(greeting);
        System.out.println(profileSummary);
        System.out.println(ageCategory);
        System.out.println(formattedProfile);
    }
    
    private static String formatDate(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatter.ofPattern("MMMM dd, yyyy"));
    }
    
    // String Template in record
    public record UserNotification(String userName, String message, LocalDateTime timestamp) {
        public String getFormattedNotification() {
            return STR."""
                📧 Notification for \\{userName}
                Message: \\{message}
                Sent: \\{timestamp.format(DateTimeFormatter.ofPattern("MMM dd, yyyy 'at' HH:mm"))}
                """;
        }
    }
}
'''
        
        with open(examples_dir / "StringTemplateExamples.java", 'w', encoding='utf-8') as f:
            f.write(string_templates_example)
        
        # Create Pattern Matching example
        pattern_matching_example = '''package lk.bandana.examples;

import java.util.List;

/**
 * Examples of Java 23 Pattern Matching features
 */
public class PatternMatchingExamples {
    
    // Sealed interface for user types
    public sealed interface UserType 
        permits RegularUser, PremiumUser, AgentUser {
        
        String getUserId();
    }
    
    public record RegularUser(String userId, int profileViews) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    public record PremiumUser(String userId, int profileViews, List<String> premiumFeatures) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    public record AgentUser(String userId, String agencyName, int clientCount) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    // Pattern matching with switch expressions
    public static String getUserDescription(UserType user) {
        return switch (user) {
            case RegularUser(var id, var views) -> 
                STR."Regular user \\{id} with \\{views} profile views";
            case PremiumUser(var id, var views, var features) -> 
                STR."Premium user \\{id} with \\{views} views and features: \\{String.join(", ", features)}";
            case AgentUser(var id, var agency, var clients) -> 
                STR."Agent \\{id} from \\{agency} managing \\{clients} clients";
        };
    }
    
    // Pattern matching with guards
    public static String getAccessLevel(UserType user) {
        return switch (user) {
            case RegularUser(_, var views) when views > 100 -> "High Activity Regular";
            case RegularUser(_, _) -> "Standard Regular";
            case PremiumUser(_, _, var features) when features.contains("unlimited_views") -> "Premium Unlimited";
            case PremiumUser(_, _, _) -> "Premium Limited";
            case AgentUser(_, _, var clients) when clients > 50 -> "Senior Agent";
            case AgentUser(_, _, _) -> "Junior Agent";
        };
    }
    
    // Unnamed patterns for when we don't need all values
    public static boolean isHighValueUser(UserType user) {
        return switch (user) {
            case RegularUser(_, var views) when views > 200 -> true;
            case PremiumUser(_, _, _) -> true;
            case AgentUser(_, _, var clients) when clients > 20 -> true;
            case _ -> false; // Unnamed pattern for all other cases
        };
    }
    
    // Pattern matching with collections
    public static String analyzeUserList(List<UserType> users) {
        return switch (users) {
            case List<UserType> list when list.isEmpty() -> "No users";
            case List<UserType> list when list.size() == 1 -> 
                STR."Single user: \\{getUserDescription(list.getFirst())}";
            case List<UserType> list when list.size() <= 5 -> 
                STR."Small group of \\{list.size()} users";
            case List<UserType> list -> 
                STR."Large group of \\{list.size()} users";
        };
    }
}
'''
        
        with open(examples_dir / "PatternMatchingExamples.java", 'w', encoding='utf-8') as f:
            f.write(pattern_matching_example)
        
        print("✓ Created Java 23 feature examples")

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python apply-java23-features.py <project_root_path>")
        sys.exit(1)
    
    project_root = sys.argv[1]
    
    if not os.path.exists(project_root):
        print(f"Project root path does not exist: {project_root}")
        sys.exit(1)
    
    modernizer = Java23Modernizer(project_root)
    modernizer.modernize_project()
    
    print("\n🚀 Java 23 modernization completed!")
    print("\nCutting-edge features applied:")
    print("- String Templates (STR processor)")
    print("- Pattern Matching for switch with guards")
    print("- Record Patterns and unnamed patterns")
    print("- Virtual Threads with Structured Concurrency")
    print("- Sealed interfaces with pattern matching")
    print("- Modern error handling patterns")

if __name__ == "__main__":
    main()
