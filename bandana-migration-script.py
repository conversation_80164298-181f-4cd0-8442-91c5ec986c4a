#!/usr/bin/env python3
"""
Bandana Service Migration Script: Neo4j to MongoDB + Lombok to Modern Java
Focused script for the specific transformations needed in the bandana project.
"""

import os
import re
import sys
from pathlib import Path

def migrate_file(file_path):
    """Migrate a single Java file from Neo4j to MongoDB and remove Lombok"""
    print(f"Migrating: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. Update imports
    import_replacements = {
        'import org.springframework.data.neo4j.core.schema.Node;': 'import org.springframework.data.mongodb.core.mapping.Document;',
        'import org.springframework.data.neo4j.core.schema.Relationship;': 'import org.springframework.data.mongodb.core.mapping.DBRef;',
        'import org.springframework.data.neo4j.core.schema.GeneratedValue;': '',
        'import org.springframework.data.neo4j.core.schema.Id;': 'import org.springframework.data.annotation.Id;',
        'import org.springframework.data.neo4j.repository.Neo4jRepository;': 'import org.springframework.data.mongodb.repository.MongoRepository;',
        'import org.springframework.data.neo4j.repository.config.EnableNeo4jRepositories;': 'import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;',
        'import org.springframework.data.neo4j.config.AbstractNeo4jConfig;': '',
        'import org.neo4j.driver.Driver;': ''
    }
    
    for old_import, new_import in import_replacements.items():
        if old_import in content:
            if new_import:
                content = content.replace(old_import, new_import)
            else:
                content = content.replace(old_import + '\n', '')
    
    # 2. Update annotations
    annotation_replacements = {
        '@Node': '@Document',
        '@Relationship': '@DBRef',
        '@GeneratedValue': ''
    }
    
    for old_annotation, new_annotation in annotation_replacements.items():
        if old_annotation in content:
            if new_annotation:
                content = content.replace(old_annotation, new_annotation)
            else:
                content = re.sub(f'{re.escape(old_annotation)}\\s*\n?', '', content)
    
    # 3. Update repository interfaces
    content = re.sub(r'extends Neo4jRepository<(\w+),\s*(\w+)>', r'extends MongoRepository<\1, \2>', content)
    
    # 4. Update configuration annotations
    content = content.replace('@EnableNeo4jRepositories', '@EnableMongoRepositories')
    content = content.replace('@EnableNeo4jAuditing', '@EnableMongoAuditing')
    
    # 5. Remove AbstractNeo4jConfig inheritance
    content = re.sub(r'extends AbstractNeo4jConfig\s*{', '{', content)
    
    # 6. Remove Driver bean method
    driver_method_pattern = r'@Bean\s*public Driver driver\(\)\s*{\s*return null;\s*}'
    content = re.sub(driver_method_pattern, '', content, flags=re.DOTALL)
    
    # 7. Remove Lombok imports
    lombok_imports = [
        'import lombok.*;',
        'import lombok.Getter;',
        'import lombok.Setter;',
        'import lombok.Data;',
        'import lombok.Builder;',
        'import lombok.AllArgsConstructor;',
        'import lombok.NoArgsConstructor;',
        'import lombok.RequiredArgsConstructor;',
        'import lombok.ToString;',
        'import lombok.EqualsAndHashCode;',
        'import lombok.NonNull;'
    ]
    
    for lombok_import in lombok_imports:
        content = content.replace(lombok_import + '\n', '')
    
    # 8. Remove Lombok annotations and add modern Java equivalents
    content = remove_lombok_add_modern_java(content)
    
    # 9. Add necessary imports for MongoDB
    if '@Document' in content and 'import org.springframework.data.mongodb.core.mapping.Document;' not in content:
        content = add_import(content, 'org.springframework.data.mongodb.core.mapping.Document')
    
    if '@DBRef' in content and 'import org.springframework.data.mongodb.core.mapping.DBRef;' not in content:
        content = add_import(content, 'org.springframework.data.mongodb.core.mapping.DBRef')
    
    # Only write if content changed
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ Updated: {file_path}")
    else:
        print(f"  - No changes: {file_path}")

def add_import(content, import_statement):
    """Add an import statement to the file"""
    import_line = f'import {import_statement};\n'
    
    # Find the last import statement
    import_pattern = r'(import .*?;\n)'
    imports = re.findall(import_pattern, content)
    
    if imports:
        last_import = imports[-1]
        content = content.replace(last_import, last_import + import_line)
    else:
        # Add after package statement
        package_pattern = r'(package .*?;\n)'
        content = re.sub(package_pattern, r'\1\n' + import_line, content)
    
    return content

def remove_lombok_add_modern_java(content):
    """Remove Lombok annotations and add modern Java equivalents"""
    
    # Remove Lombok annotations
    lombok_annotations = ['@Data', '@Getter', '@Setter', '@Builder', '@AllArgsConstructor', '@NoArgsConstructor', '@ToString', '@EqualsAndHashCode']
    
    has_getters_setters = '@Getter' in content or '@Setter' in content or '@Data' in content
    
    for annotation in lombok_annotations:
        content = re.sub(f'{re.escape(annotation)}\\s*\n?', '', content)
    
    # If the class had Lombok getters/setters, add them
    if has_getters_setters:
        content = add_getters_setters(content)
    
    return content

def add_getters_setters(content):
    """Add getter and setter methods for private fields"""
    
    # Extract private fields
    field_pattern = r'private\s+(\w+(?:<[^>]+>)?)\s+(\w+);'
    fields = re.findall(field_pattern, content)
    
    if not fields:
        return content
    
    methods = []
    for field_type, field_name in fields:
        # Generate getter
        getter_name = f"get{field_name.capitalize()}"
        if field_type.lower() == 'boolean':
            getter_name = f"is{field_name.capitalize()}"
        
        getter = f"""
    public {field_type} {getter_name}() {{
        return this.{field_name};
    }}"""
        
        # Generate setter
        setter = f"""
    public void set{field_name.capitalize()}({field_type} {field_name}) {{
        this.{field_name} = {field_name};
    }}"""
        
        methods.extend([getter, setter])
    
    # Add methods before the last closing brace
    if methods:
        methods_str = '\n'.join(methods) + '\n'
        content = content.rstrip()
        if content.endswith('}'):
            content = content[:-1] + methods_str + '}'
    
    return content

def update_pom_xml(project_root):
    """Update pom.xml to remove Neo4j dependencies"""
    pom_file = Path(project_root) / "bandana-service" / "pom.xml"
    if not pom_file.exists():
        print("pom.xml not found, skipping dependency update")
        return
    
    print("Updating pom.xml dependencies...")
    
    with open(pom_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove Neo4j dependencies
    neo4j_dependency_pattern = r'<dependency>\s*<groupId>org\.springframework\.boot</groupId>\s*<artifactId>spring-boot-starter-data-neo4j</artifactId>.*?</dependency>'
    content = re.sub(neo4j_dependency_pattern, '', content, flags=re.DOTALL)
    
    # Remove Neo4j driver dependency
    neo4j_driver_pattern = r'<dependency>\s*<groupId>org\.neo4j\.driver</groupId>.*?</dependency>'
    content = re.sub(neo4j_driver_pattern, '', content, flags=re.DOTALL)
    
    with open(pom_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✓ pom.xml updated successfully")

def update_application_properties(project_root):
    """Update application.properties to disable Neo4j and ensure MongoDB config"""
    props_file = Path(project_root) / "bandana-service" / "src" / "main" / "resources" / "application.properties"
    if not props_file.exists():
        print("application.properties not found, skipping")
        return
    
    print("Updating application.properties...")
    
    with open(props_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    new_lines = []
    for line in lines:
        # Comment out Neo4j properties
        if 'spring.neo4j' in line and not line.strip().startswith('#'):
            new_lines.append('#' + line)
        else:
            new_lines.append(line)
    
    # Add MongoDB configuration if not present
    mongodb_config_exists = any('spring.data.mongodb' in line and not line.strip().startswith('#') for line in new_lines)
    if not mongodb_config_exists:
        new_lines.extend([
            '\n# MongoDB Configuration\n',
            'spring.data.mongodb.host=localhost\n',
            'spring.data.mongodb.port=27017\n',
            'spring.data.mongodb.database=bandana\n',
            'spring.data.mongodb.auto-index-creation=true\n'
        ])
    
    with open(props_file, 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print("✓ application.properties updated successfully")

def main():
    """Main migration function"""
    if len(sys.argv) != 2:
        print("Usage: python bandana-migration-script.py <project_root_path>")
        print("Example: python bandana-migration-script.py G:/Projects/bandana")
        sys.exit(1)
    
    project_root = sys.argv[1]
    
    if not os.path.exists(project_root):
        print(f"Project root path does not exist: {project_root}")
        sys.exit(1)
    
    bandana_service = Path(project_root) / "bandana-service"
    if not bandana_service.exists():
        print(f"bandana-service directory not found in: {project_root}")
        sys.exit(1)
    
    print("Starting Bandana Service Migration: Neo4j → MongoDB + Lombok → Modern Java")
    print("=" * 70)
    
    # 1. Update configuration files
    update_pom_xml(project_root)
    update_application_properties(project_root)
    
    # 2. Migrate Java files
    src_main_java = bandana_service / "src" / "main" / "java"
    java_files = list(src_main_java.rglob("*.java"))
    
    print(f"\nMigrating {len(java_files)} Java files...")
    print("-" * 50)
    
    for java_file in java_files:
        migrate_file(java_file)
    
    print("\n" + "=" * 70)
    print("Migration completed successfully!")
    print("\nNext steps:")
    print("1. Review the generated getter/setter methods")
    print("2. Run 'mvn clean compile' to check for compilation errors")
    print("3. Update any custom Neo4j queries to MongoDB equivalents")
    print("4. Test the application with MongoDB")

if __name__ == "__main__":
    main()
