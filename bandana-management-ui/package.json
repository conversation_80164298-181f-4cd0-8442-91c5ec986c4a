{"name": "charityPageUiInternal", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@angular/ssr": "^18.2.0", "primeng": "^18.0.0", "primeicons": "^7.0.0", "primeflex": "^3.3.1", "angular-feather": "^6.5.0", "ngx-paypal": "^18.0.0", "rxjs": "~7.8.0", "tslib": "^2.6.0", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.0", "@angular/cli": "^18.2.0", "@angular/compiler-cli": "^18.2.0", "@types/file-saver": "^2.0.5", "@types/jasmine": "~5.1.0", "@types/node": "^20.14.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.0"}}