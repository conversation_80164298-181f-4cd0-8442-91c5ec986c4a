#!/usr/bin/env python3
"""
Modernize Java code to use latest Java 21 features
- Replace traditional patterns with modern Java equivalents
- Use records for immutable data classes
- Apply pattern matching and switch expressions
- Use text blocks for multi-line strings
- Apply sealed classes where appropriate
- Use var keyword for local variables
"""

import os
import re
from pathlib import Path
from typing import List, Set

class JavaModernizer:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.bandana_service = self.project_root / "bandana-service"
        self.src_main_java = self.bandana_service / "src" / "main" / "java"
        
        # Patterns for modernization
        self.immutable_entity_patterns = [
            'Response', 'MetaData', 'District', 'Province', 'QuestionOption',
            'SystemConfiguration', 'CompatibilityMatch'
        ]

    def modernize_project(self):
        """Main modernization method"""
        print("Starting Java 21 modernization...")
        
        java_files = list(self.src_main_java.rglob("*.java"))
        
        for java_file in java_files:
            print(f"Modernizing: {java_file.relative_to(self.project_root)}")
            self._modernize_single_file(java_file)
        
        print(f"Modernized {len(java_files)} Java files")

    def _modernize_single_file(self, file_path: Path):
        """Modernize a single Java file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 1. Convert appropriate classes to records
            if self._should_convert_to_record(file_path, content):
                content = self._convert_to_record(content, file_path.stem)
            
            # 2. Apply modern Java patterns
            content = self._apply_modern_patterns(content)
            
            # 3. Use text blocks for multi-line strings
            content = self._use_text_blocks(content)
            
            # 4. Apply pattern matching
            content = self._apply_pattern_matching(content)
            
            # 5. Use var for local variables
            content = self._use_var_keyword(content)
            
            # 6. Modernize exception handling
            content = self._modernize_exception_handling(content)
            
            # 7. Use modern collection methods
            content = self._modernize_collections(content)
            
            # Only write if content changed
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✓ Modernized: {file_path.name}")
            else:
                print(f"  - No changes: {file_path.name}")
        
        except Exception as e:
            print(f"  ✗ Error modernizing {file_path}: {e}")

    def _should_convert_to_record(self, file_path: Path, content: str) -> bool:
        """Determine if a class should be converted to a record"""
        # Check if it's an entity class that should remain mutable
        if '@Document' in content or '@Entity' in content:
            return False
        
        # Check if it's in the immutable patterns list
        class_name = file_path.stem
        if class_name in self.immutable_entity_patterns:
            return True
        
        # Check if it's a simple data class with only getters
        has_setters = re.search(r'public void set\w+\(', content)
        has_getters = re.search(r'public \w+ get\w+\(\)', content)
        has_business_logic = re.search(r'public \w+ (?!get|set|is)\w+\([^)]*\)\s*{[^}]*[^}]+}', content)
        
        return has_getters and not has_setters and not has_business_logic

    def _convert_to_record(self, content: str, class_name: str) -> str:
        """Convert a class to a record"""
        # Extract fields
        field_pattern = r'private\s+(?:final\s+)?(\w+(?:<[^>]+>)?)\s+(\w+);'
        fields = re.findall(field_pattern, content)
        
        if not fields:
            return content
        
        # Create record parameters
        record_params = ', '.join([f"{field_type} {field_name}" for field_type, field_name in fields])
        
        # Extract package and imports
        package_match = re.search(r'package ([^;]+);', content)
        package_line = package_match.group(0) if package_match else ""
        
        import_pattern = r'import [^;]+;'
        imports = re.findall(import_pattern, content)
        imports_section = '\n'.join(imports)
        
        # Extract class-level annotations
        annotation_pattern = r'@\w+(?:\([^)]*\))?'
        class_start = re.search(r'public class ' + class_name, content)
        if class_start:
            before_class = content[:class_start.start()]
            annotations = re.findall(annotation_pattern, before_class)
            # Filter out Lombok annotations
            annotations = [ann for ann in annotations if not any(lombok in ann for lombok in ['@Data', '@Getter', '@Setter', '@Builder'])]
            annotations_section = '\n'.join(annotations)
        else:
            annotations_section = ""
        
        # Create record
        record_content = f"""{package_line}

{imports_section}

/**
 * {class_name} record - immutable data class
 * Converted to record for modern Java 21 features
 */
{annotations_section}
public record {class_name}({record_params}) {{
    
    // Custom validation constructor
    public {class_name} {{
        // Add validation logic here if needed
    }}
    
    // Custom methods can be added here
}}
"""
        
        return record_content

    def _apply_modern_patterns(self, content: str) -> str:
        """Apply modern Java patterns"""
        
        # Replace instanceof with pattern matching (Java 16+)
        instanceof_pattern = r'if\s*\(\s*(\w+)\s+instanceof\s+(\w+)\s*\)\s*\{\s*(\w+)\s+(\w+)\s*=\s*\(\2\)\s*\1;'
        content = re.sub(instanceof_pattern, r'if (\1 instanceof \2 \4) {', content)
        
        # Use switch expressions where appropriate
        content = self._modernize_switch_statements(content)
        
        return content

    def _modernize_switch_statements(self, content: str) -> str:
        """Convert traditional switch statements to modern switch expressions"""
        
        # Pattern for simple switch statements that can be converted to expressions
        switch_pattern = r'switch\s*\(\s*(\w+)\s*\)\s*\{\s*((?:case\s+[^:]+:\s*return\s+[^;]+;\s*)+)default:\s*return\s+([^;]+);\s*\}'
        
        def replace_switch(match):
            variable = match.group(1)
            cases = match.group(2)
            default_value = match.group(3)
            
            # Convert cases to modern format
            case_lines = []
            for case_match in re.finditer(r'case\s+([^:]+):\s*return\s+([^;]+);', cases):
                case_value = case_match.group(1)
                return_value = case_match.group(2)
                case_lines.append(f"            case {case_value} -> {return_value}")
            
            cases_str = '\n'.join(case_lines)
            
            return f"""switch ({variable}) {{
{cases_str}
            default -> {default_value}
        }}"""
        
        content = re.sub(switch_pattern, replace_switch, content, flags=re.DOTALL)
        
        return content

    def _use_text_blocks(self, content: str) -> str:
        """Replace multi-line string concatenations with text blocks"""
        
        # Pattern for multi-line string concatenations
        multiline_string_pattern = r'"([^"]*)"(\s*\+\s*\n\s*"[^"]*")+;'
        
        def replace_with_text_block(match):
            full_match = match.group(0)
            # Extract all string parts
            string_parts = re.findall(r'"([^"]*)"', full_match)
            combined_string = '\n'.join(string_parts)
            
            # Only convert if it's actually multi-line and substantial
            if len(string_parts) > 2 and len(combined_string) > 50:
                return f'"""\n{combined_string}\n""";'
            else:
                return full_match
        
        content = re.sub(multiline_string_pattern, replace_with_text_block, content, flags=re.DOTALL)
        
        return content

    def _apply_pattern_matching(self, content: str) -> str:
        """Apply pattern matching where appropriate"""
        
        # Convert complex if-else chains to pattern matching
        # This is a simplified example - real implementation would be more sophisticated
        
        return content

    def _use_var_keyword(self, content: str) -> str:
        """Use var keyword for local variable declarations where type is obvious"""
        
        # Pattern for obvious type declarations
        patterns = [
            (r'(\s+)List<(\w+)>\s+(\w+)\s*=\s*new ArrayList<\2>\(\);', r'\1var \3 = new ArrayList<\2>();'),
            (r'(\s+)Map<(\w+),\s*(\w+)>\s+(\w+)\s*=\s*new HashMap<\2,\s*\3>\(\);', r'\1var \4 = new HashMap<\2, \3>();'),
            (r'(\s+)Set<(\w+)>\s+(\w+)\s*=\s*new HashSet<\2>\(\);', r'\1var \3 = new HashSet<\2>();'),
            (r'(\s+)StringBuilder\s+(\w+)\s*=\s*new StringBuilder\(\);', r'\1var \2 = new StringBuilder();'),
            (r'(\s+)Optional<(\w+)>\s+(\w+)\s*=\s*Optional\.', r'\1var \3 = Optional.'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        return content

    def _modernize_exception_handling(self, content: str) -> str:
        """Modernize exception handling patterns"""
        
        # Use multi-catch where appropriate
        multi_catch_pattern = r'catch\s*\(\s*(\w+Exception)\s+(\w+)\s*\)\s*\{([^}]+)\}\s*catch\s*\(\s*(\w+Exception)\s+\w+\s*\)\s*\{([^}]+)\}'
        
        def replace_multi_catch(match):
            exception1 = match.group(1)
            var_name = match.group(2)
            body1 = match.group(3).strip()
            exception2 = match.group(4)
            body2 = match.group(5).strip()
            
            # Only combine if the bodies are similar
            if body1 == body2:
                return f'catch ({exception1} | {exception2} {var_name}) {{\n{body1}\n}}'
            else:
                return match.group(0)
        
        content = re.sub(multi_catch_pattern, replace_multi_catch, content, flags=re.DOTALL)
        
        return content

    def _modernize_collections(self, content: str) -> str:
        """Use modern collection methods and patterns"""
        
        # Replace traditional loops with stream operations where appropriate
        # This is a simplified example
        
        # Convert simple for-each loops to streams
        foreach_pattern = r'for\s*\(\s*(\w+)\s+(\w+)\s*:\s*(\w+)\s*\)\s*\{\s*if\s*\([^)]+\)\s*\{\s*(\w+)\.add\([^)]+\);\s*\}\s*\}'
        
        return content


def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python modernize-java-code.py <project_root_path>")
        sys.exit(1)
    
    project_root = sys.argv[1]
    
    if not os.path.exists(project_root):
        print(f"Project root path does not exist: {project_root}")
        sys.exit(1)
    
    modernizer = JavaModernizer(project_root)
    modernizer.modernize_project()
    print("Java modernization completed!")


if __name__ == "__main__":
    main()
