#!/usr/bin/env python3
"""
Complete Lombok removal and modern Java conversion
Ensures no Lombok dependencies remain and all code uses modern Java features
"""

import os
import re
from pathlib import Path

def scan_for_lombok_remnants(project_root):
    """Scan for any remaining Lombok imports or annotations"""
    bandana_service = Path(project_root) / "bandana-service"
    src_main_java = bandana_service / "src" / "main" / "java"
    
    java_files = list(src_main_java.rglob("*.java"))
    lombok_files = []
    
    lombok_patterns = [
        r'import lombok\.',
        r'@Data\b',
        r'@Getter\b',
        r'@Setter\b',
        r'@Builder\b',
        r'@AllArgsConstructor\b',
        r'@NoArgsConstructor\b',
        r'@RequiredArgsConstructor\b',
        r'@ToString\b',
        r'@EqualsAndHashCode\b',
        r'@Value\b',
        r'@NonNull\b'
    ]
    
    for java_file in java_files:
        try:
            with open(java_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for pattern in lombok_patterns:
                if re.search(pattern, content):
                    lombok_files.append((java_file, pattern))
                    break
        
        except Exception as e:
            print(f"Error scanning {java_file}: {e}")
    
    return lombok_files

def remove_lombok_from_file(file_path):
    """Remove Lombok from a specific file and add modern Java equivalents"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Remove Lombok imports
        lombok_import_pattern = r'import lombok\.[^;]+;\s*\n?'
        content = re.sub(lombok_import_pattern, '', content)
        
        # Remove Lombok annotations
        lombok_annotations = [
            r'@Data\s*\n?',
            r'@Getter\s*\n?',
            r'@Setter\s*\n?',
            r'@Builder\s*\n?',
            r'@AllArgsConstructor\s*\n?',
            r'@NoArgsConstructor\s*\n?',
            r'@RequiredArgsConstructor\s*\n?',
            r'@ToString\s*\n?',
            r'@EqualsAndHashCode\s*\n?',
            r'@Value\s*\n?',
            r'@NonNull\s*\n?'
        ]
        
        for annotation_pattern in lombok_annotations:
            content = re.sub(annotation_pattern, '', content)
        
        # Add modern Java equivalents if needed
        content = ensure_modern_java_methods(content, file_path)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Cleaned Lombok from: {file_path.name}")
        
    except Exception as e:
        print(f"✗ Error cleaning {file_path}: {e}")

def ensure_modern_java_methods(content, file_path):
    """Ensure the class has proper modern Java methods"""
    
    # Check if it's an entity class that needs getters/setters
    if '@Document' in content or '@Entity' in content:
        # Extract fields
        field_pattern = r'private\s+(\w+(?:<[^>]+>)?)\s+(\w+);'
        fields = re.findall(field_pattern, content)
        
        if fields and not has_getters_setters(content):
            content = add_modern_getters_setters(content, fields)
    
    return content

def has_getters_setters(content):
    """Check if the class already has getter/setter methods"""
    return bool(re.search(r'public \w+ get\w+\(\)', content)) or bool(re.search(r'public void set\w+\(', content))

def add_modern_getters_setters(content, fields):
    """Add modern getter/setter methods"""
    methods = []
    
    for field_type, field_name in fields:
        # Generate getter
        getter_name = f"get{field_name.capitalize()}"
        if field_type.lower() == 'boolean':
            getter_name = f"is{field_name.capitalize()}"
        
        getter = f"""
    public {field_type} {getter_name}() {{
        return this.{field_name};
    }}"""
        
        # Generate setter
        setter = f"""
    public void set{field_name.capitalize()}({field_type} {field_name}) {{
        this.{field_name} = {field_name};
    }}"""
        
        methods.extend([getter, setter])
    
    # Add methods before the last closing brace
    if methods:
        methods_str = '\n'.join(methods) + '\n'
        content = content.rstrip()
        if content.endswith('}'):
            content = content[:-1] + methods_str + '}'
    
    return content

def modernize_entity_classes():
    """Apply modern Java patterns to entity classes"""
    entity_files = [
        "bandana-service/src/main/java/lk/bandana/core/entity/User.java",
        "bandana-service/src/main/java/lk/bandana/core/entity/UserProfile.java",
        "bandana-service/src/main/java/lk/bandana/core/entity/MetaData.java",
        "bandana-service/src/main/java/lk/bandana/core/entity/District.java",
        "bandana-service/src/main/java/lk/bandana/core/entity/Province.java"
    ]
    
    for entity_file in entity_files:
        entity_path = Path(entity_file)
        if entity_path.exists():
            modernize_single_entity(entity_path)

def modernize_single_entity(file_path):
    """Modernize a single entity class"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Add modern validation using Bean Validation
        if '@Document' in content and 'import jakarta.validation' not in content:
            content = add_validation_imports(content)
        
        # Add modern toString using text blocks if appropriate
        if 'toString' not in content:
            content = add_modern_toString(content, file_path.stem)
        
        # Add modern equals/hashCode using Objects.hash
        if 'equals' not in content:
            content = add_modern_equals_hashcode(content, file_path.stem)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Modernized entity: {file_path.name}")
    
    except Exception as e:
        print(f"✗ Error modernizing {file_path}: {e}")

def add_validation_imports(content):
    """Add modern validation imports"""
    validation_imports = '''import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
'''
    
    # Add after existing imports
    import_pattern = r'(import [^;]+;\n)(?=\n|public)'
    content = re.sub(import_pattern, r'\1' + validation_imports + '\n', content, count=1)
    
    return content

def add_modern_toString(content, class_name):
    """Add modern toString method using text blocks"""
    # Extract fields for toString
    field_pattern = r'private\s+(?:final\s+)?(\w+(?:<[^>]+>)?)\s+(\w+);'
    fields = re.findall(field_pattern, content)
    
    if not fields:
        return content
    
    # Create field representations using modern string formatting
    field_representations = []
    for _, field_name in fields:
        field_representations.append(f'"{field_name}=" + {field_name}')
    
    field_str = ' + ", " + '.join(field_representations)
    
    toString_method = f'''
    @Override
    public String toString() {{
        return \"\"\"{class_name}{{
            {field_str}
        }}\"\"\";
    }}'''
    
    # Add method before the last closing brace
    content = content.rstrip()
    if content.endswith('}'):
        content = content[:-1] + toString_method + '\n}'
    
    return content

def add_modern_equals_hashcode(content, class_name):
    """Add modern equals and hashCode methods"""
    # Extract fields
    field_pattern = r'private\s+(?:final\s+)?(\w+(?:<[^>]+>)?)\s+(\w+);'
    fields = re.findall(field_pattern, content)
    
    if not fields:
        return content
    
    # Generate equals method with pattern matching
    field_comparisons = []
    for field_type, field_name in fields:
        if field_type in ['int', 'long', 'double', 'float', 'boolean', 'byte', 'short', 'char']:
            field_comparisons.append(f'{field_name} == other.{field_name}')
        else:
            field_comparisons.append(f'Objects.equals({field_name}, other.{field_name})')
    
    comparison_str = ' && '.join(field_comparisons)
    
    equals_method = f'''
    @Override
    public boolean equals(Object obj) {{
        return switch (obj) {{
            case null -> false;
            case {class_name} other when this == other -> true;
            case {class_name} other -> {comparison_str};
            default -> false;
        }};
    }}'''
    
    # Generate hashCode method
    hash_fields = [field_name for _, field_name in fields]
    hash_str = ', '.join(hash_fields)
    
    hashcode_method = f'''
    @Override
    public int hashCode() {{
        return Objects.hash({hash_str});
    }}'''
    
    # Add Objects import if needed
    if 'import java.util.Objects;' not in content:
        content = add_objects_import(content)
    
    methods = equals_method + hashcode_method
    
    # Add methods before the last closing brace
    content = content.rstrip()
    if content.endswith('}'):
        content = content[:-1] + methods + '\n}'
    
    return content

def add_objects_import(content):
    """Add Objects import"""
    objects_import = 'import java.util.Objects;\n'
    
    # Add after existing imports
    import_pattern = r'(import [^;]+;\n)(?=\n|public)'
    content = re.sub(import_pattern, r'\1' + objects_import, content, count=1)
    
    return content

def create_modern_validation_example():
    """Create an example of modern validation patterns"""
    validation_example = '''package lk.bandana.core.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * Modern validation example using Java 21 features
 */
public class SriLankanPhoneValidator implements ConstraintValidator<SriLankanPhone, String> {
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^(?:\\+94|0)?[1-9]\\d{8}$"
    );
    
    @Override
    public boolean isValid(String phone, ConstraintValidatorContext context) {
        return switch (phone) {
            case null -> true; // Let @NotNull handle null validation
            case String p when p.isBlank() -> false;
            case String p -> PHONE_PATTERN.matcher(p).matches();
        };
    }
}
'''
    
    validation_dir = Path("bandana-service/src/main/java/lk/bandana/core/validation")
    validation_dir.mkdir(parents=True, exist_ok=True)
    
    with open(validation_dir / "SriLankanPhoneValidator.java", 'w', encoding='utf-8') as f:
        f.write(validation_example)
    
    print("✓ Created modern validation example")

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python remove-lombok-completely.py <project_root_path>")
        sys.exit(1)
    
    project_root = sys.argv[1]
    
    print("Scanning for Lombok remnants...")
    lombok_files = scan_for_lombok_remnants(project_root)
    
    if lombok_files:
        print(f"Found {len(lombok_files)} files with Lombok remnants:")
        for file_path, pattern in lombok_files:
            print(f"  - {file_path.name}: {pattern}")
        
        print("\nRemoving Lombok and adding modern Java equivalents...")
        for file_path, _ in lombok_files:
            remove_lombok_from_file(file_path)
    else:
        print("✓ No Lombok remnants found!")
    
    print("\nModernizing entity classes...")
    modernize_entity_classes()
    
    print("\nCreating modern validation examples...")
    create_modern_validation_example()
    
    print("\n✅ Lombok removal and Java 21 modernization completed!")
    print("\nModern Java features now in use:")
    print("- Records for immutable data")
    print("- Pattern matching in switch expressions")
    print("- Text blocks for multi-line strings")
    print("- Modern validation with Bean Validation")
    print("- Objects.hash() for hashCode implementation")
    print("- Modern equals() with pattern matching")

if __name__ == "__main__":
    main()
