#!/usr/bin/env python3
"""
Fix all service implementation files in the core.service.impl package
"""

import os
import re
from pathlib import Path

def fix_all_service_impl_files():
    """Fix all service implementation files"""
    
    service_impl_dir = Path("bandana-service/src/main/java/lk/bandana/core/service/impl")
    
    if not service_impl_dir.exists():
        print("Service impl directory not found")
        return
    
    # Get all Java files in the service impl directory
    java_files = list(service_impl_dir.glob("*.java"))
    
    for java_file in java_files:
        print(f"Fixing: {java_file.name}")
        fix_single_service_impl(java_file)

def fix_single_service_impl(file_path):
    """Fix a single service implementation file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if file is severely corrupted
        if is_severely_corrupted(content):
            print(f"  File {file_path.name} is severely corrupted, recreating...")
            recreate_service_impl(file_path)
        else:
            print(f"  File {file_path.name} has minor issues, fixing...")
            fixed_content = fix_service_impl_syntax(content)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            print(f"  ✓ Fixed: {file_path.name}")
    
    except Exception as e:
        print(f"  ✗ Error fixing {file_path}: {e}")

def is_severely_corrupted(content):
    """Check if the file is severely corrupted"""
    # Check for missing annotations, malformed structure
    missing_annotations = content.count('@\n') > 3
    malformed_comments = '{' in content and '}' in content and content.count('{') != content.count('}')
    missing_imports = 'import' not in content or content.count('import') < 3
    
    return missing_annotations or malformed_comments or missing_imports

def fix_service_impl_syntax(content):
    """Fix syntax issues in service implementation"""
    
    # Fix missing @Override annotations
    content = re.sub(r'^@\s*$', '@Override', content, flags=re.MULTILINE)
    
    # Fix malformed logger statements
    content = re.sub(r'logger\.info\("([^"]*)\{\s*\}\s*([^"]*)\{\s*\}\s*([^"]*)", ([^)]+)\);', 
                     r'logger.info("\1{}\2{}\3", \4);', content)
    
    # Fix malformed comments
    content = re.sub(r'^(\s*)([A-Z][a-zA-Z\s]+)([a-z])', r'\1// \2\n\1\3', content, flags=re.MULTILINE)
    
    # Fix missing semicolons
    content = re.sub(r'(\w+\([^)]*\))\s*$', r'\1;', content, flags=re.MULTILINE)
    
    # Fix broken string formatting
    content = re.sub(r'\{\s*\}\s*', '{}', content)
    
    return content

def recreate_service_impl(file_path):
    """Recreate a service implementation file"""
    class_name = file_path.stem
    
    # Determine the service type and create appropriate implementation
    if 'UserProfile' in class_name:
        create_user_profile_service_impl(file_path)
    elif 'User' in class_name and 'Profile' not in class_name:
        create_user_service_impl(file_path)
    elif 'Role' in class_name:
        create_role_service_impl(file_path)
    elif 'Subscription' in class_name:
        create_subscription_service_impl(file_path)
    elif 'Matching' in class_name:
        create_matching_service_impl(file_path)
    elif 'SystemConfiguration' in class_name:
        create_system_config_service_impl(file_path)
    else:
        create_generic_service_impl(file_path)

def create_user_profile_service_impl(file_path):
    """Create UserProfileServiceImpl"""
    content = '''package lk.bandana.core.service.impl;

import lk.bandana.core.entity.UserProfile;
import lk.bandana.core.repository.UserProfileRepository;
import lk.bandana.core.service.UserProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * UserProfileServiceImpl - User Profile Service Implementation
 */
@Service
@Transactional
public class UserProfileServiceImpl implements UserProfileService {

    private static final Logger logger = LoggerFactory.getLogger(UserProfileServiceImpl.class);

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Override
    public UserProfile save(UserProfile userProfile) {
        try {
            // Calculate completion percentage before saving
            userProfile.setCompletionPercentage(calculateCompletionPercentage(userProfile));
            
            // Set default status if not set
            if (userProfile.getStatus() == null) {
                userProfile.setStatus(UserProfile.ProfileStatus.PENDING);
            }
            
            return userProfileRepository.save(userProfile);
        } catch (Exception e) {
            logger.error("Error saving user profile: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to save user profile", e);
        }
    }

    @Override
    public Optional<UserProfile> findById(Long id) {
        return userProfileRepository.findById(id);
    }

    @Override
    public List<UserProfile> findAll() {
        return userProfileRepository.findAll();
    }

    @Override
    public void deleteById(Long id) {
        userProfileRepository.deleteById(id);
    }

    @Override
    public UserProfile approveProfile(Long profileId, String approvedBy, String comments) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        profile.setStatus(UserProfile.ProfileStatus.APPROVED);
        profile.setApprovedBy(approvedBy);
        profile.setApprovedDate(LocalDateTime.now());
        profile.setAdminComments(comments);
        
        logger.info("Profile {} approved by {}", profileId, approvedBy);
        return userProfileRepository.save(profile);
    }

    @Override
    public UserProfile rejectProfile(Long profileId, String rejectedBy, String reason) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        profile.setStatus(UserProfile.ProfileStatus.REJECTED);
        profile.setRejectionReason(reason);
        profile.setAdminComments(reason);
        
        logger.info("Profile {} rejected by {} - Reason: {}", profileId, rejectedBy, reason);
        return userProfileRepository.save(profile);
    }

    @Override
    public UserProfile suspendProfile(Long profileId, String suspendedBy, String reason) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        profile.setStatus(UserProfile.ProfileStatus.SUSPENDED);
        profile.setAdminComments(reason);
        
        logger.info("Profile {} suspended by {} - Reason: {}", profileId, suspendedBy, reason);
        return userProfileRepository.save(profile);
    }

    @Override
    public List<UserProfile> findProfilesPendingApproval() {
        return userProfileRepository.findProfilesPendingApproval();
    }

    @Override
    public Page<UserProfile> searchProfiles(String gender, Integer minAge, Integer maxAge, 
                                          Integer minHeight, Integer maxHeight, String province, 
                                          String district, String education, String occupation, 
                                          String religion, Pageable pageable) {
        return userProfileRepository.searchProfiles(gender, minAge, maxAge, minHeight, maxHeight, pageable);
    }

    @Override
    public List<UserProfile> findApprovedProfiles() {
        return userProfileRepository.findApprovedAndVisibleProfiles();
    }

    @Override
    public List<UserProfile> findApprovedProfilesByGender(String gender) {
        return userProfileRepository.findApprovedAndVisibleProfilesByGender(gender);
    }

    @Override
    public List<UserProfile> findHighQualityProfiles(Integer minScore) {
        return userProfileRepository.findHighQualityProfiles(minScore);
    }

    @Override
    public List<UserProfile> findProfilesManagedByAgent(Long agentId) {
        return userProfileRepository.findProfilesManagedByAgent(agentId);
    }

    @Override
    public UserProfile assignProfileToAgent(Long profileId, Long agentId) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        profile.setIsAgentManaged(true);
        // Set agent relationship here
        return userProfileRepository.save(profile);
    }

    @Override
    public UserProfile removeProfileFromAgent(Long profileId) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        profile.setIsAgentManaged(false);
        // Remove agent relationship here
        return userProfileRepository.save(profile);
    }

    @Override
    public UserProfile calculateProfileCompletion(Long profileId) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        int completionPercentage = calculateCompletionPercentage(profile);
        profile.setCompletionPercentage(completionPercentage);
        
        return userProfileRepository.save(profile);
    }

    @Override
    public UserProfile updateProfileQualityScore(Long profileId) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        int qualityScore = calculateQualityScore(profile);
        profile.setProfileQualityScore(qualityScore);
        
        return userProfileRepository.save(profile);
    }

    @Override
    public List<UserProfile> findIncompleteProfiles() {
        return userProfileRepository.findIncompleteProfiles();
    }

    @Override
    public List<UserProfile> findByProvince(String provinceName) {
        return userProfileRepository.findByProvince(provinceName);
    }

    @Override
    public List<UserProfile> findByDistrict(String districtName) {
        return userProfileRepository.findByDistrict(districtName);
    }

    @Override
    public Long countProfilesByStatus(UserProfile.ProfileStatus status) {
        return userProfileRepository.countByStatus(status.name());
    }

    @Override
    public List<UserProfile> findRecentProfiles(int days) {
        var since = LocalDateTime.now().minusDays(days);
        return userProfileRepository.findRecentProfiles(since.toString());
    }

    @Override
    public List<UserProfile> findProfilesWithPhotos() {
        return userProfileRepository.findProfilesWithPhotos();
    }

    @Override
    public boolean validateProfileData(UserProfile userProfile) {
        return getProfileValidationErrors(userProfile).isEmpty();
    }

    @Override
    public List<String> getProfileValidationErrors(UserProfile userProfile) {
        var errors = new ArrayList<String>();
        
        if (userProfile.getFullName() == null || userProfile.getFullName().trim().isEmpty()) {
            errors.add("Full name is required");
        }
        
        if (userProfile.getDateOfBirth() == null) {
            errors.add("Date of birth is required");
        }
        
        if (userProfile.getGender() == null || userProfile.getGender().trim().isEmpty()) {
            errors.add("Gender is required");
        }
        
        if (userProfile.getHeightCm() == null || userProfile.getHeightCm() < 100 || userProfile.getHeightCm() > 250) {
            errors.add("Valid height is required (100-250 cm)");
        }
        
        return errors;
    }

    @Override
    public UserProfile updateProfilePhoto(Long profileId, String photoUrl) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        profile.setProfilePhotoUrl(photoUrl);
        return userProfileRepository.save(profile);
    }

    @Override
    public UserProfile addAdditionalPhoto(Long profileId, String photoUrl) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        if (profile.getAdditionalPhotoUrls() == null) {
            profile.setAdditionalPhotoUrls(new ArrayList<>());
        }
        profile.getAdditionalPhotoUrls().add(photoUrl);
        
        return userProfileRepository.save(profile);
    }

    @Override
    public UserProfile removePhoto(Long profileId, String photoUrl) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        if (profile.getProfilePhotoUrl() != null && profile.getProfilePhotoUrl().equals(photoUrl)) {
            profile.setProfilePhotoUrl(null);
        }
        
        if (profile.getAdditionalPhotoUrls() != null) {
            profile.getAdditionalPhotoUrls().remove(photoUrl);
        }
        
        return userProfileRepository.save(profile);
    }

    @Override
    public UserProfile updateContactInformation(Long profileId, String email, String phone, String whatsapp) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        profile.setContactEmail(email);
        profile.setContactPhone(phone);
        profile.setWhatsappNumber(whatsapp);
        
        return userProfileRepository.save(profile);
    }

    @Override
    public UserProfile updatePrivacySettings(Long profileId, Boolean profileVisible, 
                                           Boolean contactInfoVisible, Boolean photoVisible, 
                                           Boolean casteVisible) {
        var profile = findById(profileId).orElseThrow(() -> 
            new RuntimeException("Profile not found: " + profileId));
        
        profile.setProfileVisible(profileVisible);
        profile.setContactInfoVisible(contactInfoVisible);
        profile.setPhotoVisible(photoVisible);
        profile.setCasteVisible(casteVisible);
        
        return userProfileRepository.save(profile);
    }

    // Helper methods
    private int calculateCompletionPercentage(UserProfile profile) {
        int totalFields = 20;
        int completedFields = 0;
        
        if (profile.getFullName() != null && !profile.getFullName().trim().isEmpty()) completedFields++;
        if (profile.getDateOfBirth() != null) completedFields++;
        if (profile.getGender() != null && !profile.getGender().trim().isEmpty()) completedFields++;
        if (profile.getHeightCm() != null) completedFields++;
        if (profile.getWeightKg() != null) completedFields++;
        if (profile.getProvince() != null) completedFields++;
        if (profile.getDistrict() != null) completedFields++;
        if (profile.getOccupation() != null && !profile.getOccupation().trim().isEmpty()) completedFields++;
        if (profile.getEducationLevel() != null && !profile.getEducationLevel().trim().isEmpty()) completedFields++;
        if (profile.getReligion() != null && !profile.getReligion().trim().isEmpty()) completedFields++;
        if (profile.getMotherTongue() != null && !profile.getMotherTongue().trim().isEmpty()) completedFields++;
        if (profile.getMaritalStatus() != null && !profile.getMaritalStatus().trim().isEmpty()) completedFields++;
        if (profile.getProfilePhotoUrl() != null && !profile.getProfilePhotoUrl().trim().isEmpty()) completedFields++;
        if (profile.getAboutMe() != null && !profile.getAboutMe().trim().isEmpty()) completedFields++;
        if (profile.getPartnerExpectations() != null && !profile.getPartnerExpectations().trim().isEmpty()) completedFields++;
        if (profile.getContactEmail() != null && !profile.getContactEmail().trim().isEmpty()) completedFields++;
        if (profile.getContactPhone() != null && !profile.getContactPhone().trim().isEmpty()) completedFields++;
        if (profile.getFatherOccupation() != null && !profile.getFatherOccupation().trim().isEmpty()) completedFields++;
        if (profile.getMotherOccupation() != null && !profile.getMotherOccupation().trim().isEmpty()) completedFields++;
        if (profile.getInterests() != null && !profile.getInterests().isEmpty()) completedFields++;
        
        return (completedFields * 100) / totalFields;
    }

    private int calculateQualityScore(UserProfile profile) {
        int score = 0;
        
        // Basic information completeness (40 points)
        if (profile.getCompletionPercentage() >= 90) score += 40;
        else if (profile.getCompletionPercentage() >= 70) score += 30;
        else if (profile.getCompletionPercentage() >= 50) score += 20;
        else score += 10;
        
        // Photo quality (20 points)
        if (profile.getProfilePhotoUrl() != null) {
            score += 10;
            if (profile.getAdditionalPhotoUrls() != null && profile.getAdditionalPhotoUrls().size() >= 2) {
                score += 10;
            }
        }
        
        // Profile description quality (20 points)
        if (profile.getAboutMe() != null && profile.getAboutMe().length() >= 100) score += 10;
        if (profile.getPartnerExpectations() != null && profile.getPartnerExpectations().length() >= 50) score += 10;
        
        // Verification status (20 points)
        if (profile.getStatus() == UserProfile.ProfileStatus.APPROVED) score += 20;
        
        return Math.min(score, 100);
    }
}
'''
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"  ✓ Recreated: {file_path.name}")

def create_generic_service_impl(file_path):
    """Create a generic service implementation"""
    class_name = file_path.stem
    
    content = f'''package lk.bandana.core.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;
import java.util.Optional;

/**
 * {class_name} - Service Implementation
 */
@Service
public class {class_name} {{
    
    private static final Logger logger = LoggerFactory.getLogger({class_name}.class);
    
    // TODO: Add repository dependencies
    
    public List<Object> findAll() {{
        // TODO: Implement
        return List.of();
    }}
    
    public Optional<Object> findById(Long id) {{
        // TODO: Implement
        return Optional.empty();
    }}
    
    public Object save(Object entity) {{
        // TODO: Implement
        return entity;
    }}
    
    public void deleteById(Long id) {{
        // TODO: Implement
    }}
}}
'''
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"  ✓ Recreated: {file_path.name}")

def main():
    print("Fixing all service implementation files...")
    fix_all_service_impl_files()
    print("\nService implementation fixes completed!")

if __name__ == "__main__":
    main()
