#!/usr/bin/env python3
"""
Fix corrupted Java files that were damaged during the migration process
"""

import os
import re
from pathlib import Path

def fix_corrupted_files(project_root):
    """Fix Java files that have been corrupted (all content on one line)"""
    bandana_service = Path(project_root) / "bandana-service"
    src_main_java = bandana_service / "src" / "main" / "java"
    
    java_files = list(src_main_java.rglob("*.java"))
    
    corrupted_files = []
    
    for java_file in java_files:
        try:
            with open(java_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Check if file is corrupted (very few lines but very long lines)
            if len(lines) <= 5:  # Very few lines
                total_chars = sum(len(line) for line in lines)
                if total_chars > 1000:  # But lots of content
                    corrupted_files.append(java_file)
                    print(f"Found corrupted file: {java_file}")
        
        except Exception as e:
            print(f"Error checking {java_file}: {e}")
    
    print(f"\nFound {len(corrupted_files)} corrupted files")
    
    for corrupted_file in corrupted_files:
        fix_single_file(corrupted_file)

def fix_single_file(file_path):
    """Fix a single corrupted Java file"""
    print(f"Fixing: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix the content by adding proper line breaks
        fixed_content = fix_java_formatting(content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"  ✓ Fixed: {file_path}")
    
    except Exception as e:
        print(f"  ✗ Error fixing {file_path}: {e}")

def fix_java_formatting(content):
    """Fix Java code formatting by adding proper line breaks"""
    
    # Remove any existing line breaks first
    content = content.replace('\n', ' ').replace('\r', ' ')
    
    # Add line breaks after common Java patterns
    patterns = [
        (r';\s*', ';\n'),  # After semicolons
        (r'{\s*', '{\n'),  # After opening braces
        (r'}\s*', '}\n'),  # After closing braces
        (r'import\s+', '\nimport '),  # Before imports
        (r'package\s+', '\npackage '),  # Before package
        (r'@\w+\s*', '\n@'),  # Before annotations
        (r'public\s+class\s+', '\npublic class '),  # Before class declarations
        (r'public\s+interface\s+', '\npublic interface '),  # Before interface declarations
        (r'private\s+', '\n    private '),  # Before private fields/methods
        (r'public\s+', '\n    public '),  # Before public methods
        (r'protected\s+', '\n    protected '),  # Before protected methods
        (r'/\*\*', '\n    /**'),  # Before javadoc comments
        (r'\*/\s*', '*/\n'),  # After javadoc comments
        (r'//.*?(?=\s*[a-zA-Z@])', '\n'),  # After single line comments
        (r'try\s*{', '\n        try {'),  # Before try blocks
        (r'catch\s*\(', '\n        } catch ('),  # Before catch blocks
        (r'if\s*\(', '\n        if ('),  # Before if statements
        (r'for\s*\(', '\n        for ('),  # Before for loops
        (r'while\s*\(', '\n        while ('),  # Before while loops
        (r'return\s+', '\n            return '),  # Before return statements
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    # Clean up multiple consecutive newlines
    content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
    
    # Fix indentation
    content = fix_indentation(content)
    
    # Ensure file starts clean
    content = content.strip() + '\n'
    
    return content

def fix_indentation(content):
    """Fix basic Java indentation"""
    lines = content.split('\n')
    fixed_lines = []
    indent_level = 0
    
    for line in lines:
        line = line.strip()
        
        if not line:
            fixed_lines.append('')
            continue
        
        # Decrease indent for closing braces
        if line.startswith('}'):
            indent_level = max(0, indent_level - 1)
        
        # Add appropriate indentation
        if indent_level > 0:
            indented_line = '    ' * indent_level + line
        else:
            indented_line = line
        
        fixed_lines.append(indented_line)
        
        # Increase indent for opening braces
        if line.endswith('{'):
            indent_level += 1
    
    return '\n'.join(fixed_lines)

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python fix-corrupted-files.py <project_root_path>")
        sys.exit(1)
    
    project_root = sys.argv[1]
    
    if not os.path.exists(project_root):
        print(f"Project root path does not exist: {project_root}")
        sys.exit(1)
    
    print("Scanning for corrupted Java files...")
    fix_corrupted_files(project_root)
    print("Done!")

if __name__ == "__main__":
    main()
