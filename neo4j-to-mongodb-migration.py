#!/usr/bin/env python3
"""
Neo4j to MongoDB Migration Script for Bandana Service
This script migrates Java code from Neo4j to MongoDB and removes Lombok dependencies.
"""

import os
import re
import shutil
from pathlib import Path
from typing import List, Dict, Tuple

class Neo4jToMongoMigrator:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.bandana_service = self.project_root / "bandana-service"
        self.src_main_java = self.bandana_service / "src" / "main" / "java"
        
        # Mapping of Neo4j to MongoDB annotations and imports
        self.annotation_mappings = {
            '@Node': '@Document',
            '@Relationship': '@DBRef',
            '@Id': '@Id',
            '@GeneratedValue': '',  # MongoDB auto-generates ObjectId
            '@CreatedDate': '@CreatedDate',
            '@LastModifiedDate': '@LastModifiedDate'
        }
        
        self.import_mappings = {
            'org.springframework.data.neo4j.core.schema.Node': 'org.springframework.data.mongodb.core.mapping.Document',
            'org.springframework.data.neo4j.core.schema.Relationship': 'org.springframework.data.mongodb.core.mapping.DBRef',
            'org.springframework.data.neo4j.core.schema.GeneratedValue': '',
            'org.springframework.data.neo4j.repository.Neo4jRepository': 'org.springframework.data.mongodb.repository.MongoRepository',
            'org.springframework.data.neo4j.repository.config.EnableNeo4jRepositories': 'org.springframework.data.mongodb.repository.config.EnableMongoRepositories',
            'org.springframework.data.neo4j.config.AbstractNeo4jConfig': '',
            'org.springframework.data.neo4j.core.support.UUIDStringGenerator': '',
            'org.springframework.data.neo4j.core.schema.Id': 'org.springframework.data.annotation.Id',
            'org.springframework.data.neo4j.repository.support.Neo4jEvaluationContextExtension': '',
            'org.springframework.data.neo4j.core.mapping.callback.BeforeBindCallback': '',
            'org.springframework.data.neo4j.core.mapping.callback.EventCallback': '',
            'org.neo4j.driver.Driver': ''
        }
        
        # Lombok to modern Java mappings
        self.lombok_patterns = {
            '@Data': self._replace_data_annotation,
            '@Getter': self._replace_getter_annotation,
            '@Setter': self._replace_setter_annotation,
            '@Builder': self._replace_builder_annotation,
            '@AllArgsConstructor': self._replace_all_args_constructor,
            '@NoArgsConstructor': self._replace_no_args_constructor,
            '@RequiredArgsConstructor': self._replace_required_args_constructor,
            '@ToString': self._replace_toString_annotation,
            '@EqualsAndHashCode': self._replace_equals_hashcode_annotation
        }

    def migrate_project(self):
        """Main migration method"""
        print("Starting Neo4j to MongoDB migration...")
        
        # 1. Update pom.xml dependencies
        self._update_pom_dependencies()
        
        # 2. Update application.properties
        self._update_application_properties()
        
        # 3. Migrate Java files
        self._migrate_java_files()
        
        # 4. Update configuration files
        self._update_configuration_files()
        
        print("Migration completed successfully!")

    def _update_pom_dependencies(self):
        """Update Maven dependencies in pom.xml"""
        pom_file = self.bandana_service / "pom.xml"
        if not pom_file.exists():
            print("pom.xml not found, skipping dependency update")
            return
            
        print("Updating pom.xml dependencies...")
        
        with open(pom_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove Neo4j dependencies
        neo4j_dependency_pattern = r'<dependency>\s*<groupId>org\.springframework\.boot</groupId>\s*<artifactId>spring-boot-starter-data-neo4j</artifactId>.*?</dependency>'
        content = re.sub(neo4j_dependency_pattern, '', content, flags=re.DOTALL)
        
        # Remove Neo4j driver dependency
        neo4j_driver_pattern = r'<dependency>\s*<groupId>org\.neo4j\.driver</groupId>.*?</dependency>'
        content = re.sub(neo4j_driver_pattern, '', content, flags=re.DOTALL)
        
        # Ensure MongoDB dependency exists (it should already be there based on the analysis)
        if 'spring-boot-starter-data-mongodb' not in content:
            mongodb_dependency = '''        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>'''
            
            # Insert after spring-boot-starter-web
            content = content.replace(
                '</artifactId>\n        </dependency>',
                '</artifactId>\n        </dependency>\n' + mongodb_dependency,
                1
            )
        
        with open(pom_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("pom.xml updated successfully")

    def _update_application_properties(self):
        """Update application.properties to use MongoDB instead of Neo4j"""
        props_file = self.bandana_service / "src" / "main" / "resources" / "application.properties"
        if not props_file.exists():
            print("application.properties not found, skipping")
            return
            
        print("Updating application.properties...")
        
        with open(props_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        new_lines = []
        for line in lines:
            # Comment out or remove Neo4j properties
            if 'spring.neo4j' in line and not line.strip().startswith('#'):
                new_lines.append('#' + line)
            else:
                new_lines.append(line)
        
        # Add MongoDB configuration if not present
        mongodb_config_exists = any('spring.data.mongodb' in line for line in new_lines)
        if not mongodb_config_exists:
            new_lines.extend([
                '\n# MongoDB Configuration\n',
                'spring.data.mongodb.host=localhost\n',
                'spring.data.mongodb.port=27017\n',
                'spring.data.mongodb.database=bandana\n',
                'spring.data.mongodb.auto-index-creation=true\n'
            ])
        
        with open(props_file, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        
        print("application.properties updated successfully")

    def _migrate_java_files(self):
        """Migrate all Java files from Neo4j to MongoDB"""
        print("Migrating Java files...")
        
        java_files = list(self.src_main_java.rglob("*.java"))
        
        for java_file in java_files:
            print(f"Processing: {java_file.relative_to(self.project_root)}")
            self._migrate_single_java_file(java_file)
        
        print(f"Migrated {len(java_files)} Java files")

    def _migrate_single_java_file(self, file_path: Path):
        """Migrate a single Java file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 1. Update imports
        content = self._update_imports(content)
        
        # 2. Update annotations
        content = self._update_annotations(content)
        
        # 3. Update repository interfaces
        content = self._update_repository_interfaces(content)
        
        # 4. Remove Lombok and add modern Java features
        content = self._remove_lombok_add_modern_java(content, file_path)
        
        # 5. Update specific Neo4j patterns
        content = self._update_neo4j_patterns(content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

    def _update_imports(self, content: str) -> str:
        """Update import statements"""
        for old_import, new_import in self.import_mappings.items():
            if old_import in content:
                if new_import:
                    content = content.replace(f'import {old_import};', f'import {new_import};')
                else:
                    # Remove the import entirely
                    content = re.sub(f'import {re.escape(old_import)};?\n?', '', content)
        
        # Add common MongoDB imports if needed
        if '@Document' in content and 'import org.springframework.data.mongodb.core.mapping.Document;' not in content:
            content = self._add_import(content, 'org.springframework.data.mongodb.core.mapping.Document')
        
        if '@DBRef' in content and 'import org.springframework.data.mongodb.core.mapping.DBRef;' not in content:
            content = self._add_import(content, 'org.springframework.data.mongodb.core.mapping.DBRef')
            
        return content

    def _add_import(self, content: str, import_statement: str) -> str:
        """Add an import statement to the file"""
        import_line = f'import {import_statement};\n'
        
        # Find the last import statement
        import_pattern = r'(import .*?;\n)'
        imports = re.findall(import_pattern, content)
        
        if imports:
            last_import = imports[-1]
            content = content.replace(last_import, last_import + import_line)
        else:
            # Add after package statement
            package_pattern = r'(package .*?;\n)'
            content = re.sub(package_pattern, r'\1\n' + import_line, content)
        
        return content

    def _update_annotations(self, content: str) -> str:
        """Update Neo4j annotations to MongoDB equivalents"""
        for old_annotation, new_annotation in self.annotation_mappings.items():
            if old_annotation in content:
                if new_annotation:
                    content = content.replace(old_annotation, new_annotation)
                else:
                    # Remove the annotation entirely
                    content = re.sub(f'{re.escape(old_annotation)}\\s*\n?', '', content)
        
        return content

    def _update_repository_interfaces(self, content: str) -> str:
        """Update repository interface declarations"""
        # Replace Neo4jRepository with MongoRepository
        neo4j_repo_pattern = r'extends Neo4jRepository<(\w+),\s*(\w+)>'
        content = re.sub(neo4j_repo_pattern, r'extends MongoRepository<\1, \2>', content)
        
        return content

    def _remove_lombok_add_modern_java(self, content: str, file_path: Path) -> str:
        """Remove Lombok annotations and add modern Java equivalents"""
        # This is a simplified implementation - in practice, you'd need more sophisticated parsing
        
        # Remove Lombok imports
        lombok_imports = [
            'import lombok.*;',
            'import lombok.Getter;',
            'import lombok.Setter;',
            'import lombok.Data;',
            'import lombok.Builder;',
            'import lombok.AllArgsConstructor;',
            'import lombok.NoArgsConstructor;',
            'import lombok.RequiredArgsConstructor;',
            'import lombok.ToString;',
            'import lombok.EqualsAndHashCode;',
            'import lombok.NonNull;'
        ]
        
        for lombok_import in lombok_imports:
            content = content.replace(lombok_import + '\n', '')
        
        # Remove Lombok annotations (simplified - would need more sophisticated logic for full implementation)
        lombok_annotations = ['@Data', '@Getter', '@Setter', '@Builder', '@AllArgsConstructor', '@NoArgsConstructor', '@ToString', '@EqualsAndHashCode']
        
        for annotation in lombok_annotations:
            content = re.sub(f'{re.escape(annotation)}\\s*\n?', '', content)
        
        return content

    def _update_neo4j_patterns(self, content: str) -> str:
        """Update specific Neo4j patterns to MongoDB equivalents"""
        
        # Update @EnableNeo4jRepositories to @EnableMongoRepositories
        content = content.replace('@EnableNeo4jRepositories', '@EnableMongoRepositories')
        content = content.replace('@EnableNeo4jAuditing', '@EnableMongoAuditing')
        
        # Remove AbstractNeo4jConfig inheritance
        content = re.sub(r'extends AbstractNeo4jConfig\s*{', '{', content)
        
        # Remove Driver bean method
        driver_method_pattern = r'@Bean\s*public Driver driver\(\)\s*{\s*return null;\s*}'
        content = re.sub(driver_method_pattern, '', content, flags=re.DOTALL)
        
        return content

    def _update_configuration_files(self):
        """Update configuration files"""
        print("Updating configuration files...")
        
        # The DbConfig.java file seems to already be updated based on the analysis
        # But let's ensure it's properly configured for MongoDB
        
        db_config_file = self.src_main_java / "lk" / "bandana" / "config" / "DbConfig.java"
        if db_config_file.exists():
            print("DbConfig.java already appears to be configured for MongoDB")

    def _replace_data_annotation(self, content: str) -> str:
        """Replace @Data with getters and setters"""
        # Extract class fields and generate getters/setters
        return self._generate_getters_setters(content)

    def _replace_getter_annotation(self, content: str) -> str:
        """Replace @Getter with individual getter methods"""
        return self._generate_getters(content)

    def _replace_setter_annotation(self, content: str) -> str:
        """Replace @Setter with individual setter methods"""
        return self._generate_setters(content)

    def _replace_builder_annotation(self, content: str) -> str:
        """Replace @Builder with factory methods or constructors"""
        return self._generate_builder_pattern(content)

    def _replace_all_args_constructor(self, content: str) -> str:
        """Replace @AllArgsConstructor with explicit constructor"""
        return self._generate_all_args_constructor(content)

    def _replace_no_args_constructor(self, content: str) -> str:
        """Replace @NoArgsConstructor with explicit default constructor"""
        return self._generate_no_args_constructor(content)

    def _replace_required_args_constructor(self, content: str) -> str:
        """Replace @RequiredArgsConstructor with explicit constructor for final fields"""
        return self._generate_required_args_constructor(content)

    def _replace_toString_annotation(self, content: str) -> str:
        """Replace @ToString with explicit toString method"""
        return self._generate_toString_method(content)

    def _replace_equals_hashcode_annotation(self, content: str) -> str:
        """Replace @EqualsAndHashCode with explicit equals and hashCode methods"""
        return self._generate_equals_hashcode_methods(content)

    def _generate_getters_setters(self, content: str) -> str:
        """Generate getter and setter methods for all fields"""
        # Extract fields from the class
        field_pattern = r'private\s+(\w+(?:<[^>]+>)?)\s+(\w+);'
        fields = re.findall(field_pattern, content)

        methods = []
        for field_type, field_name in fields:
            # Generate getter
            getter_name = f"get{field_name.capitalize()}"
            if field_type.lower() == 'boolean':
                getter_name = f"is{field_name.capitalize()}"

            getter = f"""
    public {field_type} {getter_name}() {{
        return this.{field_name};
    }}"""

            # Generate setter
            setter = f"""
    public void set{field_name.capitalize()}({field_type} {field_name}) {{
        this.{field_name} = {field_name};
    }}"""

            methods.extend([getter, setter])

        # Add methods before the last closing brace
        if methods:
            methods_str = '\n'.join(methods) + '\n'
            content = content.rstrip()
            if content.endswith('}'):
                content = content[:-1] + methods_str + '}'

        return content

    def _generate_getters(self, content: str) -> str:
        """Generate only getter methods"""
        field_pattern = r'private\s+(\w+(?:<[^>]+>)?)\s+(\w+);'
        fields = re.findall(field_pattern, content)

        methods = []
        for field_type, field_name in fields:
            getter_name = f"get{field_name.capitalize()}"
            if field_type.lower() == 'boolean':
                getter_name = f"is{field_name.capitalize()}"

            getter = f"""
    public {field_type} {getter_name}() {{
        return this.{field_name};
    }}"""
            methods.append(getter)

        if methods:
            methods_str = '\n'.join(methods) + '\n'
            content = content.rstrip()
            if content.endswith('}'):
                content = content[:-1] + methods_str + '}'

        return content

    def _generate_setters(self, content: str) -> str:
        """Generate only setter methods"""
        field_pattern = r'private\s+(\w+(?:<[^>]+>)?)\s+(\w+);'
        fields = re.findall(field_pattern, content)

        methods = []
        for field_type, field_name in fields:
            setter = f"""
    public void set{field_name.capitalize()}({field_type} {field_name}) {{
        this.{field_name} = {field_name};
    }}"""
            methods.append(setter)

        if methods:
            methods_str = '\n'.join(methods) + '\n'
            content = content.rstrip()
            if content.endswith('}'):
                content = content[:-1] + methods_str + '}'

        return content

    def _generate_builder_pattern(self, content: str) -> str:
        """Generate builder pattern using modern Java"""
        # Extract class name
        class_pattern = r'public class (\w+)'
        class_match = re.search(class_pattern, content)
        if not class_match:
            return content

        class_name = class_match.group(1)

        # Extract fields
        field_pattern = r'private\s+(\w+(?:<[^>]+>)?)\s+(\w+);'
        fields = re.findall(field_pattern, content)

        # Generate static factory method
        factory_method = f"""
    public static {class_name} builder() {{
        return new {class_name}();
    }}"""

        # Generate fluent setters
        fluent_setters = []
        for field_type, field_name in fields:
            fluent_setter = f"""
    public {class_name} {field_name}({field_type} {field_name}) {{
        this.{field_name} = {field_name};
        return this;
    }}"""
            fluent_setters.append(fluent_setter)

        # Generate build method
        build_method = f"""
    public {class_name} build() {{
        return this;
    }}"""

        methods = [factory_method] + fluent_setters + [build_method]
        methods_str = '\n'.join(methods) + '\n'

        content = content.rstrip()
        if content.endswith('}'):
            content = content[:-1] + methods_str + '}'

        return content

    def _generate_all_args_constructor(self, content: str) -> str:
        """Generate constructor with all arguments"""
        # Extract class name and fields
        class_pattern = r'public class (\w+)'
        class_match = re.search(class_pattern, content)
        if not class_match:
            return content

        class_name = class_match.group(1)
        field_pattern = r'private\s+(\w+(?:<[^>]+>)?)\s+(\w+);'
        fields = re.findall(field_pattern, content)

        if not fields:
            return content

        # Generate constructor parameters
        params = [f"{field_type} {field_name}" for field_type, field_name in fields]
        param_str = ', '.join(params)

        # Generate constructor body
        assignments = [f"        this.{field_name} = {field_name};" for _, field_name in fields]
        assignment_str = '\n'.join(assignments)

        constructor = f"""
    public {class_name}({param_str}) {{
{assignment_str}
    }}"""

        # Insert constructor after fields
        last_field_pattern = r'(private\s+\w+(?:<[^>]+>)?\s+\w+;)'
        matches = list(re.finditer(last_field_pattern, content))
        if matches:
            last_match = matches[-1]
            insert_pos = last_match.end()
            content = content[:insert_pos] + constructor + content[insert_pos:]

        return content

    def _generate_no_args_constructor(self, content: str) -> str:
        """Generate default no-args constructor"""
        class_pattern = r'public class (\w+)'
        class_match = re.search(class_pattern, content)
        if not class_match:
            return content

        class_name = class_match.group(1)

        constructor = f"""
    public {class_name}() {{
    }}"""

        # Insert constructor after fields
        field_pattern = r'(private\s+\w+(?:<[^>]+>)?\s+\w+;)'
        matches = list(re.finditer(field_pattern, content))
        if matches:
            last_match = matches[-1]
            insert_pos = last_match.end()
            content = content[:insert_pos] + constructor + content[insert_pos:]

        return content

    def _generate_required_args_constructor(self, content: str) -> str:
        """Generate constructor for final/required fields"""
        # Extract class name and final fields
        class_pattern = r'public class (\w+)'
        class_match = re.search(class_pattern, content)
        if not class_match:
            return content

        class_name = class_match.group(1)
        final_field_pattern = r'private\s+final\s+(\w+(?:<[^>]+>)?)\s+(\w+);'
        final_fields = re.findall(final_field_pattern, content)

        if not final_fields:
            return content

        # Generate constructor parameters
        params = [f"{field_type} {field_name}" for field_type, field_name in final_fields]
        param_str = ', '.join(params)

        # Generate constructor body
        assignments = [f"        this.{field_name} = {field_name};" for _, field_name in final_fields]
        assignment_str = '\n'.join(assignments)

        constructor = f"""
    public {class_name}({param_str}) {{
{assignment_str}
    }}"""

        # Insert constructor after fields
        field_pattern = r'(private\s+(?:final\s+)?\w+(?:<[^>]+>)?\s+\w+;)'
        matches = list(re.finditer(field_pattern, content))
        if matches:
            last_match = matches[-1]
            insert_pos = last_match.end()
            content = content[:insert_pos] + constructor + content[insert_pos:]

        return content

    def _generate_toString_method(self, content: str) -> str:
        """Generate toString method"""
        # Extract class name and fields
        class_pattern = r'public class (\w+)'
        class_match = re.search(class_pattern, content)
        if not class_match:
            return content

        class_name = class_match.group(1)
        field_pattern = r'private\s+(?:final\s+)?(\w+(?:<[^>]+>)?)\s+(\w+);'
        fields = re.findall(field_pattern, content)

        if not fields:
            return content

        # Generate field representations
        field_strs = [f'"{field_name}=" + {field_name}' for _, field_name in fields]
        field_str = ' + ", " + '.join(field_strs)

        toString_method = f'''
    @Override
    public String toString() {{
        return "{class_name}{{" + {field_str} + "}}";
    }}'''

        # Add method before the last closing brace
        content = content.rstrip()
        if content.endswith('}'):
            content = content[:-1] + toString_method + '\n}'

        return content

    def _generate_equals_hashcode_methods(self, content: str) -> str:
        """Generate equals and hashCode methods"""
        # Extract class name and fields
        class_pattern = r'public class (\w+)'
        class_match = re.search(class_pattern, content)
        if not class_match:
            return content

        class_name = class_match.group(1)
        field_pattern = r'private\s+(?:final\s+)?(\w+(?:<[^>]+>)?)\s+(\w+);'
        fields = re.findall(field_pattern, content)

        if not fields:
            return content

        # Generate equals method
        field_comparisons = []
        for field_type, field_name in fields:
            if field_type in ['int', 'long', 'double', 'float', 'boolean', 'byte', 'short', 'char']:
                field_comparisons.append(f'{field_name} == other.{field_name}')
            else:
                field_comparisons.append(f'Objects.equals({field_name}, other.{field_name})')

        comparison_str = ' && '.join(field_comparisons)

        equals_method = f'''
    @Override
    public boolean equals(Object obj) {{
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        {class_name} other = ({class_name}) obj;
        return {comparison_str};
    }}'''

        # Generate hashCode method
        hash_fields = [field_name for _, field_name in fields]
        hash_str = ', '.join(hash_fields)

        hashcode_method = f'''
    @Override
    public int hashCode() {{
        return Objects.hash({hash_str});
    }}'''

        # Add Objects import if needed
        if 'import java.util.Objects;' not in content:
            content = self._add_import(content, 'java.util.Objects')

        methods = equals_method + hashcode_method

        # Add methods before the last closing brace
        content = content.rstrip()
        if content.endswith('}'):
            content = content[:-1] + methods + '\n}'

        return content


def main():
    """Main function to run the migration"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python neo4j-to-mongodb-migration.py <project_root_path>")
        sys.exit(1)
    
    project_root = sys.argv[1]
    
    if not os.path.exists(project_root):
        print(f"Project root path does not exist: {project_root}")
        sys.exit(1)
    
    migrator = Neo4jToMongoMigrator(project_root)
    migrator.migrate_project()


if __name__ == "__main__":
    main()
