#!/usr/bin/env python3
"""
Convert appropriate Java classes to records and apply modern Java 21 features
"""

import os
import re
from pathlib import Path

def convert_response_to_record():
    """Convert Response.java to a modern record"""
    response_file = Path("bandana-service/src/main/java/lk/bandana/core/entity/Response.java")
    
    if not response_file.exists():
        print("Response.java not found")
        return
    
    record_content = '''package lk.bandana.core.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Response record for API responses
 * Modern Java 21 record implementation
 */
@Document
public record Response<T>(
    @Id String id,
    boolean success,
    String message,
    T data,
    String errorCode,
    long timestamp
) {
    
    // Compact constructor for validation
    public Response {
        if (message == null) {
            message = success ? "Operation successful" : "Operation failed";
        }
        if (timestamp == 0) {
            timestamp = System.currentTimeMillis();
        }
    }
    
    // Static factory methods for common responses
    public static <T> Response<T> success(T data) {
        return new Response<>(
            null,
            true,
            "Operation successful",
            data,
            null,
            System.currentTimeMillis()
        );
    }
    
    public static <T> Response<T> success(String message, T data) {
        return new Response<>(
            null,
            true,
            message,
            data,
            null,
            System.currentTimeMillis()
        );
    }
    
    public static <T> Response<T> error(String message) {
        return new Response<>(
            null,
            false,
            message,
            null,
            "ERROR",
            System.currentTimeMillis()
        );
    }
    
    public static <T> Response<T> error(String message, String errorCode) {
        return new Response<>(
            null,
            false,
            message,
            null,
            errorCode,
            System.currentTimeMillis()
        );
    }
    
    // Convenience methods
    public boolean isSuccess() {
        return success;
    }
    
    public boolean hasData() {
        return data != null;
    }
    
    public boolean hasError() {
        return !success;
    }
}
'''
    
    with open(response_file, 'w', encoding='utf-8') as f:
        f.write(record_content)
    
    print("✓ Converted Response.java to modern record")

def modernize_user_profile():
    """Modernize UserProfile.java with Java 21 features"""
    user_profile_file = Path("bandana-service/src/main/java/lk/bandana/core/entity/UserProfile.java")
    
    if not user_profile_file.exists():
        print("UserProfile.java not found")
        return
    
    try:
        with open(user_profile_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add modern Java features
        modernized_content = add_modern_features_to_user_profile(content)
        
        with open(user_profile_file, 'w', encoding='utf-8') as f:
            f.write(modernized_content)
        
        print("✓ Modernized UserProfile.java with Java 21 features")
    
    except Exception as e:
        print(f"✗ Error modernizing UserProfile.java: {e}")

def add_modern_features_to_user_profile(content: str) -> str:
    """Add modern Java features to UserProfile class"""
    
    # Add sealed interface for ProfileStatus if not exists
    if 'enum ProfileStatus' in content:
        # Replace enum with sealed interface pattern
        enum_pattern = r'public enum ProfileStatus \{[^}]+\}'
        sealed_interface = '''
    /**
     * Sealed interface for profile status using modern Java pattern
     */
    public sealed interface ProfileStatus 
        permits ProfileStatus.Pending, ProfileStatus.Approved, ProfileStatus.Rejected, ProfileStatus.Suspended {
        
        record Pending() implements ProfileStatus {}
        record Approved() implements ProfileStatus {}
        record Rejected(String reason) implements ProfileStatus {}
        record Suspended(String reason, java.time.LocalDateTime until) implements ProfileStatus {}
        
        // Pattern matching helper
        default String getDisplayName() {
            return switch (this) {
                case Pending() -> "Pending Review";
                case Approved() -> "Approved";
                case Rejected(var reason) -> "Rejected: " + reason;
                case Suspended(var reason, var until) -> "Suspended until " + until + ": " + reason;
            };
        }
    }'''
        
        content = re.sub(enum_pattern, sealed_interface, content, flags=re.DOTALL)
    
    # Add text blocks for validation messages
    if 'validation' in content.lower():
        validation_messages = '''
    private static final String VALIDATION_RULES = \"\"\"
        Profile Validation Rules:
        - First name and last name are required
        - Age must be between 18 and 80
        - Height must be between 120cm and 220cm
        - Email must be valid format
        - Phone number must be valid Sri Lankan format
        - At least one photo is required for approval
        \"\"\";'''
        
        # Insert after class declaration
        class_pattern = r'(public class UserProfile[^{]*\{)'
        content = re.sub(class_pattern, r'\1\n' + validation_messages, content)
    
    # Add modern validation method using pattern matching
    validation_method = '''
    
    /**
     * Validate profile using modern Java pattern matching
     */
    public ValidationResult validateProfile() {
        return switch (this.getStatus()) {
            case ProfileStatus.Pending() -> validateForApproval();
            case ProfileStatus.Approved() -> ValidationResult.valid();
            case ProfileStatus.Rejected(var reason) -> ValidationResult.invalid("Profile rejected: " + reason);
            case ProfileStatus.Suspended(var reason, var until) -> 
                ValidationResult.invalid("Profile suspended until " + until + ": " + reason);
        };
    }
    
    private ValidationResult validateForApproval() {
        var errors = new java.util.ArrayList<String>();
        
        if (firstName == null || firstName.isBlank()) {
            errors.add("First name is required");
        }
        
        if (lastName == null || lastName.isBlank()) {
            errors.add("Last name is required");
        }
        
        if (age < 18 || age > 80) {
            errors.add("Age must be between 18 and 80");
        }
        
        if (height < 120 || height > 220) {
            errors.add("Height must be between 120cm and 220cm");
        }
        
        return errors.isEmpty() ? 
            ValidationResult.valid() : 
            ValidationResult.invalid(String.join(", ", errors));
    }
    
    /**
     * Validation result record
     */
    public record ValidationResult(boolean isValid, String message) {
        public static ValidationResult valid() {
            return new ValidationResult(true, "Valid");
        }
        
        public static ValidationResult invalid(String message) {
            return new ValidationResult(false, message);
        }
    }'''
    
    # Add before the last closing brace
    content = content.rstrip()
    if content.endswith('}'):
        content = content[:-1] + validation_method + '\n}'
    
    return content

def modernize_service_classes():
    """Add modern Java features to service classes"""
    service_files = [
        "bandana-service/src/main/java/lk/bandana/core/service/UserProfileService.java",
        "bandana-service/src/main/java/lk/bandana/core/service/MatchingService.java",
        "bandana-service/src/main/java/lk/bandana/core/service/SubscriptionService.java"
    ]
    
    for service_file in service_files:
        service_path = Path(service_file)
        if service_path.exists():
            try:
                with open(service_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Add modern patterns
                modernized_content = add_modern_service_patterns(content)
                
                with open(service_path, 'w', encoding='utf-8') as f:
                    f.write(modernized_content)
                
                print(f"✓ Modernized {service_path.name}")
            
            except Exception as e:
                print(f"✗ Error modernizing {service_path.name}: {e}")

def add_modern_service_patterns(content: str) -> str:
    """Add modern Java patterns to service classes"""
    
    # Replace traditional null checks with modern patterns
    content = re.sub(
        r'if\s*\(\s*(\w+)\s*!=\s*null\s*\)\s*\{',
        r'if (\1 != null) {',
        content
    )
    
    # Add Optional usage examples
    if 'findById' in content and 'Optional' not in content:
        optional_import = 'import java.util.Optional;\n'
        # Add import after package
        package_pattern = r'(package [^;]+;\n)'
        content = re.sub(package_pattern, r'\1\n' + optional_import, content)
    
    return content

def create_modern_dto_records():
    """Create modern DTO records for API communication"""
    dto_dir = Path("bandana-service/src/main/java/lk/bandana/core/dto")
    dto_dir.mkdir(exist_ok=True)
    
    # Create UserProfileDTO record
    user_profile_dto = '''package lk.bandana.core.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * User Profile DTO record for API communication
 * Modern Java 21 record implementation
 */
public record UserProfileDTO(
    String id,
    String firstName,
    String lastName,
    String email,
    String phone,
    LocalDate dateOfBirth,
    Integer age,
    String gender,
    Integer height,
    String occupation,
    String education,
    String religion,
    String caste,
    String province,
    String district,
    List<String> interests,
    List<String> photoUrls,
    String profileStatus,
    Integer completionPercentage,
    Integer qualityScore,
    LocalDateTime createdAt,
    LocalDateTime updatedAt
) {
    
    // Compact constructor for validation
    public UserProfileDTO {
        if (firstName != null) {
            firstName = firstName.trim();
        }
        if (lastName != null) {
            lastName = lastName.trim();
        }
        if (email != null) {
            email = email.toLowerCase().trim();
        }
    }
    
    // Convenience methods
    public String getFullName() {
        return STR."\\{firstName} \\{lastName}";
    }
    
    public boolean isComplete() {
        return completionPercentage != null && completionPercentage >= 80;
    }
    
    public boolean isHighQuality() {
        return qualityScore != null && qualityScore >= 80;
    }
}
'''
    
    with open(dto_dir / "UserProfileDTO.java", 'w', encoding='utf-8') as f:
        f.write(user_profile_dto)
    
    print("✓ Created modern UserProfileDTO record")

def main():
    print("Converting Java classes to modern Java 21 features...")
    
    # Convert simple classes to records
    convert_response_to_record()
    
    # Modernize existing classes
    modernize_user_profile()
    modernize_service_classes()
    
    # Create new modern DTOs
    create_modern_dto_records()
    
    print("\nJava 21 modernization completed!")
    print("\nModern features applied:")
    print("- Records for immutable data classes")
    print("- Sealed interfaces for type safety")
    print("- Pattern matching in switch expressions")
    print("- Text blocks for multi-line strings")
    print("- String templates (STR processor)")
    print("- Modern validation patterns")

if __name__ == "__main__":
    main()
