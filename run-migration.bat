@echo off
echo Bandana Service Migration: Neo4j to MongoDB + Lombok to Modern Java
echo ================================================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.6+ and try again
    pause
    exit /b 1
)

REM Get the current directory (should be the project root)
set PROJECT_ROOT=%cd%

echo Project Root: %PROJECT_ROOT%
echo.

REM Check if bandana-service directory exists
if not exist "%PROJECT_ROOT%\bandana-service" (
    echo Error: bandana-service directory not found in current directory
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

echo Starting migration...
echo.

REM Run the migration script
python bandana-migration-script.py "%PROJECT_ROOT%"

if errorlevel 1 (
    echo.
    echo Migration failed! Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Migration completed successfully!
echo.
echo Next steps:
echo 1. Review the changes in your IDE
echo 2. Run: mvn clean compile
echo 3. Test with MongoDB
echo.
pause
