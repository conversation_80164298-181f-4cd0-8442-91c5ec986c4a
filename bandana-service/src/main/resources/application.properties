# MongoDB Configuration
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=bandana
spring.data.mongodb.authentication-database=bandana
spring.data.mongodb.username=bandana_user
spring.data.mongodb.password=Madhawa1988#

# MongoDB Connection URI (alternative configuration)
# spring.data.mongodb.uri=mongodb://bandana_user:Madhawa1988#@localhost:27017/bandana

# Neo4j Configuration (commented out - migrated to MongoDB)
#spring.neo4j.uri=neo4j+s://cfa6fc31.databases.neo4j.io
#spring.neo4j.authentication.username=neo4j
#spring.neo4j.authentication.password=p_QtsZ8R9uEFNyfm1x65kZYLrNrokKgEi2kTgfGNZs8
#spring.neo4j.uri=bolt://localhost:7687
#spring.neo4j.authentication.username=neo4j
#spring.neo4j.authentication.password=Mad<PERSON>a1988#

#Thymeleaf
spring.thymeleaf.enabled=true
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.mode=HTML
