package lk.bandana.custom.service;

import lk.bandana.core.entity.Sequence;
import lk.bandana.core.repository.SequenceRepository;
import lk.bandana.custom.entity.Kendara;
import lk.bandana.custom.entity.helper.DashaBalance;
import lk.bandana.custom.entity.helper.LagnaSquare;
import lk.bandana.custom.repository.KendaraRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class KendaraService {

    private static final Logger LOGGER = LoggerFactory.getLogger(KendaraService.class);

    @Autowired
    KendaraRepository kendaraRepository;

    @Autowired
    Kendara kendara;

    @Autowired
    SequenceRepository sequenceRepository;

    // b6 - T0 - no of centuries from 1900 jan 1st to given date
    private double ps, pt, z1, z2, b6, s1, lat, longt, aya, obliq, sidtime, h6, G;
    double[] tt = new double[4];

    int ret = 0, page = 1, line;
    int[] r3 = new int[13];
    int[] s3 = new int[13];
    int[][] varga = new int[13][7];

    double[] plnt = new double[26];

    String birthDate;

    String rashi[] = {"MESHA", "VRUSHABA", "MITHUNA", "KATAKA", "SINHA", "KANNYA", "THULA", "VHUSCHIKA", "DHANU",
            "MAKARA", "KUMBA", "MEENA"};

    String nakath[] = {"ASVIDA", "BERANA", "KETTI", "REHENA", "MUWASIRASA", "ADA", "PUNAWASA", "PUSA", "ASLISA",
            "MA", "PUWAPAL", "UTTRAPAL", "HATHA", "SITHA", "SA", "VISA", "ANURA", "DHETA", "MULA", "PUWASALA",
            "UTTRASALA", "SUWANA", "DHENATA", "SIYAWASA", "PUWAPUTUPA", "UTTRAPUTUPA", "RHEWATEE"};

    String x1[] = {"KETU", "SIKURU", "RAVI", "SADU", "KUJA", "RAHU", "GURU", "SHANI", "BUDA"};

    double yy[] = {7, 20, 6, 10, 7, 18, 16, 19, 17};

    String day[] = {"SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"};

    long LG;

    private long calculateJulianDate(long d, long m, long y) {
        long j, a9, y9, m9, a1, b1, c1, d1, JDN;
        float b;

        a9 = (14 - m) / 12;
        y9 = y + 4800 - a9;
        m9 = m + 12 * a9 - 3;

        a1 = (153 * m9 + 2) / 5;
        b1 = y9 / 4;
        c1 = y9 / 100;
        d1 = y9 / 400;

        JDN = d + a1 + 365 * y9 + b1 - c1 + d1 - 32045;

        j = JDN - 1720995;

        return j;
    }

    private double planet(double pg, double ph, double pp, double pe, double pq, double pa, int pno) {
        double pm, pb = 0, pf = 0, pc, pd, pr, e1, e2, e3, e4, v1, pv, pj, pk, pl, px, py;
        pm = pg - ph;
        if (pm < 0) {
            pm += 360.0;
        }
        pb = pm * z2;
        pf = pb + pe * Math.sin(pb);
        do {
            pc = pf - pe * Math.sin(pf) - pb;
            pd = 1 - pe * Math.cos(pf);
            pf = pf - pc / pd;
        }
        while (Math.abs(pc / pd) > 0.01);
        pr = pa * (1 - pe * Math.cos(pf));
        e1 = Math.atan(pe / Math.sqrt(1 - pe * pe));
        e2 = z1 / 4 - e1 / 2;
        e3 = Math.tan(e2);
        e4 = Math.tan(pf / 2);
        v1 = Math.atan(e4 / e3);
        if (v1 < 0.0)
            v1 += z1;
        pv = 2 * v1;
        pc = ph * z2;
        pd = pp * z2;
        pb = pq * z2;
        pj = pv + pc;
        pk = pj - pd;
        pl = 1.0 - Math.cos(pb);
        px = (Math.cos(pj) + Math.sin(pk) * Math.sin(pd) * pl) * pr;
        py = (Math.sin(pj) - Math.sin(pk) * Math.cos(pd) * pl) * pr;
        if (pno == 1) {
            ps = px;
            pt = py;
        }
        pc = ps + px;
        pd = pt + py;
        pm = Math.atan(pd / pc) / z2;
        if (pc < 0.0)
            pm += 180.0;
        else if (pd < 0.0)
            pm += 360.0;
        return pm;
    }


    private double fract(double x) {
        long i;
        double y;

        i = (long) x;
        y = x - i;
        return y;
    }

    private void calculateSun() {
        double g0, h0, p0, e0, q0, a0;
        int pno;

        g0 = 360 * fract(0.71455 + 99.99826 * b6);
        h0 = 258.76 + 0.323 * b6;
        p0 = 0.0;
        e0 = 0.016751 - 0.000042 * b6;
        q0 = 0.0;
        a0 = 1.0;
        pno = 1;

        plnt[pno] = planet(g0, h0, p0, e0, q0, a0, pno);
    }

    private void calculateMercury() {
        double g0, h0, p0, e0, q0, a0;
        int pno;

        g0 = 360 * fract(0.43255 + 415.20187 * b6);
        h0 = 53.44 + 0.159 * b6;
        p0 = 24.69 - 0.211 * b6;
        e0 = 0.205614 + 0.00002 * b6;
        q0 = 7.00288 + 0.001861 * b6;
        a0 = 0.3871;
        pno = 2;

        plnt[pno] = planet(g0, h0, p0, e0, q0, a0, pno);
    }

    private void calculateVenus() {
        double g0, h0, p0, e0, q0, a0;
        int pno;

        g0 = 360 * fract(0.88974 + 162.54949 * b6);
        h0 = 107.70 + 0.012 * b6;
        p0 = 53.22 - 0.496 * b6;
        e0 = 0.006820 - 0.000048 * b6;
        q0 = 3.39363 + 0.001 * b6;
        a0 = 0.72333;
        pno = 3;

        plnt[pno] = planet(g0, h0, p0, e0, q0, a0, pno);
    }

    private void calculateMars() {
        // g0 -
        // a0 - Mean Distance
        double g0, h0, p0, e0, q0, a0;
        int pno;

        g0 = 360 * fract(0.75358 + 53.16751 * b6);
        h0 = 311.76 + 0.445 * b6;
        p0 = 26.33 - 0.625 * b6;
        e0 = 0.093313 - 0.000092 * b6;
        q0 = 1.850333 - 0.000675 * b6;
        a0 = 1.5237;
        pno = 4;

        plnt[pno] = planet(g0, h0, p0, e0, q0, a0, pno);
    }

    private void calculateJupiter() {
        double g0, h0, p0, e0, q0, a0;
        int pno;

        g0 = 360 * fract(0.59886 + 8.43029 * b6);
        h0 = 350.26 + 0.214 * b6;
        e0 = 0.048335 - 0.000164 * b6;
        p0 = 76.98 - 0.386 * b6;
        q0 = 1.308376 - 0.005696 * b6;
        a0 = 5.2026;
        pno = 5;

        plnt[pno] = planet(g0, h0, p0, e0, q0, a0, pno);
    }

    private void calculateSaturn() {
        double g0, h0, p0, e0, q0, a0;
        int pno;

        g0 = 360 * fract(0.67807 + 3.39476 * b6);
        e0 = 0.055892 - 0.000346 * b6;
        h0 = 68.64 + 0.562 * b6;
        p0 = 90.33 - 0.523 * b6;
        q0 = 2.492520 - 0.003920 * b6;
        a0 = 9.5547;
        pno = 6;

        plnt[pno] = planet(g0, h0, p0, e0, q0, a0, pno);
    }

    private void calculateMoon() {
        double g1, h1, a0, b0, c0, g0, e0, d0, f0, l0;
        double r0, d3, d4, d5;

        g1 = 360 * fract(0.71455 + 99.99826 * b6);
        h1 = 258.76 + 0.323 * b6;
        a0 = 360 * fract(0.68882 + 1336.851353 * b6);
        b0 = 360 * fract(0.8663 + 11.298994 * b6 - 3.0e-5 * b6 * b6);
        c0 = 360 * fract(0.65756 - 5.376495 * b6);
        if (c0 < 0.0)
            c0 += 360.0;
        g0 = z2 * (a0 - b0);
        e0 = z2 * (g1 - h1);
        d0 = z2 * (a0 - g1);
        f0 = z2 * (a0 - c0);
        l0 = a0 + 6.2888 * Math.sin(g0) + 0.2136 * Math.sin(2 * g0) + 0.01 * Math.sin(3 * g0) + 1.274 * Math.sin(2 * d0 - g0) + 0.0085 * Math.sin(4 * d0 - 2 * g0);
        l0 = l0 - .0347 * Math.sin(d0) + 0.6583 * Math.sin(2 * d0) + 0.0039 * Math.sin(4 * d0) - 0.1856 * Math.sin(e0) - .0021 * Math.sin(2 * e0) + 0.0052 * Math.sin(g0 - d0);
        l0 = l0 - .0588 * Math.sin(2 * g0 - 2 * d0) + .0572 * Math.sin(2 * d0 - g0 - e0) + .0533 * Math.sin(g0 + 2 * d0) + .0458 * Math.sin(2 * d0 - e0) + .041 * Math.sin(g0 - e0) - .0305 * Math.sin(g0 + e0);
        l0 = l0 - .0237 * Math.sin(2 * f0 - g0) - .0153 * Math.sin(2 * f0 - 2 * d0) + .0107 * Math.sin(4 * d0 - g0) - .0079 * Math.sin(-g0 + e0 + 2 * d0) - .0068 * Math.sin(e0 + 2 * d0) + .005 * Math.sin(e0 + d0);
        l0 = l0 - .0023 * Math.sin(g0 + d0) + .004 * Math.sin(2 * g0 + 2 * d0) + .004 * Math.sin(g0 - e0 + 2 * d0) - .0037 * Math.sin(3 * g0 - 2 * d0) - .0026 * Math.sin(g0 - 2 * d0 + 2 * f0) + .0027 * Math.sin(2 * g0 - e0);
        l0 = l0 - .0024 * Math.sin(2 * g0 + e0 - 2 * d0) + .0022 * Math.sin(2 * d0 - 2 * e0) - .0021 * Math.sin(2 * g0 + e0) + .0021 * Math.sin(c0 * z2) + .0021 * Math.sin(2 * d0 - g0 - 2 * e0);
        l0 = l0 - .0018 * Math.sin(g0 + 2 * d0 - 2 * f0) + .0012 * Math.sin(4 * d0 - g0 - e0) - .0008 * Math.sin(3 * d0 - g0);
        r0 = z2 * 2 * (l0 - c0);
        d3 = l0 - 0.1143 * Math.sin(r0) + .004;
        if (d3 >= 360.0)
            d3 -= 360.0;
        if (d3 < 0.0)
            d3 += 360.0;

        plnt[7] = d3;

        d4 = c0;

        plnt[8] = d4;

        d5 = c0 + 180.0;
        if (d5 >= 360.0)
            d5 -= 360.0;

        plnt[9] = d5;
    }


    private void ayan() {
        plnt[0] = 22.460148 + 1.396042 * b6 + 3.08e-4 * b6 * b6;
    }

    private void bhav() {
        double a0, b0, c0;
        int i;

        aya = plnt[0];
        obliq = 23.452294 - 0.0130125 * b6;
        a0 = 24 * fract(0.2769 + 100.00214 * b6);
        b0 = h6 * 24 + 12;
        c0 = longt / 15;
        sidtime = 24 * fract((a0 + b0 + c0) / 24);
        if (sidtime < 0)
            sidtime += 24.0;

        G = bhavspl(sidtime, lat);
        LG = (long) (G / 30);

        kendara.setAyanamsa(plnt[0]);
        kendara.setObliquity(obliq);
        kendara.setSiderealTime(sidtime);
        kendara.setLagnaSputa(G);
        kendara.setLagna(rashi[(int) LG]);

    }

    private double bhavspl(double a0, double c0) {
        double r0, w0, b0, g0;

        r0 = aya;
        w0 = obliq * z2;
        b0 = a0 * 15 + 90.0;
        if (b0 >= 360.0)
            b0 -= 360.0;
        a0 *= z1 / 12;
        c0 *= z2;
        if (a0 == 0.0 && c0 == 0.0)
            return 90.0;
        g0 = Math.atan(-Math.cos(a0) / (Math.sin(c0) * Math.sin(w0) / Math.cos(c0) + Math.sin(a0) * Math.cos(w0)));
        g0 /= z2;
        if (g0 < 0.0)
            g0 += 180.0;
        if (b0 - g0 > 75.0)
            g0 += 180.0;
        g0 -= r0;
        if (g0 < 0.0)
            g0 += 360.0;
        if (g0 > 360.0)
            g0 -= 360.0;
        return g0;
    }

    private void vimst() {
        double d0, n0, aa, ee, ff, gg, hh;
        int q, dd, bb, cc;

        d0 = plnt[7];
        d0 = 9.0 * fract(d0 / 120);
        n0 = fract(d0);
        q = (int) d0;
        aa = (1 - n0) * yy[q];
        bb = (int) aa;
        ee = aa - bb;
        ff = ee * 12;
        cc = (int) ff;
        gg = ff - cc;
        hh = gg * 30;
        dd = (int) hh;

//        System.out.println("Maha Dasha Balance = Years    Months    Days\n");
//        System.out.println(x1[q] + "  " + bb + "  " + cc + "  " + dd);

        List<DashaBalance> dashaBalances = new ArrayList<>();

        DashaBalance dashaBalance = new DashaBalance();
        dashaBalance.setPlanet(x1[q]);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
        dashaBalance.setStartDate(LocalDate.parse(birthDate, formatter));
        dashaBalance.setDuration(Period.of(bb, cc, dd));
        dashaBalance.setEndDate(dashaBalance.getStartDate().plus(dashaBalance.getDuration()));

        dashaBalances.add(dashaBalance);

        kendara.setDashaBalances(dashaBalances);
    }

    private void nakath() {
        int na, part;
        double naksha;

        naksha = plnt[7] * 3 / 40;
        tt[1] = naksha;
        part = (int) (4 * fract(tt[1]) + 1);
        na = (int) tt[1];

        kendara.setNekatha(nakath[na]);
        kendara.setNekathPada(part);

        /*System.out.println("NAKATHA     : " + nakath[na]);
        System.out.println("NAKATH PADA : " + part);*/
    }

    @Transactional
    public void calc() {
        try {
            long d, m, y, j, h, mt, i;
            int latdeg, latmt, longdeg, longmt, an, bn, cn, anx, bnx, cnx, dnx, enx, fnx;
            double aa1, bb1, cc1, aax, bbx, ccx, ddx, eex, ffx;
            String ns, ew;

            List<LagnaSquare> squares = new ArrayList<>();

            ps = 0.0;
            pt = 0.0;
            z1 = 3.14159265359;
            z2 = z1 / 180;
            s1 = 99.99826;

            //BufferedReader bufferRead = new BufferedReader(new InputStreamReader(System.in));

            // System.out.println("Enter Your Birth Day (Day.Month.Year)  Ex:- 1979/03/16 =16.3.1979");
            // String date = bufferRead.readLine();
            birthDate = "26.02.2019";
            d = Long.parseLong(birthDate.split("\\.")[0]);
            m = Long.parseLong(birthDate.split("\\.")[1]);
            y = Long.parseLong(birthDate.split("\\.")[2]);

            //System.out.println("Enter Birth Time (h.mt) (24 hour)  Ex:- 10:31AM=10.31 , 10.31PM=22.31");
            //String time = bufferRead.readLine();
            String time = "02.24";
            h = Long.parseLong(time.split("\\.")[0]);
            mt = Long.parseLong(time.split("\\.")[1]);

            //System.out.println("Enter Birth city Latitude(latdeg.latmt n/s:)  Ex :- Kalutara=6.22.n");
            //String latt = bufferRead.readLine();
            String latt = "6.31.n";
            latdeg = Integer.parseInt(latt.split("\\.")[0]);
            latmt = Integer.parseInt(latt.split("\\.")[1]);
            ns = latt.split("\\.")[2];

            //System.out.println("Enter Birth city Longitude(longdeg.longmt e/w:)  Ex:- Kalutara=79.58.e");
            //String longi = bufferRead.readLine();
            String longi = "80.84.e";
            longdeg = Integer.parseInt(longi.split("\\.")[0]);
            longmt = Integer.parseInt(longi.split("\\.")[1]);
            ew = longi.split("\\.")[2];

            lat = (double) latdeg + (double) latmt / 60;
            if (ns.equals('S') || ns.equals('s'))
                lat = -lat;
            longt = (double) longdeg + (double) longmt / 60;
            if (ew.equals('W') || ew.equals('w'))
                longt = -longt;

            j = calculateJulianDate(d, m, y);
            h6 = ((double) h + (double) mt / 60 - 17.5) / 24;
            b6 = (j - 694025 + h6) / 36525;
            j = (j + 4) % 7;

            //System.out.println("Day is  : " + day[(int) j]);
            kendara.setPersonName("Sayul");
            kendara.setBirthDay(day[(int) j]);

            ayan();
            calculateSun();
            calculateMercury();
            calculateVenus();
            calculateMars();
            calculateJupiter();
            calculateSaturn();
            calculateMoon();
            bhav();

            aax = plnt[1];
            bbx = plnt[2];
            ccx = plnt[3];
            ddx = plnt[4];
            eex = plnt[5];
            ffx = plnt[6];
            aa1 = plnt[7];
            bb1 = plnt[8];
            cc1 = plnt[9];

            anx = (int) (aax / 30);
            squares.add(new LagnaSquare(anx, aax, "Ravi", rashi[anx]));
            bnx = (int) (bbx / 30);
            squares.add(new LagnaSquare(bnx, bbx, "Budha", rashi[bnx]));
            cnx = (int) (ccx / 30);
            squares.add(new LagnaSquare(cnx, ccx, "Sikuru", rashi[cnx]));
            dnx = (int) (ddx / 30);
            squares.add(new LagnaSquare(dnx, ddx, "Kuja", rashi[dnx]));
            enx = (int) (eex / 30);
            squares.add(new LagnaSquare(enx, eex, "Guru", rashi[enx]));
            fnx = (int) (ffx / 30);
            squares.add(new LagnaSquare(fnx, ffx, "Shani", rashi[fnx]));

            an = (int) (aa1 / 30);
            squares.add(new LagnaSquare(an, aa1, "Chandra", rashi[an]));
            bn = (int) (bb1 / 30);
            squares.add(new LagnaSquare(bn, bb1, "Rahu", rashi[bn]));
            cn = (int) (cc1 / 30);
            squares.add(new LagnaSquare(cn, cc1, "Ketu", rashi[cn]));

            for (LagnaSquare square : squares) {
                if (square.getIndex() < (int) LG) {
                    square.setIndex(square.getIndex() + 12 - (int) LG + 1);
                } else {
                    square.setIndex(square.getIndex() - (int) LG + 1);
                }
            }

            for (LagnaSquare square : squares) {
                if (square.getIndex() == 1) {
                    if (kendara.getSq1() == null) {
                        kendara.setSq1(new ArrayList<>());
                    }
                    kendara.getSq1().add(square);
                }
                if (square.getIndex() == 2) {
                    if (kendara.getSq2() == null) {
                        kendara.setSq2(new ArrayList<>());
                    }
                    kendara.getSq2().add(square);
                }
                if (square.getIndex() == 3) {
                    if (kendara.getSq3() == null) {
                        kendara.setSq3(new ArrayList<>());
                    }
                    kendara.getSq3().add(square);
                }
                if (square.getIndex() == 4) {
                    if (kendara.getSq4() == null) {
                        kendara.setSq4(new ArrayList<>());
                    }
                    kendara.getSq4().add(square);
                }
                if (square.getIndex() == 5) {
                    if (kendara.getSq5() == null) {
                        kendara.setSq5(new ArrayList<>());
                    }
                    kendara.getSq5().add(square);
                }
                if (square.getIndex() == 6) {
                    if (kendara.getSq6() == null) {
                        kendara.setSq6(new ArrayList<>());
                    }
                    kendara.getSq6().add(square);
                }
                if (square.getIndex() == 7) {
                    if (kendara.getSq7() == null) {
                        kendara.setSq7(new ArrayList<>());
                    }
                    kendara.getSq7().add(square);
                }
                if (square.getIndex() == 8) {
                    if (kendara.getSq8() == null) {
                        kendara.setSq8(new ArrayList<>());
                    }
                    kendara.getSq8().add(square);
                }
                if (square.getIndex() == 9) {
                    if (kendara.getSq9() == null) {
                        kendara.setSq9(new ArrayList<>());
                    }
                    kendara.getSq9().add(square);
                }
                if (square.getIndex() == 10) {
                    if (kendara.getSq10() == null) {
                        kendara.setSq10(new ArrayList<>());
                    }
                    kendara.getSq10().add(square);
                }
                if (square.getIndex() == 11) {
                    if (kendara.getSq11() == null) {
                        kendara.setSq11(new ArrayList<>());
                    }
                    kendara.getSq11().add(square);
                }
                if (square.getIndex() == 12) {
                    if (kendara.getSq12() == null) {
                        kendara.setSq12(new ArrayList<>());
                    }
                    kendara.getSq12().add(square);
                }
            }

            Sequence sequence = sequenceRepository.findByName("kendara");

            kendara.setId(sequence.getCounter());

            nakath();
            vimst();

            sequence.setCounter(sequence.getCounter() + 1);
            sequenceRepository.save(sequence);

            for (Kendara kendara1 : kendaraRepository.findAll()) {
                kendaraRepository.delete(kendara1);
            }

            kendaraRepository.save(kendara);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Creating Sales Invoice Failed on line " + ex.getStackTrace()[0].getLineNumber() + " : " +
                    ex.getMessage());
            ex.printStackTrace();
        }
    }

}
