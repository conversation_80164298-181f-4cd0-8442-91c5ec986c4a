package lk.bandana.custom.entity.helper;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.Id;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/15/2023
 */

@Getter
@Setter
@AllArgsConstructor
public class LagnaSquare implements Comparable<LagnaSquare>{

    @Id
    int index;

    Double longitude;

    String planet;

    String rashi;

    @Override
    public int compareTo(LagnaSquare s){
        return this.index - s.index;
    }

}
