package lk.bandana.custom.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.stereotype.Component;

@Node
@Component
@Setter
@Getter
public class NekathPorondamLink {

    @Id
    private Integer id;

    private Integer nekathId;

    private String nekatha;

    private String ghana;

    private String yoni;

    private String wruksha;

    private String linga;

    private String nadi;

    private String pakshi;

    private String gothra;

    private String warna;

    private String rajju;

    private String bhootha;
}
