package lk.bandana.custom.entity.helper;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Period;

@Node
@Component
@Setter
@Getter
public class DashaBalance {

    @Id
    private String planet;

    private LocalDate startDate;

    private LocalDate endDate;

    private Period duration;
}
