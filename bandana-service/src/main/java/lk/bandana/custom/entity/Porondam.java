package lk.bandana.custom.entity;

import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.stereotype.Component;

@Node
@Component
@Setter
@Getter
public class Porondam {

    @Id
    private Integer id;

    private String name;

    private String nameSn;
}
