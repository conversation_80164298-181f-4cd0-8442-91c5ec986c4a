package lk.bandana.core.service; import lk.bandana.core.entity.SubscriptionPackage; import lk.bandana.core.entity.UserSubscription; import java.math.BigDecimal; import java.time.LocalDateTime; import java.util.List; import java.util.Optional; public interface SubscriptionService { // Subscription Package Management SubscriptionPackage savePackage(SubscriptionPackage subscriptionPackage); Optional<SubscriptionPackage> findPackageById(Long id); List<SubscriptionPackage> findActivePackages(); List<SubscriptionPackage> findPackagesForUserType(SubscriptionPackage.UserType userType); List<SubscriptionPackage> findAgentPackages(); List<SubscriptionPackage> findRegularUserPackages(); List<SubscriptionPackage> findFeaturedPackages(); // User Subscription Management UserSubscription createSubscription(Long userId, Long packageId, String paymentMethod); UserSubscription renewSubscription(Long subscriptionId); UserSubscription cancelSubscription(Long subscriptionId, String reason); UserSubscription suspendSubscription(Long subscriptionId, String reason); Optional<UserSubscription> findCurrentActiveSubscription(Long userId); List<UserSubscription> findUserSubscriptions(Long userId); boolean hasActiveSubscription(Long userId); // Subscription validation and limits boolean canViewProfile(Long userId); boolean canSendContactRequest(Long userId); boolean canUploadPhoto(Long userId); boolean hasAccessToHighQualityProfiles(Long userId); void incrementProfileViewUsage(Long userId); void incrementContactRequestUsage(Long userId); void incrementPhotoUploadUsage(Long userId); // Billing and renewal List<UserSubscription> findSubscriptionsDueForRenewal(); List<UserSubscription> findExpiringSubscriptions(int daysAhead); UserSubscription processRenewal(Long subscriptionId); // Trial subscriptions UserSubscription createTrialSubscription(Long userId, Long packageId, int trialDays); List<UserSubscription> findTrialSubscriptions(); UserSubscription convertTrialToRegular(Long subscriptionId, String paymentMethod); // Agent-specific features boolean canAccessCrossAgentMatches(Long agentId); int getMatchingThreshold(Long agentId); int getMaxClientProfiles(Long agentId); boolean hasReachedClientLimit(Long agentId); // Subscription analytics Long countActiveSubscriptionsByPackage(Long packageId); BigDecimal calculateMonthlyRevenue(); BigDecimal calculateRevenueByDateRange(LocalDateTime startDate, LocalDateTime endDate); // Package configuration SubscriptionPackage updatePackageFeatures(Long packageId, Integer maxProfileViews, Integer maxContactRequests, Boolean premiumAccess); SubscriptionPackage updatePackagePricing(Long packageId, BigDecimal price, BigDecimal discountPercentage); // Subscription status management void processExpiredSubscriptions(); void sendRenewalReminders(); void handleFailedRenewals(); } 