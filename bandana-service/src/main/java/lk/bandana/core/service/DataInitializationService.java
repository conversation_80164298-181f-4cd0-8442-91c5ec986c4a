package lk.bandana.core.service; import lk.bandana.core.entity.*; import lk.bandana.core.repository.*; import org.springframework.beans.factory.annotation.Autowired; import org.springframework.stereotype.Service; import org.springframework.transaction.annotation.Transactional; import java.math.BigDecimal; import java.util.Arrays; import java.util.List; /** * Service to initialize default data for the matchmaking system */ @Service @Transactional public class DataInitializationService { @Autowired private ProvinceRepository provinceRepository; @Autowired private DistrictRepository districtRepository; @Autowired private InterestRepository interestRepository; @Autowired private SubscriptionPackageRepository subscriptionPackageRepository; @Autowired private SystemConfigurationRepository systemConfigurationRepository; @Autowired private UserRoleRepository userRoleRepository; /** * Initialize all default data */ public void initializeAllData() { initializeProvinces(); initializeInterests(); initializeSubscriptionPackages(); initializeSystemConfigurations(); initializeUserRoles(); } /** * Initialize Sri Lankan provinces and districts */ public void initializeProvinces() { if (provinceRepository.count() > 0) { return; // Already initialized } // Western Province Province western = new Province("Western", "බස්නාහිර", "மேல்", "WP"); western = provinceRepository.save(western); districtRepository.saveAll(Arrays.asList( new District("Colombo", "කොළඹ", "கொழும்பு", "CO", western), new District("Gampaha", "ගම්පහ", "கம்பஹா", "GA", western), new District("Kalutara", "කළුතර", "களுத்துறை", "KA", western) )); // Central Province Province central = new Province("Central", "මධ්‍යම", "மத்திய", "CP"); central = provinceRepository.save(central); districtRepository.saveAll(Arrays.asList( new District("Kandy", "මහනුවර", "கண்டி", "KD", central), new District("Matale", "මාතලේ", "மாத்தளை", "MT", central), new District("Nuwara Eliya", "නුවරඑළිය", "நுவரேலியா", "NE", central) )); // Southern Province Province southern = new Province("Southern", "දකුණු", "தென்", "SP"); southern = provinceRepository.save(southern); districtRepository.saveAll(Arrays.asList( new District("Galle", "ගාල්ල", "காலி", "GL", southern), new District("Matara", "මාතර", "மாத்தறை", "MR", southern), new District("Hambantota", "හම්බන්තොට", "அம்பாந்தோட்டை", "HB", southern) )); // Northern Province Province northern = new Province("Northern", "උතුරු", "வட", "NP"); northern = provinceRepository.save(northern); districtRepository.saveAll(Arrays.asList( new District("Jaffna", "යාපනය", "யாழ்ப்பாணம்", "JA", northern), new District("Kilinochchi", "කිලිනොච්චි", "கிளிநொச்சி", "KC", northern), new District("Mannar", "මන්නාරම", "மன்னார்", "MN", northern), new District("Vavuniya", "වවුනියාව", "வவுனியா", "VV", northern), new District("Mullaitivu", "මුලතිව්", "முல்லைத்தீவு", "MU", northern) )); // Eastern Province Province eastern = new Province("Eastern", "නැගෙනහිර", "கிழக்கு", "EP"); eastern = provinceRepository.save(eastern); districtRepository.saveAll(Arrays.asList( new District("Trincomalee", "ත්‍රිකුණාමලය", "திருகோணமலை", "TR", eastern), new District("Batticaloa", "මඩකලපුව", "மட்டக்களப்பு", "BC", eastern), new District("Ampara", "අම්පාර", "அம்பாறை", "AP", eastern) )); // North Western Province Province northWestern = new Province("North Western", "වයඹ", "வட மேல்", "NW"); northWestern = provinceRepository.save(northWestern); districtRepository.saveAll(Arrays.asList( new District("Kurunegala", "කුරුණෑගල", "குருனாகல்", "KU", northWestern), new District("Puttalam", "පුත්තලම", "புத்தளம்", "PU", northWestern) )); // North Central Province Province northCentral = new Province("North Central", "උතුරු මැද", "வட மத்திய", "NC"); northCentral = provinceRepository.save(northCentral); districtRepository.saveAll(Arrays.asList( new District("Anuradhapura", "අනුරාධපුරය", "அனுராதபுரம்", "AD", northCentral), new District("Polonnaruwa", "පොළොන්නරුව", "பொலன்னறுவை", "PL", northCentral) )); // Uva Province Province uva = new Province("Uva", "ඌව", "ஊவா", "UV"); uva = provinceRepository.save(uva); districtRepository.saveAll(Arrays.asList( new District("Badulla", "බදුල්ල", "பதுளை", "BD", uva), new District("Monaragala", "මොණරාගල", "மொணராகலை", "MG", uva) )); // Sabaragamuwa Province Province sabaragamuwa = new Province("Sabaragamuwa", "සබරගමුව", "சபரகமுவ", "SG"); sabaragamuwa = provinceRepository.save(sabaragamuwa); districtRepository.saveAll(Arrays.asList( new District("Ratnapura", "රත්නපුර", "இரத்தினபுரி", "RP", sabaragamuwa), new District("Kegalle", "කෑගල්ල", "கேகாலை", "KG", sabaragamuwa) )); } /** * Initialize interest categories and options */ public void initializeInterests() { if (interestRepository.count() > 0) { return; // Already initialized } List<Interest> interests = Arrays.asList( // Sports new Interest("Cricket", "SPORTS", "Playing or watching cricket"), new Interest("Football", "SPORTS", "Playing or watching football"), new Interest("Tennis", "SPORTS", "Playing or watching tennis"), new Interest("Swimming", "SPORTS", "Swimming as exercise or recreation"), new Interest("Badminton", "SPORTS", "Playing badminton"), new Interest("Volleyball", "SPORTS", "Playing volleyball"), // Music new Interest("Classical Music", "MUSIC", "Listening to or playing classical music"), new Interest("Pop Music", "MUSIC", "Listening to popular music"), new Interest("Traditional Music", "MUSIC", "Sri Lankan traditional music"), new Interest("Playing Piano", "MUSIC", "Playing piano"), new Interest("Playing Guitar", "MUSIC", "Playing guitar"), new Interest("Singing", "MUSIC", "Vocal music and singing"), // Arts & Culture new Interest("Painting", "ARTS", "Creating visual art through painting"), new Interest("Dancing", "ARTS", "Traditional or modern dancing"), new Interest("Photography", "ARTS", "Taking and editing photographs"), new Interest("Reading", "ARTS", "Reading books and literature"), new Interest("Writing", "ARTS", "Creative writing and blogging"), new Interest("Theater", "ARTS", "Watching or participating in theater"), // Travel new Interest("Domestic Travel", "TRAVEL", "Traveling within Sri Lanka"), new Interest("International Travel", "TRAVEL", "Traveling to other countries"), new Interest("Adventure Travel", "TRAVEL", "Hiking, trekking, adventure sports"), new Interest("Cultural Tourism", "TRAVEL", "Visiting historical and cultural sites"), // Food & Cooking new Interest("Cooking", "FOOD", "Preparing and cooking meals"), new Interest("Baking", "FOOD", "Baking cakes, bread, and pastries"), new Interest("Sri Lankan Cuisine", "FOOD", "Traditional Sri Lankan cooking"), new Interest("International Cuisine", "FOOD", "Cooking international dishes"), // Technology new Interest("Programming", "TECHNOLOGY", "Software development and coding"), new Interest("Gaming", "TECHNOLOGY", "Video games and online gaming"), new Interest("Social Media", "TECHNOLOGY", "Using social media platforms"), // Fitness & Health new Interest("Yoga", "FITNESS", "Practicing yoga for health and wellness"), new Interest("Gym Workouts", "FITNESS", "Regular gym and fitness training"), new Interest("Running", "FITNESS", "Running for exercise and competition"), new Interest("Meditation", "FITNESS", "Mindfulness and meditation practices"), // Hobbies new Interest("Gardening", "HOBBIES", "Growing plants and maintaining gardens"), new Interest("Collecting", "HOBBIES", "Collecting stamps, coins, or other items"), new Interest("Crafts", "HOBBIES", "Handicrafts and DIY projects"), new Interest("Board Games", "HOBBIES", "Playing board games and puzzles") ); interestRepository.saveAll(interests); } /** * Initialize subscription packages */ public void initializeSubscriptionPackages() { if (subscriptionPackageRepository.count() > 0) { return; // Already initialized } List<SubscriptionPackage> packages = Arrays.asList( // Regular User Packages createRegularUserPackage("Basic", "Basic matchmaking features", new BigDecimal("1500"), 30, 1, 10, 5, 3, false), createRegularUserPackage("Premium", "Enhanced matchmaking with premium features", new BigDecimal("3500"), 30, 2, 50, 20, 10, true), createRegularUserPackage("Elite", "Complete matchmaking experience", new BigDecimal("6500"), 30, 3, 200, 100, 25, true), // Agent Packages createAgentPackage("Agent Monthly", "Monthly agent subscription", new BigDecimal("15000"), 30, 10, 80), createAgentPackage("Agent Annual", "Annual agent subscription with discount", new BigDecimal("150000"), 365, 25, 80) ); subscriptionPackageRepository.saveAll(packages); } /** * Initialize system configurations */ public void initializeSystemConfigurations() { if (systemConfigurationRepository.count() > 0) { return; // Already initialized } List<SystemConfiguration> configs = Arrays.asList( new SystemConfiguration(SystemConfiguration.AGENT_MATCH_THRESHOLD, "80", "Minimum compatibility score for agents to view cross-agent matches", SystemConfiguration.ConfigType.INTEGER), new SystemConfiguration(SystemConfiguration.PROFILE_APPROVAL_REQUIRED, "true", "Whether profile approval by admin is required", SystemConfiguration.ConfigType.BOOLEAN), new SystemConfiguration(SystemConfiguration.SMS_VERIFICATION_ENABLED, "false", "Whether SMS verification is enabled for phone numbers", SystemConfiguration.ConfigType.BOOLEAN), new SystemConfiguration(SystemConfiguration.MAX_PROFILE_PHOTOS, "5", "Maximum number of photos per profile", SystemConfiguration.ConfigType.INTEGER), new SystemConfiguration(SystemConfiguration.DEFAULT_SUBSCRIPTION_DAYS, "30", "Default subscription duration in days", SystemConfiguration.ConfigType.INTEGER), new SystemConfiguration(SystemConfiguration.PAYMENT_GATEWAY_ENABLED, "true", "Whether online payment gateway is enabled", SystemConfiguration.ConfigType.BOOLEAN), new SystemConfiguration(SystemConfiguration.HOROSCOPE_MATCHING_WEIGHT, "20", "Weight percentage for horoscope compatibility in matching", SystemConfiguration.ConfigType.INTEGER), new SystemConfiguration(SystemConfiguration.AUTO_PROFILE_APPROVAL, "false", "Whether profiles are automatically approved", SystemConfiguration.ConfigType.BOOLEAN), new SystemConfiguration(SystemConfiguration.CONTACT_REQUEST_LIMIT, "10", "Default contact request limit for basic users", SystemConfiguration.ConfigType.INTEGER), new SystemConfiguration(SystemConfiguration.PROFILE_VIEW_LIMIT, "20", "Default profile view limit for basic users", SystemConfiguration.ConfigType.INTEGER) ); systemConfigurationRepository.saveAll(configs); } /** * Initialize user roles */ public void initializeUserRoles() { if (userRoleRepository.count() > 0) { return; // Already initialized } List<UserRole> roles = Arrays.asList( new UserRole(UserRole.ROLE_USER, "Regular User", 1), new UserRole(UserRole.ROLE_AGENT, "Match Maker Agent", 2), new UserRole(UserRole.ROLE_ADMIN, "Administrator", 3), new UserRole(UserRole.ROLE_SUPER_ADMIN, "Super Administrator", 4) ); userRoleRepository.saveAll(roles); } // Helper methods private SubscriptionPackage createRegularUserPackage(String name, String description, BigDecimal price, Integer durationDays, Integer qualityTier, Integer maxProfileViews, Integer maxContactRequests, Integer maxPhotoUploads, Boolean premiumAccess) { SubscriptionPackage pkg = new SubscriptionPackage(); pkg.setPackageName(name); pkg.setDescription(description); pkg.setPrice(price); pkg.setDurationDays(durationDays); pkg.setPackageType(SubscriptionPackage.PackageType.valueOf(name.toUpperCase())); pkg.setTargetUserType(SubscriptionPackage.UserType.REGULAR_USER); pkg.setQualityTier(qualityTier); pkg.setMaxProfileViews(maxProfileViews); pkg.setMaxContactRequests(maxContactRequests); pkg.setMaxPhotoUploads(maxPhotoUploads); pkg.setPremiumMatchAccess(premiumAccess); pkg.setBillingCycle(SubscriptionPackage.BillingCycle.MONTHLY); return pkg; } private SubscriptionPackage createAgentPackage(String name, String description, BigDecimal price, Integer durationDays, Integer maxClientProfiles, Integer matchingThreshold) { SubscriptionPackage pkg = new SubscriptionPackage(); pkg.setPackageName(name); pkg.setDescription(description); pkg.setPrice(price); pkg.setDurationDays(durationDays); pkg.setPackageType(name.contains("Monthly") ? SubscriptionPackage.PackageType.AGENT_MONTHLY : SubscriptionPackage.PackageType.AGENT_ANNUAL); pkg.setTargetUserType(SubscriptionPackage.UserType.AGENT); pkg.setMaxClientProfiles(maxClientProfiles); pkg.setCrossAgentMatching(true); pkg.setMatchingThreshold(matchingThreshold); pkg.setBillingCycle(name.contains("Monthly") ? SubscriptionPackage.BillingCycle.MONTHLY : SubscriptionPackage.BillingCycle.ANNUAL); return pkg; } } 