package lk.bandana.core.service.impl;

import lk.bandana.core.entity.Action;
import lk.bandana.core.entity.Response;
import lk.bandana.core.repository.ActionRepository;
import lk.bandana.core.service.ActionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ActionServiceImpl implements ActionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ActionService.class);
    @Autowired
    ActionRepository actionRepository;
    @Autowired
    Response response;

    public boolean save(Action action) {
        try {
            actionRepository.save(action);
            response.setCode(200);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Creating Action Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Action Failed");
            response.setData(ex.getMessage());
            return false;
        }
    }
}