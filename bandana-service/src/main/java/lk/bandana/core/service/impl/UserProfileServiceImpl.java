package lk.bandana.core.service.impl;

import lk.bandana.core.entity.UserProfile;

import lk.bandana.core.repository.UserProfileRepository;

import lk.bandana.core.service.UserProfileService;

import org.slf4j.Logger;

import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.data.domain.Page;

import org.springframework.data.domain.Pageable;

import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import java.util.ArrayList;

import java.util.List;

import java.util.Optional;

@
@

public class UserProfileServiceImpl implements UserProfileService {

    private static final Logger logger = LoggerFactory.getLogger(UserProfileServiceImpl.class);

    @
    private UserProfileRepository userProfileRepository;

    @
    public UserProfile save(UserProfile userProfile) {

        try {

            Calculate completion percentage before saving userProfile.setCompletionPercentage(calculateCompletionPercentage(userProfile));

            Set default status if not set
            if (userProfile.getStatus() == null) {
                userProfile.setStatus(UserProfile.ProfileStatus.PENDING);
            }

            return userProfileRepository.save(userProfile);
        }

    } catch (Exception e) {
        logger.error("Error saving user profile: {
        }
        ", e.getMessage(), e);
        throw new RuntimeException("Failed to save user profile", e);
    }
}

@
public Optional<UserProfile> findById(Long id) {

    return userProfileRepository.findById(id);
}

@
public List<UserProfile> findAll() {
    List<UserProfile> profiles = new ArrayList<>();
    userProfileRepository.findAll().forEach(profiles::add);

    return profiles;
}

@
public void deleteById(Long id) {
    userProfileRepository.deleteById(id);
}

@
public UserProfile approveProfile(Long profileId, String approvedBy, String comments) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();
        profile.setStatus(UserProfile.ProfileStatus.APPROVED);
        profile.setApprovedBy(approvedBy);
        profile.setApprovedDate(LocalDateTime.now());
        profile.setAdminComments(comments);
        logger.info("Profile {
        }
        approved by {
        }
        ", profileId, approvedBy);

        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public UserProfile rejectProfile(Long profileId, String rejectedBy, String reason) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();
        profile.setStatus(UserProfile.ProfileStatus.REJECTED);
        profile.setRejectionReason(reason);
        profile.setAdminComments(reason);
        logger.info("Profile {
        }
        rejected by {
        }
        - Reason: {
        }
        ", profileId, rejectedBy, reason);

        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public UserProfile suspendProfile(Long profileId, String suspendedBy, String reason) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();
        profile.setStatus(UserProfile.ProfileStatus.SUSPENDED);
        profile.setAdminComments(reason);
        logger.info("Profile {
        }
        suspended by {
        }
        - Reason: {
        }
        ", profileId, suspendedBy, reason);

        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public List<UserProfile> findProfilesPendingApproval() {

    return userProfileRepository.findProfilesPendingApproval();
}

@
public Page<UserProfile> searchProfiles(String gender, Integer minAge, Integer maxAge, Integer minHeight, Integer maxHeight, String province, String district, String education, String occupation, String religion, Pageable pageable) {

    return userProfileRepository.searchProfiles(gender, minAge, maxAge, minHeight, maxHeight, pageable);
}

@
public List<UserProfile> findApprovedProfiles() {

    return userProfileRepository.findApprovedAndVisibleProfiles();
}

@
public List<UserProfile> findApprovedProfilesByGender(String gender) {

    return userProfileRepository.findApprovedAndVisibleProfilesByGender(gender);
}

@
public List<UserProfile> findHighQualityProfiles(Integer minScore) {

    return userProfileRepository.findHighQualityProfiles(minScore);
}

@
public List<UserProfile> findProfilesManagedByAgent(Long agentId) {

    return userProfileRepository.findProfilesManagedByAgent(agentId);
}

@
public UserProfile assignProfileToAgent(Long profileId, Long agentId) {

    Implementation would involve setting the managedByAgent relationship
    This is a simplified version Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();
        profile.setIsAgentManaged(true);

        Set agent relationship here
        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public UserProfile removeProfileFromAgent(Long profileId) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();
        profile.setIsAgentManaged(false);

        Remove agent relationship here
        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public UserProfile calculateProfileCompletion(Long profileId) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();
        int completionPercentage = calculateCompletionPercentage(profile);
        profile.setCompletionPercentage(completionPercentage);

        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public UserProfile updateProfileQualityScore(Long profileId) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();
        int qualityScore = calculateQualityScore(profile);
        profile.setProfileQualityScore(qualityScore);

        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public List<UserProfile> findIncompleteProfiles() {

    return userProfileRepository.findIncompleteProfiles();
}

@
public List<UserProfile> findByProvince(String provinceName) {

    return userProfileRepository.findByProvince(provinceName);
}

@
public List<UserProfile> findByDistrict(String districtName) {

    return userProfileRepository.findByDistrict(districtName);
}

@
public Long countProfilesByStatus(UserProfile.ProfileStatus status) {

    return userProfileRepository.countByStatus(status.name());
}

@
public List<UserProfile> findRecentProfiles(int days) {
    LocalDateTime since = LocalDateTime.now().minusDays(days);

    return userProfileRepository.findRecentProfiles(since.toString());
}

@
public List<UserProfile> findProfilesWithPhotos() {

    return userProfileRepository.findProfilesWithPhotos();
}

@
public boolean validateProfileData(UserProfile userProfile) {

    return getProfileValidationErrors(userProfile).isEmpty();
}

@
public List<String> getProfileValidationErrors(UserProfile userProfile) {
    List<String> errors = new ArrayList<>();

    if (userProfile.getFullName() == null || userProfile.getFullName().trim().isEmpty()) {
        errors.add("Full name is required");
    }

    if (userProfile.getDateOfBirth() == null) {
        errors.add("Date of birth is required");
    }

    if (userProfile.getGender() == null || userProfile.getGender().trim().isEmpty()) {
        errors.add("Gender is required");
    }

    if (userProfile.getHeightCm() == null || userProfile.getHeightCm() < 100 || userProfile.getHeightCm() > 250) {
        errors.add("Valid height is required (100-250 cm)");
    }

    Add more validation rules as needed
    return errors;
}

@
public UserProfile updateProfilePhoto(Long profileId, String photoUrl) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();
        profile.setProfilePhotoUrl(photoUrl);

        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public UserProfile addAdditionalPhoto(Long profileId, String photoUrl) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();

        if (profile.getAdditionalPhotoUrls() == null) {
            profile.setAdditionalPhotoUrls(new ArrayList<>());
        }
        profile.getAdditionalPhotoUrls().add(photoUrl);

        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public UserProfile removePhoto(Long profileId, String photoUrl) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();

        if (profile.getProfilePhotoUrl() != null && profile.getProfilePhotoUrl().equals(photoUrl)) {
            profile.setProfilePhotoUrl(null);
        }

        if (profile.getAdditionalPhotoUrls() != null) {
            profile.getAdditionalPhotoUrls().remove(photoUrl);
        }

        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public UserProfile updateContactInformation(Long profileId, String email, String phone, String whatsapp) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();
        profile.setContactEmail(email);
        profile.setContactPhone(phone);
        profile.setWhatsappNumber(whatsapp);

        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

@
public UserProfile updatePrivacySettings(Long profileId, Boolean profileVisible, Boolean contactInfoVisible, Boolean photoVisible, Boolean casteVisible) {
    Optional<UserProfile> profileOpt = userProfileRepository.findById(profileId);

    if (profileOpt.isPresent()) {
        UserProfile profile = profileOpt.get();
        profile.setProfileVisible(profileVisible);
        profile.setContactInfoVisible(contactInfoVisible);
        profile.setPhotoVisible(photoVisible);
        profile.setCasteVisible(casteVisible);

        return userProfileRepository.save(profile);
    }
    throw new RuntimeException("Profile not found: " + profileId);
}

Helper methods
private int calculateCompletionPercentage(UserProfile profile) {
    int totalFields = 20;

    Total number of important fields int completedFields = 0;

    if (profile.getFullName() != null && !profile.getFullName().trim().isEmpty()) completedFields++;

    if (profile.getDateOfBirth() != null) completedFields++;

    if (profile.getGender() != null && !profile.getGender().trim().isEmpty()) completedFields++;

    if (profile.getHeightCm() != null) completedFields++;

    if (profile.getWeightKg() != null) completedFields++;

    if (profile.getProvince() != null) completedFields++;

    if (profile.getDistrict() != null) completedFields++;

    if (profile.getOccupation() != null && !profile.getOccupation().trim().isEmpty()) completedFields++;

    if (profile.getEducationLevel() != null && !profile.getEducationLevel().trim().isEmpty()) completedFields++;

    if (profile.getReligion() != null && !profile.getReligion().trim().isEmpty()) completedFields++;

    if (profile.getMotherTongue() != null && !profile.getMotherTongue().trim().isEmpty()) completedFields++;

    if (profile.getMaritalStatus() != null && !profile.getMaritalStatus().trim().isEmpty()) completedFields++;

    if (profile.getProfilePhotoUrl() != null && !profile.getProfilePhotoUrl().trim().isEmpty()) completedFields++;

    if (profile.getAboutMe() != null && !profile.getAboutMe().trim().isEmpty()) completedFields++;

    if (profile.getPartnerExpectations() != null && !profile.getPartnerExpectations().trim().isEmpty()) completedFields++;

    if (profile.getContactEmail() != null && !profile.getContactEmail().trim().isEmpty()) completedFields++;

    if (profile.getContactPhone() != null && !profile.getContactPhone().trim().isEmpty()) completedFields++;

    if (profile.getFatherOccupation() != null && !profile.getFatherOccupation().trim().isEmpty()) completedFields++;

    if (profile.getMotherOccupation() != null && !profile.getMotherOccupation().trim().isEmpty()) completedFields++;

    if (profile.getInterests() != null && !profile.getInterests().isEmpty()) completedFields++;

    return (completedFields * 100) / totalFields;
}

private int calculateQualityScore(UserProfile profile) {
    int score = 0;

    Basic information completeness (40 points)
    if (profile.getCompletionPercentage() >= 90) score += 40;
    else
    if (profile.getCompletionPercentage() >= 70) score += 30;
    else
    if (profile.getCompletionPercentage() >= 50) score += 20;
    else score += 10;

    Photo quality (20 points)
    if (profile.getProfilePhotoUrl() != null) {
        score += 10;

        if (profile.getAdditionalPhotoUrls() != null && profile.getAdditionalPhotoUrls().size() >= 2) {
            score += 10;
        }
    }

    Profile description quality (20 points)
    if (profile.getAboutMe() != null && profile.getAboutMe().length() >= 100) score += 10;

    if (profile.getPartnerExpectations() != null && profile.getPartnerExpectations().length() >= 50) score += 10;

    Verification status (20 points)
    if (profile.getStatus() == UserProfile.ProfileStatus.APPROVED) score += 20;

    return Math.min(score, 100);

    Cap at 100 }
}
