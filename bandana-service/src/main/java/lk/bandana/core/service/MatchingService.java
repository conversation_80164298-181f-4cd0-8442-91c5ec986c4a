package lk.bandana.core.service;

import lk.bandana.core.entity.CompatibilityMatch;

import lk.bandana.core.entity.UserProfile;

import org.springframework.data.domain.Page;

import org.springframework.data.domain.Pageable;

import java.util.List;

import java.util.Map;

import java.util.Optional;

/**
 * Service interface for Compatibility Matching
 * Handles profile matching algorithms and compatibility calculations
 */
public interface MatchingService {

    // Core matching functionality
    CompatibilityMatch calculateCompatibility(Long profileId1, Long profileId2);
    List<CompatibilityMatch> findMatchesForProfile(Long profileId);
    List<CompatibilityMatch> findHighQualityMatches(Long profileId, Integer minScore);

    // Batch matching operations
    void calculateMatchesForProfile(Long profileId);
    void recalculateAllMatches();
    void recalculateMatchesForProfile(Long profileId);

    // Agent-specific matching
    List<CompatibilityMatch> findMatchesForAgentClients(Long agentId, Integer minScore);
    List<CompatibilityMatch> findCrossAgentMatches(Integer minScore);
    boolean isMatchVisibleToAgent(Long matchId, Long agentId);

    // Match retrieval and filtering
    Optional<CompatibilityMatch> findMatchBetweenProfiles(Long profileId1, Long profileId2);
    List<CompatibilityMatch> findMatchesByScoreRange(Integer minScore, Integer maxScore);
    Page<CompatibilityMatch> findMatchesForPremiumUsers(Integer minTier, Pageable pageable);

    // Match interaction tracking
    CompatibilityMatch markProfileViewed(Long matchId, Long viewerProfileId);
    CompatibilityMatch sendContactRequest(Long matchId, Long senderProfileId);
    CompatibilityMatch respondToContactRequest(Long matchId, boolean accepted);

    // Compatibility scoring
    Integer calculateOverallScore(Map<String, Integer> categoryScores, Map<String, Integer> weights);
    Integer calculateAgeCompatibility(UserProfile profile1, UserProfile profile2);
    Integer calculateLocationCompatibility(UserProfile profile1, UserProfile profile2);
    Integer calculateEducationCompatibility(UserProfile profile1, UserProfile profile2);
    Integer calculateOccupationCompatibility(UserProfile profile1, UserProfile profile2);
    Integer calculateReligionCompatibility(UserProfile profile1, UserProfile profile2);
    Integer calculateInterestCompatibility(UserProfile profile1, UserProfile profile2);
    Integer calculateLifestyleCompatibility(UserProfile profile1, UserProfile profile2);
    Integer calculateFamilyCompatibility(UserProfile profile1, UserProfile profile2);

    // Horoscope compatibility (integration with existing system)
    Integer calculateHoroscopeCompatibility(Long profileId1, Long profileId2);

    // Match configuration and weights
    Map<String, Integer> getDefaultCategoryWeights();
    Map<String, Integer> getUserCategoryWeights(Long userId);
    void updateUserCategoryWeights(Long userId, Map<String, Integer> weights);

    // Match visibility and access control
    List<CompatibilityMatch> getVisibleMatches(Long userId, Integer subscriptionTier);
    boolean canAccessMatch(Long userId, Long matchId);
    void updateMatchVisibility(Long matchId, boolean visible, boolean visibleToAgents);

    // Match statistics and analytics
    Long countMatchesForProfile(Long profileId);
    Double getAverageMatchScore(Long profileId);
    List<CompatibilityMatch> getTopMatches(Long profileId, int limit);

    // Match maintenance
    List<CompatibilityMatch> findMatchesNeedingRecalculation();
    void markMatchForRecalculation(Long matchId);
    void cleanupOldMatches(int daysOld);

    // Preference-based matching
    List<CompatibilityMatch> findMatchesBasedOnPreferences(Long profileId);
    boolean matchesUserPreferences(UserProfile profile, UserProfile candidate);

    // Advanced matching algorithms
    List<CompatibilityMatch> findSimilarProfiles(Long profileId, int limit);
    List<CompatibilityMatch> findComplementaryProfiles(Long profileId, int limit);
}
