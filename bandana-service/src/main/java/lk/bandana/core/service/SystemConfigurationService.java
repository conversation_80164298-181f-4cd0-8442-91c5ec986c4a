package lk.bandana.core.service;

import lk.bandana.core.entity.SystemConfiguration;

import java.util.List;

import java.util.Optional;

public interface SystemConfigurationService {

    Basic CRUD operations SystemConfiguration save(SystemConfiguration config);
    Optional<SystemConfiguration> findById(Long id);
    Optional<SystemConfiguration> findByKey(String configKey);
    List<SystemConfiguration> findAll();
    List<SystemConfiguration> findByCategory(String category);
    void deleteById(Long id);

    Configuration value retrieval with type safety String getStringValue(String configKey);
    String getStringValue(String configKey, String defaultValue);
    Integer getIntegerValue(String configKey);
    Integer getIntegerValue(String configKey, Integer defaultValue);
    Boolean getBooleanValue(String configKey);
    Boolean getBooleanValue(String configKey, Boolean defaultValue);
    Double getDoubleValue(String configKey);
    Double getDoubleValue(String configKey, Double defaultValue);

    Configuration value updates SystemConfiguration updateStringValue(String configKey, String value);
    SystemConfiguration updateIntegerValue(String configKey, Integer value);
    SystemConfiguration updateBooleanValue(String configKey, Boolean value);
    SystemConfiguration updateDoubleValue(String configKey, Double value);

    Specific configuration getters for common settings Integer getAgentMatchThreshold();
    Boolean isProfileApprovalRequired();
    Boolean isSmsVerificationEnabled();
    Integer getMaxProfilePhotos();
    Integer getDefaultSubscriptionDays();
    Boolean isPaymentGatewayEnabled();
    Integer getHoroscopeMatchingWeight();
    Boolean isAutoProfileApprovalEnabled();
    Integer getContactRequestLimit();
    Integer getProfileViewLimit();

    Specific configuration setters void setAgentMatchThreshold(Integer threshold);
    void setProfileApprovalRequired(Boolean required);
    void setSmsVerificationEnabled(Boolean enabled);
    void setMaxProfilePhotos(Integer maxPhotos);
    void setDefaultSubscriptionDays(Integer days);
    void setPaymentGatewayEnabled(Boolean enabled);
    void setHoroscopeMatchingWeight(Integer weight);
    void setAutoProfileApprovalEnabled(Boolean enabled);
    void setContactRequestLimit(Integer limit);
    void setProfileViewLimit(Integer limit);

    Configuration validation boolean validateConfigValue(String configKey, String value);
    List<String> getValidationErrors(String configKey, String value);

    Configuration management List<SystemConfiguration> findVisibleConfigurations();
    SystemConfiguration createDefaultConfiguration(String configKey, String defaultValue, String description, SystemConfiguration.ConfigType type);
    void initializeDefaultConfigurations();
    void resetToDefaults();

    Configuration categories List<String> getAllCategories();
    List<SystemConfiguration> findConfigurationsByType(SystemConfiguration.ConfigType type);

    Configuration backup and restore String exportConfigurations();
    void importConfigurations(String configData);
}
