package lk.bandana.core.service; import lk.bandana.core.entity.Payment; import java.math.BigDecimal; import java.time.LocalDateTime; import java.util.List; import java.util.Optional; public interface PaymentService { // Payment processing Payment createPayment(Long userId, Long subscriptionId, BigDecimal amount, Payment.PaymentMethod paymentMethod); Payment processPayment(Long paymentId, String gatewayTransactionId, String gatewayResponse); Payment markPaymentCompleted(Long paymentId, String transactionId); Payment markPaymentFailed(Long paymentId, String failureReason, String failureCode); // Payment retrieval Optional<Payment> findById(Long id); Optional<Payment> findByTransactionId(String transactionId); Optional<Payment> findByGatewayTransactionId(String gatewayTransactionId); List<Payment> findByUserId(Long userId); List<Payment> findByStatus(Payment.PaymentStatus status); // Payment retry and recovery List<Payment> findFailedPaymentsForRetry(); Payment retryPayment(Long paymentId); void processFailedPayments(); // Refund management Payment processRefund(Long paymentId, BigDecimal refundAmount, String reason); Payment processPartialRefund(Long paymentId, BigDecimal refundAmount, String reason); List<Payment> findRefundedPayments(); // Payment analytics and reporting BigDecimal calculateTotalRevenue(); BigDecimal calculateRevenueByDateRange(LocalDateTime startDate, LocalDateTime endDate); BigDecimal calculateRevenueByPaymentMethod(Payment.PaymentMethod paymentMethod); Long countPaymentsByStatus(Payment.PaymentStatus status); List<Payment> findPaymentsByDateRange(LocalDateTime startDate, LocalDateTime endDate); // Payment validation boolean validatePaymentData(Payment payment); List<String> getPaymentValidationErrors(Payment payment); // Invoice management Payment generateInvoice(Long paymentId); String getInvoiceUrl(Long paymentId); // Payment gateway integration String initiateGatewayPayment(Payment payment); Payment handleGatewayCallback(String gatewayTransactionId, String status, String response); // Subscription payment handling Payment processSubscriptionPayment(Long subscriptionId, BigDecimal amount, Payment.PaymentMethod paymentMethod); Payment processRenewalPayment(Long subscriptionId); // Payment cleanup and maintenance void cleanupPendingPayments(int hoursOld); void archiveOldPayments(int daysOld); } 