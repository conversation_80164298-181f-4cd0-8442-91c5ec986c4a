package lk.bandana.core.service.impl;

import lk.bandana.core.entity.MetaData;

import lk.bandana.core.repository.MetaDataRepository;

import lk.bandana.core.service.MetaDataService;

import org.slf4j.Logger;

import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@

public class MetaDataServiceImpl implements MetaDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetaDataServiceImpl.class);

    @MetaDataRepository metaDataRepository;

    @
    public boolean save(MetaData metaData) {

        try {
            metaDataRepository.save(metaData);
            LOGGER.info(metaData.getName());

            return true;
        }

    } catch (Exception ex) {
        LOGGER.error("Save Metadata failed: " + ex.getMessage());

        return false;
    }
}

@
public MetaData searchMetaData(String value, String category) {

    try {

        return metaDataRepository.findByValueAndCategory(value, category);
    }

} catch (Exception ex) {
    LOGGER.error("searchMetaData failed: " + ex.getMessage());

    return null;
}
}

@
public List<MetaData> findByCategory(String text) {

    try {

        return metaDataRepository.findByCategory(text);
    }

} catch (Exception ex) {
    LOGGER.error("findByCategory Metadata failed: " + ex.getMessage());

    return null;
}
}

@
public MetaData findById(Integer id) {

    try {

        return metaDataRepository.findById(id).get();
    }

} catch (Exception ex) {
    LOGGER.error("findById " + ex.getMessage());

    return null;
}
}
}
