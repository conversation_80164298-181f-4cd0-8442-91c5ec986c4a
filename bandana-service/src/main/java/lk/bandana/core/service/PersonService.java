package lk.bandana.core.service; import lk.bandana.business.kendara.entity.Person; import org.springframework.data.domain.Pageable; import java.util.List; public interface PersonService { boolean save(Person person); Iterable<Person> findAllByType(Pageable pageable, String personType); Iterable<Person> findAll(Pageable pageable); boolean disablePerson(Long personId); Person searchPerson(String name); List<Person> searchPersonNameLike(String name); List<Person> searchPersonNameLike(String name, Integer personTypeId); } 