package lk.bandana.core.service.impl;

import lk.bandana.core.entity.User;
import lk.bandana.core.repository.UserRepository;
import lk.bandana.core.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;


@Service(value = "userService")
public class UserServiceImpl implements UserDetailsService, UserService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    public UserDetails loadUserByUsername(String userId) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(userId);
        if (user == null) {
            throw new UsernameNotFoundException("Invalid username or password.");
        }
        return new org.springframework.security.core.userdetails.User(user.getUsername(), user.getPassword(), getAuthority());
    }

    private List<SimpleGrantedAuthority> getAuthority() {
        return Arrays.asList(new SimpleGrantedAuthority("ROLE_ADMIN"));
    }

    public List<User> findAll() {
        List<User> list = new ArrayList<>();
        userRepository.findAll().iterator().forEachRemaining(list::add);
        return list;
    }

    @Override
    public String delete(Long id) {
        try {
            userRepository.deleteById(id);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("Removing user failed " + ex.getMessage());
            return "failed";
        }
    }

    @Override
    public User search(String username) {
        try {
            return userRepository.findByUsername(username);
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public User findOne(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    @Override
    public boolean save(User user) {
        boolean success = false;
        try {
            if ((user.getPassword() != null) & (user.getId() == null)) {
                userRepository.save(user);
                success = true;
            } else if ((user.getPassword().equals("NOCHNG")) & (user.getId() != null)) {
                user.setPassword(userRepository.findById(user.getId()).get().getPassword());
                userRepository.save(user);
                success = true;
            } else if ((user.getPassword() != "NOCHNG") & (user.getId() != null)) {
                userRepository.save(user);
                success = true;
            }
            return success;
        } catch (Exception ex) {
            LOGGER.error("Add user failed " + ex.getMessage());
            return false;
        }
    }
}
