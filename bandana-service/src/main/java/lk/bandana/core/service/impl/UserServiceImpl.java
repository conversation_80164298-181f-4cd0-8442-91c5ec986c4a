package lk.bandana.core.service.impl;

import lk.bandana.core.entity.User;
import lk.bandana.core.repository.UserRepository;
import lk.bandana.core.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.security.crypto.password.PasswordEncoder;
import java.util.List;
import java.util.Optional;

/**
 * UserServiceImpl - User Service Implementation
 * Handles user management operations
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public List<User> findAll() {
        return userRepository.findAll();
    }

    @Override
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    @Override
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    @Override
    public User save(User user) {
        // Encode password if it's a new user or password is being changed
        if (user.getId() == null || user.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        return userRepository.save(user);
    }

    @Override
    public User createUser(String username, String email, String password, String role) {
        var user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword(password);
        user.setEnabled(true);
        // Set role logic here
        return save(user);
    }

    @Override
    public void deleteById(Long id) {
        userRepository.deleteById(id);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public List<User> findByRole(String role) {
        return userRepository.findByRole(role);
    }

    @Override
    public User updatePassword(Long userId, String newPassword) {
        var user = findById(userId).orElseThrow(() ->
            new RuntimeException("User not found with id: " + userId));
        user.setPassword(passwordEncoder.encode(newPassword));
        return userRepository.save(user);
    }

    @Override
    public User enableUser(Long userId) {
        var user = findById(userId).orElseThrow(() ->
            new RuntimeException("User not found with id: " + userId));
        user.setEnabled(true);
        return userRepository.save(user);
    }

    @Override
    public User disableUser(Long userId) {
        var user = findById(userId).orElseThrow(() ->
            new RuntimeException("User not found with id: " + userId));
        user.setEnabled(false);
        return userRepository.save(user);
    }
}
