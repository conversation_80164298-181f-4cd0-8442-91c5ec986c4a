package lk.bandana.core.service.impl;

import lk.bandana.core.entity.UserRole;
import lk.bandana.core.repository.UserRoleRepository;
import lk.bandana.core.service.RoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Optional;

/**
 * RoleServiceImpl - Role Service Implementation
 * Handles user role management operations
 */
@Service
public class RoleServiceImpl implements RoleService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RoleServiceImpl.class);

    @Autowired
    private UserRoleRepository roleRepository;

    @Override
    public List<UserRole> findAll() {
        try {
            return roleRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Roles retrieving failed: " + ex.getMessage(), ex);
            return List.of();
        }
    }

    @Override
    public UserRole save(UserRole role) {
        try {
            return roleRepository.save(role);
        } catch (Exception ex) {
            LOGGER.error("Create role failed: " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to save role: " + ex.getMessage(), ex);
        }
    }

    @Override
    public Optional<UserRole> findById(Long id) {
        try {
            return roleRepository.findById(id);
        } catch (Exception ex) {
            LOGGER.error("Find role by id failed: " + ex.getMessage(), ex);
            return Optional.empty();
        }
    }

    @Override
    public Optional<UserRole> findByName(String name) {
        try {
            return Optional.ofNullable(roleRepository.findByName(name));
        } catch (Exception ex) {
            LOGGER.error("Find role by name failed: " + ex.getMessage(), ex);
            return Optional.empty();
        }
    }

    @Override
    public void deleteById(Long id) {
        try {
            roleRepository.deleteById(id);
        } catch (Exception ex) {
            LOGGER.error("Delete role failed: " + ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete role: " + ex.getMessage(), ex);
        }
    }

    @Override
    public boolean existsByName(String name) {
        try {
            return roleRepository.existsByName(name);
        } catch (Exception ex) {
            LOGGER.error("Check role existence failed: " + ex.getMessage(), ex);
            return false;
        }
    }

    @Override
    public UserRole createDefaultRole(String roleName, String description) {
        var role = new UserRole();
        role.setName(roleName);
        role.setDescription(description);
        role.setActive(true);
        return save(role);
    }
}
