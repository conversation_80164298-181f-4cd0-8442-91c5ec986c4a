package lk.bandana.core.service.async;

import org.springframework.stereotype.Service;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.StructuredTaskScope;
import java.util.List;
import java.util.concurrent.Future;

/**
 * Modern async service using Java 23 Virtual Threads and Structured Concurrency
 */
@Service
public class VirtualThreadMatchingService {
    
    private final java.util.concurrent.ExecutorService virtualExecutor = 
        Executors.newVirtualThreadPerTaskExecutor();
    
    /**
     * Process multiple profile matches concurrently using Virtual Threads
     */
    public CompletableFuture<List<MatchResult>> processMatchesConcurrently(
            List<String> profileIds) {
        
        return CompletableFuture.supplyAsync(() -> {
            try (var scope = new StructuredTaskScope.ShutdownOnFailure()) {
                
                // Submit all matching tasks concurrently
                var matchTasks = profileIds.stream()
                    .map(profileId -> scope.fork(() -> processMatch(profileId)))
                    .toList();
                
                // Wait for all tasks to complete
                scope.join();
                scope.throwIfFailed();
                
                // Collect results using pattern matching
                return matchTasks.stream()
                    .map(Future::resultNow)
                    .toList();
                    
            } catch (Exception e) {
                throw new RuntimeException(STR."Failed to process matches: \{e.getMessage()}", e);
            }
        }, virtualExecutor);
    }
    
    /**
     * Process a single match using modern pattern matching
     */
    private MatchResult processMatch(String profileId) {
        // Simulate processing time
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return new MatchResult.Failed(profileId, "Interrupted");
        }
        
        // Use pattern matching for result processing
        return switch (profileId.length() % 3) {
            case 0 -> new MatchResult.HighCompatibility(profileId, 95);
            case 1 -> new MatchResult.MediumCompatibility(profileId, 75);
            case 2 -> new MatchResult.LowCompatibility(profileId, 45);
            default -> new MatchResult.Failed(profileId, "Unknown error");
        };
    }
    
    /**
     * Sealed interface for match results using modern Java patterns
     */
    public sealed interface MatchResult 
        permits MatchResult.HighCompatibility, 
                MatchResult.MediumCompatibility, 
                MatchResult.LowCompatibility, 
                MatchResult.Failed {
        
        String profileId();
        
        record HighCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return STR."High compatibility match (\{score}%) for profile \{profileId}";
            }
        }
        
        record MediumCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return STR."Medium compatibility match (\{score}%) for profile \{profileId}";
            }
        }
        
        record LowCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return STR."Low compatibility match (\{score}%) for profile \{profileId}";
            }
        }
        
        record Failed(String profileId, String reason) implements MatchResult {
            public String getDescription() {
                return STR."Match failed for profile \{profileId}: \{reason}";
            }
        }
        
        // Pattern matching helper
        default String getFormattedResult() {
            return switch (this) {
                case HighCompatibility(var id, var score) -> 
                    STR."🎯 Excellent match for \{id} (\{score}%)";
                case MediumCompatibility(var id, var score) -> 
                    STR."👍 Good match for \{id} (\{score}%)";
                case LowCompatibility(var id, var score) -> 
                    STR."👌 Possible match for \{id} (\{score}%)";
                case Failed(var id, var reason) -> 
                    STR."❌ Failed to match \{id}: \{reason}";
            };
        }
    }
}
