package lk.bandana.core.service.async;

import org.springframework.stereotype.Service;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ExecutorService;
import java.util.List;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * Modern async service using Java 17 features
 * Compatible with current Java version while providing concurrent processing
 */
@Service
public class VirtualThreadMatchingService {

    private final ExecutorService executor = Executors.newCachedThreadPool();

    /**
     * Process multiple profile matches concurrently
     */
    public CompletableFuture<List<MatchResult>> processMatchesConcurrently(
            List<String> profileIds) {

        return CompletableFuture.supplyAsync(() -> {
            try {
                // Submit all matching tasks concurrently
                var matchTasks = profileIds.stream()
                    .map(profileId -> CompletableFuture.supplyAsync(() -> processMatch(profileId), executor))
                    .collect(Collectors.toList());

                // Wait for all tasks to complete and collect results
                return matchTasks.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

            } catch (Exception e) {
                throw new RuntimeException("Failed to process matches: " + e.getMessage(), e);
            }
        }, executor);
    }
    
    /**
     * Process a single match using modern Java 17 patterns
     */
    private MatchResult processMatch(String profileId) {
        // Simulate processing time
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return new MatchResult.Failed(profileId, "Interrupted");
        }

        // Use switch expression (Java 17 compatible)
        return switch (profileId.length() % 3) {
            case 0 -> new MatchResult.HighCompatibility(profileId, 95);
            case 1 -> new MatchResult.MediumCompatibility(profileId, 75);
            case 2 -> new MatchResult.LowCompatibility(profileId, 45);
            default -> new MatchResult.Failed(profileId, "Unknown error");
        };
    }
    
    /**
     * Sealed interface for match results using Java 17 sealed classes
     */
    public sealed interface MatchResult
        permits MatchResult.HighCompatibility,
                MatchResult.MediumCompatibility,
                MatchResult.LowCompatibility,
                MatchResult.Failed {

        String profileId();

        record HighCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return "High compatibility match (%d%%) for profile %s".formatted(score, profileId);
            }
        }

        record MediumCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return "Medium compatibility match (%d%%) for profile %s".formatted(score, profileId);
            }
        }

        record LowCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return "Low compatibility match (%d%%) for profile %s".formatted(score, profileId);
            }
        }

        record Failed(String profileId, String reason) implements MatchResult {
            public String getDescription() {
                return "Match failed for profile %s: %s".formatted(profileId, reason);
            }
        }

        // Pattern matching helper using instanceof (Java 17)
        default String getFormattedResult() {
            if (this instanceof HighCompatibility high) {
                return "🎯 Excellent match for %s (%d%%)".formatted(high.profileId(), high.score());
            } else if (this instanceof MediumCompatibility medium) {
                return "👍 Good match for %s (%d%%)".formatted(medium.profileId(), medium.score());
            } else if (this instanceof LowCompatibility low) {
                return "👌 Possible match for %s (%d%%)".formatted(low.profileId(), low.score());
            } else if (this instanceof Failed failed) {
                return "❌ Failed to match %s: %s".formatted(failed.profileId(), failed.reason());
            }
            return "Unknown result";
        }
    }
}
