package lk.bandana.core.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;
import java.util.Optional;

/**
 * PersonServiceImpl - Service Implementation
 */
@Service
public class PersonServiceImpl {
    
    private static final Logger logger = LoggerFactory.getLogger(PersonServiceImpl.class);
    
    // TODO: Add repository dependencies
    
    public List<Object> findAll() {
        // TODO: Implement
        return List.of();
    }
    
    public Optional<Object> findById(Long id) {
        // TODO: Implement
        return Optional.empty();
    }
    
    public Object save(Object entity) {
        // TODO: Implement
        return entity;
    }
    
    public void deleteById(Long id) {
        // TODO: Implement
    }
}
