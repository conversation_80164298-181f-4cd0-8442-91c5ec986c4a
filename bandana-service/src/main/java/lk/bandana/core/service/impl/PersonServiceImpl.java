package lk.bandana.core.service.impl; import lk.bandana.core.entity.MetaData; import lk.bandana.business.kendara.entity.Person; import lk.bandana.core.repository.MetaDataRepository; import lk.bandana.business.kendara.repository.PersonRepository; import lk.bandana.core.service.PersonService; import org.slf4j.Logger; import org.slf4j.LoggerFactory; import org.springframework.beans.factory.annotation.Autowired; import org.springframework.data.domain.Pageable; import org.springframework.stereotype.Service; import java.util.List; @Service public class PersonServiceImpl implements PersonService { private static final Logger LOGGER = LoggerFactory.getLogger(PersonServiceImpl.class); @Autowired PersonRepository personRepository; @Autowired MetaDataRepository metaDataRepository; @Override public boolean save(Person person) { personRepository.save(person); LOGGER.info("Person saved. " + person.getFirstName()); return true; } @Override public Iterable<Person> findAllByType(Pageable pageable, String personTypeId) { try { return personRepository.findAllByPersonTypeId(pageable, personTypeId); } catch (Exception ex) { LOGGER.error("get vehicles failed " + ex.getMessage()); return null; } } @Override public Iterable<Person> findAll(Pageable pageable) { try { return personRepository.findAll(pageable); } catch (Exception ex) { LOGGER.error("get vehicles failed " + ex.getMessage()); return null; } } @Override public boolean disablePerson(Long personId) { try { Person person = personRepository.findById(personId).get(); person.setActive(false); personRepository.save(person); LOGGER.info("Person removed. " + person.getFirstName()); return true; } catch (Exception ex) { LOGGER.error("Removing Person Failed: " + ex.getMessage()); return false; } } @Override public Person searchPerson(String name) { return personRepository.findByFirstName(name); } @Override public List<Person> searchPersonNameLike(String name) { try { return personRepository.findPersonByFirstNameLikeIgnoreCase(name); } catch (Exception ex) { LOGGER.error("Search Person Failed: " + ex.getMessage()); return null; } } @Override public List<Person> searchPersonNameLike(String name, Integer personTypeId) { try { MetaData metaData = metaDataRepository.findById(personTypeId).get(); List<Person> people = personRepository.findAllByPersonTypeAndFirstNameLikeIgnoreCase(metaData.getId(), name); return people; } catch (Exception ex) { LOGGER.error("Search Person Failed: " + ex.getMessage()); return null; } } } 