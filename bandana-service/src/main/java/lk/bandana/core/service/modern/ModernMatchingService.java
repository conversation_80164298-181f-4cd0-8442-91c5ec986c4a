package lk.bandana.core.service.modern;

import org.springframework.stereotype.Service;
import java.util.concurrent.CompletableFuture;
import java.util.List;
import java.util.concurrent.Executors;

/**
 * Modern service using Java 17 features
 */
@Service
public class ModernMatchingService {
    
    private final var executor = Executors.newVirtualThreadPerTaskExecutor();
    
    /**
     * Process multiple profile matches using modern Java patterns
     */
    public CompletableFuture<List<MatchResult>> processMatches(List<String> profileIds) {
        return CompletableFuture.supplyAsync(() -> {
            return profileIds.stream()
                .map(this::processMatch)
                .toList();
        }, executor);
    }
    
    /**
     * Process a single match using pattern matching
     */
    private MatchResult processMatch(String profileId) {
        // Simulate processing
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return new MatchResult.Failed(profileId, "Interrupted");
        }
        
        // Use switch expression (Java 14+)
        return switch (profileId.length() % 3) {
            case 0 -> new MatchResult.HighCompatibility(profileId, 95);
            case 1 -> new MatchResult.MediumCompatibility(profileId, 75);
            default -> new MatchResult.LowCompatibility(profileId, 45);
        };
    }
    
    /**
     * Sealed interface for match results (Java 17+)
     */
    public sealed interface MatchResult 
        permits MatchResult.HighCompatibility, 
                MatchResult.MediumCompatibility, 
                MatchResult.LowCompatibility, 
                MatchResult.Failed {
        
        String profileId();
        
        record HighCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return "High compatibility match (%d%%) for profile %s".formatted(score, profileId);
            }
        }
        
        record MediumCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return "Medium compatibility match (%d%%) for profile %s".formatted(score, profileId);
            }
        }
        
        record LowCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return "Low compatibility match (%d%%) for profile %s".formatted(score, profileId);
            }
        }
        
        record Failed(String profileId, String reason) implements MatchResult {
            public String getDescription() {
                return "Match failed for profile %s: %s".formatted(profileId, reason);
            }
        }
        
        // Pattern matching helper using instanceof (Java 16+)
        default String getFormattedResult() {
            if (this instanceof HighCompatibility high) {
                return "🎯 Excellent match for " + high.profileId() + " (" + high.score() + "%)";
            } else if (this instanceof MediumCompatibility medium) {
                return "👍 Good match for " + medium.profileId() + " (" + medium.score() + "%)";
            } else if (this instanceof LowCompatibility low) {
                return "👌 Possible match for " + low.profileId() + " (" + low.score() + "%)";
            } else if (this instanceof Failed failed) {
                return "❌ Failed to match " + failed.profileId() + ": " + failed.reason();
            }
            return "Unknown result";
        }
    }
}
