package lk.bandana.core.service; import lk.bandana.core.entity.UserProfile; import org.springframework.data.domain.Page; import org.springframework.data.domain.Pageable; import java.util.List; import java.util.Optional; public interface UserProfileService { // Basic CRUD operations UserProfile save(UserProfile userProfile); Optional<UserProfile> findById(Long id); List<UserProfile> findAll(); void deleteById(Long id); // Profile status management UserProfile approveProfile(Long profileId, String approvedBy, String comments); UserProfile rejectProfile(Long profileId, String rejectedBy, String reason); UserProfile suspendProfile(Long profileId, String suspendedBy, String reason); List<UserProfile> findProfilesPendingApproval(); // Profile search and filtering Page<UserProfile> searchProfiles(String gender, Integer minAge, Integer maxAge, Integer minHeight, Integer maxHeight, String province, String district, String education, String occupation, String religion, Pageable pageable); List<UserProfile> findApprovedProfiles(); List<UserProfile> findApprovedProfilesByGender(String gender); List<UserProfile> findHighQualityProfiles(Integer minScore); // Agent management List<UserProfile> findProfilesManagedByAgent(Long agentId); UserProfile assignProfileToAgent(Long profileId, Long agentId); UserProfile removeProfileFromAgent(Long profileId); // Profile completion and quality UserProfile calculateProfileCompletion(Long profileId); UserProfile updateProfileQualityScore(Long profileId); List<UserProfile> findIncompleteProfiles(); // Location-based searches List<UserProfile> findByProvince(String provinceName); List<UserProfile> findByDistrict(String districtName); // Statistics and reporting Long countProfilesByStatus(UserProfile.ProfileStatus status); List<UserProfile> findRecentProfiles(int days); List<UserProfile> findProfilesWithPhotos(); // Profile validation boolean validateProfileData(UserProfile userProfile); List<String> getProfileValidationErrors(UserProfile userProfile); // Photo management UserProfile updateProfilePhoto(Long profileId, String photoUrl); UserProfile addAdditionalPhoto(Long profileId, String photoUrl); UserProfile removePhoto(Long profileId, String photoUrl); // Contact information UserProfile updateContactInformation(Long profileId, String email, String phone, String whatsapp); // Privacy settings UserProfile updatePrivacySettings(Long profileId, Boolean profileVisible, Boolean contactInfoVisible, Boolean photoVisible, Boolean casteVisible); } 