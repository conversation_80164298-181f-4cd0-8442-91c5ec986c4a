package lk.bandana.core.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * Modern validation example using Java 21 features
 */
public class SriLankanPhoneValidator implements ConstraintValidator<SriLankanPhone, String> {
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^(?:\+94|0)?[1-9]\d{8}$"
    );
    
    @Override
    public boolean isValid(String phone, ConstraintValidatorContext context) {
        return switch (phone) {
            case null -> true; // Let @NotNull handle null validation
            case String p when p.isBlank() -> false;
            case String p -> PHONE_PATTERN.matcher(p).matches();
        };
    }
}
