package lk.bandana.profile.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Document(collection = "user_profiles")
@Getter
@Setter
public class UserProfile {

    @Id
    private String id;

    // Basic Information
    @Field("full_name")
    private String fullName;

    @Field("date_of_birth")
    private LocalDate dateOfBirth;

    @Field("age")
    private Integer age;

    @Field("gender")
    private String gender; // MALE, FEMALE

    @Field("height_cm")
    private Integer heightCm;

    @Field("weight_kg")
    private Integer weightKg;

    // Location Information
    @Field("province_id")
    private String provinceId;

    @Field("district_id")
    private String districtId;

    @Field("city")
    private String city;

    @Field("address")
    private String address;

    // Professional Information
    @Field("occupation")
    private String occupation;

    @Field("education_level")
    private String educationLevel;

    @Field("work_place")
    private String workPlace;

    @Field("monthly_income")
    private String monthlyIncome;

    // Cultural Information
    @Field("religion")
    private String religion;

    @Field("caste")
    private String caste; // Optional field with privacy controls

    @Field("caste_visible")
    private Boolean casteVisible = false;

    @Field("mother_tongue")
    private String motherTongue;

    // Astrological Information
    private String birthCity;
    private String birthCountry;
    private Double birthLatitude;
    private Double birthLongitude;
    private String timeOfBirth;

    // Family Information
    private String fatherOccupation;
    private String motherOccupation;
    private Integer numberOfSiblings;
    private String familyType; // NUCLEAR, JOINT
    private String familyStatus; // MIDDLE_CLASS, UPPER_MIDDLE_CLASS, etc.

    // Personal Preferences
    private String maritalStatus; // NEVER_MARRIED, DIVORCED, WIDOWED
    private String smokingHabits; // NEVER, OCCASIONALLY, REGULARLY
    private String drinkingHabits; // NEVER, OCCASIONALLY, SOCIALLY, REGULARLY
    private String dietaryHabits; // VEGETARIAN, NON_VEGETARIAN, VEGAN

    // Profile Management
    private ProfileStatus status = ProfileStatus.PENDING;
    private String adminComments;
    private String rejectionReason;
    private LocalDateTime approvedDate;
    private String approvedBy;

    // Photos and Documents
    private String profilePhotoUrl;
    private List<String> additionalPhotoUrls;
    private String documentUrl; // ID verification document

    // Interests and Hobbies
    @Relationship("HAS_INTEREST")
    private List<lk.bandana.profile.entity.Interest> interests;

    // Matching Preferences
    private Integer preferredMinAge;
    private Integer preferredMaxAge;
    private Integer preferredMinHeight;
    private Integer preferredMaxHeight;
    private String preferredEducation;
    private String preferredOccupation;
    private List<String> preferredLocations;

    // Privacy Settings
    private Boolean profileVisible = true;
    private Boolean contactInfoVisible = false;
    private Boolean photoVisible = true;

    // Profile Quality Score (for subscription tiers)
    private Integer profileQualityScore = 0;

    // Agent Management (for agent-managed profiles)
    @Relationship("MANAGED_BY")
    private lk.bandana.user.entity.User managedByAgent;

    private Boolean isAgentManaged = false;

    // Audit Fields
    @CreatedDate
    private LocalDateTime createdDate;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedBy
    private String lastModifiedBy;

    // Profile completion percentage
    private Integer completionPercentage = 0;

    // Contact Information
    private String contactEmail;
    private String contactPhone;
    private String whatsappNumber;

    // About section
    private String aboutMe;
    private String partnerExpectations;

    public enum ProfileStatus {
        PENDING,
        APPROVED,
        REJECTED,
        SUSPENDED,
        INCOMPLETE
    }
}
