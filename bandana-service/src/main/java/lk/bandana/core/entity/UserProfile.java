package lk.bandana.profile.entity;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.mongodb.core.mapping.Field;

import org.springframework.data.mongodb.core.mapping.DBRef;

import org.springframework.data.annotation.CreatedDate;

import org.springframework.data.annotation.LastModifiedDate;

import org.springframework.data.annotation.CreatedBy;

import org.springframework.data.annotation.LastModifiedBy;

import java.time.LocalDate;

import java.time.LocalDateTime;

import java.util.List;

@(collection = "user_profiles")

public class UserProfile {

    @
    private String id;

    Basic Information
    @("full_name")
    private String fullName;

    @("date_of_birth")
    private LocalDate dateOfBirth;

    @("age")
    private Integer age;

    @("gender")
    private String gender;

    MALE, FEMALE
    @("height_cm")
    private Integer heightCm;

    @("weight_kg")
    private Integer weightKg;

    Location Information
    @("province_id")
    private String provinceId;

    @("district_id")
    private String districtId;

    @("city")
    private String city;

    @("address")
    private String address;

    Professional Information
    @("occupation")
    private String occupation;

    @("education_level")
    private String educationLevel;

    @("work_place")
    private String workPlace;

    @("monthly_income")
    private String monthlyIncome;

    Cultural Information
    @("religion")
    private String religion;

    @("caste")
    private String caste;

    Optional field with privacy controls
    @("caste_visible")
    private Boolean casteVisible = false;

    @("mother_tongue")
    private String motherTongue;

    Astrological Information
    private String birthCity;

    private String birthCountry;

    private Double birthLatitude;

    private Double birthLongitude;

    private String timeOfBirth;

    Family Information
    private String fatherOccupation;

    private String motherOccupation;

    private Integer numberOfSiblings;

    private String familyType;

    NUCLEAR, JOINT
    private String familyStatus;

    MIDDLE_CLASS, UPPER_MIDDLE_CLASS, etc.
    Personal Preferences
    private String maritalStatus;

    NEVER_MARRIED, DIVORCED, WIDOWED
    private String smokingHabits;

    NEVER, OCCASIONALLY, REGULARLY
    private String drinkingHabits;

    NEVER, OCCASIONALLY, SOCIALLY, REGULARLY
    private String dietaryHabits;

    VEGETARIAN, NON_VEGETARIAN, VEGAN
    Profile Management
    private ProfileStatus status = ProfileStatus.PENDING;

    private String adminComments;

    private String rejectionReason;

    private LocalDateTime approvedDate;

    private String approvedBy;

    Photos and Documents
    private String profilePhotoUrl;

    private List<String> additionalPhotoUrls;

    private String documentUrl;

    ID verification document
    Interests and Hobbies
    @
    private List<lk.bandana.profile.entity.Interest> interests;

    Matching Preferences
    private Integer preferredMinAge;

    private Integer preferredMaxAge;

    private Integer preferredMinHeight;

    private Integer preferredMaxHeight;

    private String preferredEducation;

    private String preferredOccupation;

    private List<String> preferredLocations;

    Privacy Settings
    private Boolean profileVisible = true;

    private Boolean contactInfoVisible = false;

    private Boolean photoVisible = true;

    Profile Quality Score (for subscription tiers)
    private Integer profileQualityScore = 0;

    Agent Management (for agent-managed profiles)
    @
    private lk.bandana.user.entity.User managedByAgent;

    private Boolean isAgentManaged = false;

    Audit Fields
    @
    private LocalDateTime createdDate;

    @
    private LocalDateTime lastModifiedDate;

    @
    private String createdBy;

    @
    private String lastModifiedBy;

    Profile completion percentage
    private Integer completionPercentage = 0;

    Contact Information
    private String contactEmail;

    private String contactPhone;

    private String whatsappNumber;

    About section
    private String aboutMe;

    private String partnerExpectations;

    public enum ProfileStatus {
        PENDING, APPROVED, REJECTED, SUSPENDED, INCOMPLETE }

        public String getId() {

            return this.id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getFullname() {

            return this.fullName;
        }

        public void setFullname(String fullName) {
            this.fullName = fullName;
        }

        public LocalDate getDateofbirth() {

            return this.dateOfBirth;
        }

        public void setDateofbirth(LocalDate dateOfBirth) {
            this.dateOfBirth = dateOfBirth;
        }

        public Integer getAge() {

            return this.age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }

        public String getGender() {

            return this.gender;
        }

        public void setGender(String gender) {
            this.gender = gender;
        }

        public Integer getHeightcm() {

            return this.heightCm;
        }

        public void setHeightcm(Integer heightCm) {
            this.heightCm = heightCm;
        }

        public Integer getWeightkg() {

            return this.weightKg;
        }

        public void setWeightkg(Integer weightKg) {
            this.weightKg = weightKg;
        }

        public String getProvinceid() {

            return this.provinceId;
        }

        public void setProvinceid(String provinceId) {
            this.provinceId = provinceId;
        }

        public String getDistrictid() {

            return this.districtId;
        }

        public void setDistrictid(String districtId) {
            this.districtId = districtId;
        }

        public String getCity() {

            return this.city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getAddress() {

            return this.address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getOccupation() {

            return this.occupation;
        }

        public void setOccupation(String occupation) {
            this.occupation = occupation;
        }

        public String getEducationlevel() {

            return this.educationLevel;
        }

        public void setEducationlevel(String educationLevel) {
            this.educationLevel = educationLevel;
        }

        public String getWorkplace() {

            return this.workPlace;
        }

        public void setWorkplace(String workPlace) {
            this.workPlace = workPlace;
        }

        public String getMonthlyincome() {

            return this.monthlyIncome;
        }

        public void setMonthlyincome(String monthlyIncome) {
            this.monthlyIncome = monthlyIncome;
        }

        public String getReligion() {

            return this.religion;
        }

        public void setReligion(String religion) {
            this.religion = religion;
        }

        public String getCaste() {

            return this.caste;
        }

        public void setCaste(String caste) {
            this.caste = caste;
        }

        public String getMothertongue() {

            return this.motherTongue;
        }

        public void setMothertongue(String motherTongue) {
            this.motherTongue = motherTongue;
        }

        public String getBirthcity() {

            return this.birthCity;
        }

        public void setBirthcity(String birthCity) {
            this.birthCity = birthCity;
        }

        public String getBirthcountry() {

            return this.birthCountry;
        }

        public void setBirthcountry(String birthCountry) {
            this.birthCountry = birthCountry;
        }

        public Double getBirthlatitude() {

            return this.birthLatitude;
        }

        public void setBirthlatitude(Double birthLatitude) {
            this.birthLatitude = birthLatitude;
        }

        public Double getBirthlongitude() {

            return this.birthLongitude;
        }

        public void setBirthlongitude(Double birthLongitude) {
            this.birthLongitude = birthLongitude;
        }

        public String getTimeofbirth() {

            return this.timeOfBirth;
        }

        public void setTimeofbirth(String timeOfBirth) {
            this.timeOfBirth = timeOfBirth;
        }

        public String getFatheroccupation() {

            return this.fatherOccupation;
        }

        public void setFatheroccupation(String fatherOccupation) {
            this.fatherOccupation = fatherOccupation;
        }

        public String getMotheroccupation() {

            return this.motherOccupation;
        }

        public void setMotheroccupation(String motherOccupation) {
            this.motherOccupation = motherOccupation;
        }

        public Integer getNumberofsiblings() {

            return this.numberOfSiblings;
        }

        public void setNumberofsiblings(Integer numberOfSiblings) {
            this.numberOfSiblings = numberOfSiblings;
        }

        public String getFamilytype() {

            return this.familyType;
        }

        public void setFamilytype(String familyType) {
            this.familyType = familyType;
        }

        public String getFamilystatus() {

            return this.familyStatus;
        }

        public void setFamilystatus(String familyStatus) {
            this.familyStatus = familyStatus;
        }

        public String getMaritalstatus() {

            return this.maritalStatus;
        }

        public void setMaritalstatus(String maritalStatus) {
            this.maritalStatus = maritalStatus;
        }

        public String getSmokinghabits() {

            return this.smokingHabits;
        }

        public void setSmokinghabits(String smokingHabits) {
            this.smokingHabits = smokingHabits;
        }

        public String getDrinkinghabits() {

            return this.drinkingHabits;
        }

        public void setDrinkinghabits(String drinkingHabits) {
            this.drinkingHabits = drinkingHabits;
        }

        public String getDietaryhabits() {

            return this.dietaryHabits;
        }

        public void setDietaryhabits(String dietaryHabits) {
            this.dietaryHabits = dietaryHabits;
        }

        public String getAdmincomments() {

            return this.adminComments;
        }

        public void setAdmincomments(String adminComments) {
            this.adminComments = adminComments;
        }

        public String getRejectionreason() {

            return this.rejectionReason;
        }

        public void setRejectionreason(String rejectionReason) {
            this.rejectionReason = rejectionReason;
        }

        public LocalDateTime getApproveddate() {

            return this.approvedDate;
        }

        public void setApproveddate(LocalDateTime approvedDate) {
            this.approvedDate = approvedDate;
        }

        public String getApprovedby() {

            return this.approvedBy;
        }

        public void setApprovedby(String approvedBy) {
            this.approvedBy = approvedBy;
        }

        public String getProfilephotourl() {

            return this.profilePhotoUrl;
        }

        public void setProfilephotourl(String profilePhotoUrl) {
            this.profilePhotoUrl = profilePhotoUrl;
        }

        public List<String> getAdditionalphotourls() {

            return this.additionalPhotoUrls;
        }

        public void setAdditionalphotourls(List<String> additionalPhotoUrls) {
            this.additionalPhotoUrls = additionalPhotoUrls;
        }

        public String getDocumenturl() {

            return this.documentUrl;
        }

        public void setDocumenturl(String documentUrl) {
            this.documentUrl = documentUrl;
        }

        public List<lk.bandana.profile.entity.Interest> getInterests() {

            return this.interests;
        }

        public void setInterests(List<lk.bandana.profile.entity.Interest> interests) {
            this.interests = interests;
        }

        public Integer getPreferredminage() {

            return this.preferredMinAge;
        }

        public void setPreferredminage(Integer preferredMinAge) {
            this.preferredMinAge = preferredMinAge;
        }

        public Integer getPreferredmaxage() {

            return this.preferredMaxAge;
        }

        public void setPreferredmaxage(Integer preferredMaxAge) {
            this.preferredMaxAge = preferredMaxAge;
        }

        public Integer getPreferredminheight() {

            return this.preferredMinHeight;
        }

        public void setPreferredminheight(Integer preferredMinHeight) {
            this.preferredMinHeight = preferredMinHeight;
        }

        public Integer getPreferredmaxheight() {

            return this.preferredMaxHeight;
        }

        public void setPreferredmaxheight(Integer preferredMaxHeight) {
            this.preferredMaxHeight = preferredMaxHeight;
        }

        public String getPreferrededucation() {

            return this.preferredEducation;
        }

        public void setPreferrededucation(String preferredEducation) {
            this.preferredEducation = preferredEducation;
        }

        public String getPreferredoccupation() {

            return this.preferredOccupation;
        }

        public void setPreferredoccupation(String preferredOccupation) {
            this.preferredOccupation = preferredOccupation;
        }

        public List<String> getPreferredlocations() {

            return this.preferredLocations;
        }

        public void setPreferredlocations(List<String> preferredLocations) {
            this.preferredLocations = preferredLocations;
        }

        public LocalDateTime getCreateddate() {

            return this.createdDate;
        }

        public void setCreateddate(LocalDateTime createdDate) {
            this.createdDate = createdDate;
        }

        public LocalDateTime getLastmodifieddate() {

            return this.lastModifiedDate;
        }

        public void setLastmodifieddate(LocalDateTime lastModifiedDate) {
            this.lastModifiedDate = lastModifiedDate;
        }

        public String getCreatedby() {

            return this.createdBy;
        }

        public void setCreatedby(String createdBy) {
            this.createdBy = createdBy;
        }

        public String getLastmodifiedby() {

            return this.lastModifiedBy;
        }

        public void setLastmodifiedby(String lastModifiedBy) {
            this.lastModifiedBy = lastModifiedBy;
        }

        public String getContactemail() {

            return this.contactEmail;
        }

        public void setContactemail(String contactEmail) {
            this.contactEmail = contactEmail;
        }

        public String getContactphone() {

            return this.contactPhone;
        }

        public void setContactphone(String contactPhone) {
            this.contactPhone = contactPhone;
        }

        public String getWhatsappnumber() {

            return this.whatsappNumber;
        }

        public void setWhatsappnumber(String whatsappNumber) {
            this.whatsappNumber = whatsappNumber;
        }

        public String getAboutme() {

            return this.aboutMe;
        }

        public void setAboutme(String aboutMe) {
            this.aboutMe = aboutMe;
        }

        public String getPartnerexpectations() {

            return this.partnerExpectations;
        }

        public void setPartnerexpectations(String partnerExpectations) {
            this.partnerExpectations = partnerExpectations;
        }
    }
