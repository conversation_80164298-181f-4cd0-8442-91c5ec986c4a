package lk.bandana.matching.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;

@Node
@Getter
@Setter
public class QuestionOption {

    @Id
    private Long id;

    private String optionText;
    private String optionTextSinhala;
    private String optionTextTamil;
    private String value;
    private Integer sortOrder = 0;
    private Boolean active = true;

    // Scoring for compatibility matching
    private Integer compatibilityScore = 0; // Used in matching algorithm

    public QuestionOption() {}

    public QuestionOption(String optionText, String value) {
        this.optionText = optionText;
        this.value = value;
    }

    public QuestionOption(String optionText, String value, Integer compatibilityScore) {
        this.optionText = optionText;
        this.value = value;
        this.compatibilityScore = compatibilityScore;
    }
}
