package lk.bandana.matching.entity; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; @Document public class QuestionOption { @Id private Long id; private String optionText; private String optionTextSinhala; private String optionTextTamil; private String value; private Integer sortOrder = 0; private Boolean active = true; // Scoring for compatibility matching private Integer compatibilityScore = 0; // Used in matching algorithm public QuestionOption() {} public QuestionOption(String optionText, String value) { this.optionText = optionText; this.value = value; } public QuestionOption(String optionText, String value, Integer compatibilityScore) { this.optionText = optionText; this.value = value; this.compatibilityScore = compatibilityScore; } public Long getId() { return this.id; } public void setId(Long id) { this.id = id; } public String getOptiontext() { return this.optionText; } public void setOptiontext(String optionText) { this.optionText = optionText; } public String getOptiontextsinhala() { return this.optionTextSinhala; } public void setOptiontextsinhala(String optionTextSinhala) { this.optionTextSinhala = optionTextSinhala; } public String getOptiontexttamil() { return this.optionTextTamil; } public void setOptiontexttamil(String optionTextTamil) { this.optionTextTamil = optionTextTamil; } public String getValue() { return this.value; } public void setValue(String value) { this.value = value; } }