package lk.bandana.matching.entity;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.List;

@

public class Question {

    @
    private Long id;

    private String questionText;

    private String questionTextSinhala;

    private String questionTextTamil;

    private QuestionType type;

    private Boolean required = false;

    private Integer sortOrder = 0;

    private Boolean active = true;

    Scoring and matching
    private Integer weightInMatching = 1;

    scale
    private String matchingCategory;

    AGE, LOCATION, EDUCATION, etc.
    @
    private List<lk.bandana.matching.entity.QuestionOption> options;

    Conditional logic
    private Long dependsOnQuestionId;

    private String dependsOnAnswerValue;

    public enum QuestionType {
        SINGLE_CHOICE, MULTIPLE_CHOICE, TEXT, NUMBER, DATE, SCALE,
        rating YES_NO }

        public Long getId() {

            return this.id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getQuestiontext() {

            return this.questionText;
        }

        public void setQuestiontext(String questionText) {
            this.questionText = questionText;
        }

        public String getQuestiontextsinhala() {

            return this.questionTextSinhala;
        }

        public void setQuestiontextsinhala(String questionTextSinhala) {
            this.questionTextSinhala = questionTextSinhala;
        }

        public String getQuestiontexttamil() {

            return this.questionTextTamil;
        }

        public void setQuestiontexttamil(String questionTextTamil) {
            this.questionTextTamil = questionTextTamil;
        }

        public QuestionType getType() {

            return this.type;
        }

        public void setType(QuestionType type) {
            this.type = type;
        }

        public String getMatchingcategory() {

            return this.matchingCategory;
        }

        public void setMatchingcategory(String matchingCategory) {
            this.matchingCategory = matchingCategory;
        }

        public List<lk.bandana.matching.entity.QuestionOption> getOptions() {

            return this.options;
        }

        public void setOptions(List<lk.bandana.matching.entity.QuestionOption> options) {
            this.options = options;
        }

        public Long getDependsonquestionid() {

            return this.dependsOnQuestionId;
        }

        public void setDependsonquestionid(Long dependsOnQuestionId) {
            this.dependsOnQuestionId = dependsOnQuestionId;
        }

        public String getDependsonanswervalue() {

            return this.dependsOnAnswerValue;
        }

        public void setDependsonanswervalue(String dependsOnAnswerValue) {
            this.dependsOnAnswerValue = dependsOnAnswerValue;
        }
    }
