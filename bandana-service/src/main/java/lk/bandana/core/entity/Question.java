package lk.bandana.matching.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.List;

@Node
@Getter
@Setter
public class Question {

    @Id
    private Long id;

    private String questionText;
    private String questionTextSinhala;
    private String questionTextTamil;
    private QuestionType type;
    private Boolean required = false;
    private Integer sortOrder = 0;
    private Boolean active = true;

    // Scoring and matching
    private Integer weightInMatching = 1; // 1-10 scale
    private String matchingCategory; // AGE, LOCATION, EDUCATION, etc.

    @Relationship("HAS_OPTION")
    private List<lk.bandana.matching.entity.QuestionOption> options;

    // Conditional logic
    private Long dependsOnQuestionId;
    private String dependsOnAnswerValue;

    public enum QuestionType {
        SINGLE_CHOICE,
        MULTIPLE_CHOICE,
        TEXT,
        NUMBER,
        DATE,
        SCALE, // 1-10 rating
        YES_NO
    }
}
