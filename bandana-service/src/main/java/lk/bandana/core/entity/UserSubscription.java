package lk.bandana.subscription.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Node
@Getter
@Setter
public class UserSubscription {

    @Id
    private Long id;

    @Relationship("SUBSCRIBES_TO")
    private lk.bandana.subscription.entity.SubscriptionPackage subscriptionPackage;

    @Relationship("BELONGS_TO")
    private lk.bandana.user.entity.User user;

    // Subscription period
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private LocalDateTime nextBillingDate;

    // Status
    private SubscriptionStatus status = SubscriptionStatus.ACTIVE;
    private Boolean autoRenewal = true;

    // Payment information
    private BigDecimal amountPaid;
    private String currency = "LKR";
    private String paymentMethod;
    private String transactionId;
    private LocalDateTime paymentDate;

    // Usage tracking
    private Integer profileViewsUsed = 0;
    private Integer contactRequestsUsed = 0;
    private Integer photosUploaded = 0;

    // Billing information
    private String billingAddress;
    private String billingCity;
    private String billingCountry;
    private String billingPostalCode;

    // Cancellation information
    private LocalDateTime cancelledDate;
    private String cancellationReason;
    private Boolean refundRequested = false;
    private BigDecimal refundAmount = BigDecimal.ZERO;

    // Trial information
    private Boolean isTrial = false;
    private LocalDateTime trialEndDate;

    // Promotional information
    private String promoCode;
    private BigDecimal discountApplied = BigDecimal.ZERO;

    @CreatedDate
    private LocalDateTime createdDate;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    // Helper methods
    public boolean isActive() {
        return status == SubscriptionStatus.ACTIVE && 
               endDate != null && 
               endDate.isAfter(LocalDateTime.now());
    }

    public boolean isExpired() {
        return endDate != null && endDate.isBefore(LocalDateTime.now());
    }

    public boolean canViewProfile() {
        if (subscriptionPackage == null) return false;
        return profileViewsUsed < subscriptionPackage.getMaxProfileViews();
    }

    public boolean canSendContactRequest() {
        if (subscriptionPackage == null) return false;
        return contactRequestsUsed < subscriptionPackage.getMaxContactRequests();
    }

    public enum SubscriptionStatus {
        ACTIVE,
        EXPIRED,
        CANCELLED,
        SUSPENDED,
        PENDING_PAYMENT,
        TRIAL
    }
}
