package lk.bandana.subscription.entity;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.mongodb.core.mapping.DBRef;

import org.springframework.data.annotation.CreatedDate;

import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;

import java.time.LocalDateTime;

@

public class UserSubscription {

    @
    private Long id;

    @
    private lk.bandana.subscription.entity.SubscriptionPackage subscriptionPackage;

    @
    private lk.bandana.user.entity.User user;

    Subscription period
    private LocalDateTime startDate;

    private LocalDateTime endDate;

    private LocalDateTime nextBillingDate;

    Status
    private SubscriptionStatus status = SubscriptionStatus.ACTIVE;

    private Boolean autoRenewal = true;

    Payment information
    private BigDecimal amountPaid;

    private String currency = "LKR";

    private String paymentMethod;

    private String transactionId;

    private LocalDateTime paymentDate;

    Usage tracking
    private Integer profileViewsUsed = 0;

    private Integer contactRequestsUsed = 0;

    private Integer photosUploaded = 0;

    Billing information
    private String billingAddress;

    private String billingCity;

    private String billingCountry;

    private String billingPostalCode;

    Cancellation information
    private LocalDateTime cancelledDate;

    private String cancellationReason;

    private Boolean refundRequested = false;

    private BigDecimal refundAmount = BigDecimal.ZERO;

    Trial information
    private Boolean isTrial = false;

    private LocalDateTime trialEndDate;

    Promotional information
    private String promoCode;

    private BigDecimal discountApplied = BigDecimal.ZERO;

    @
    private LocalDateTime createdDate;

    @
    private LocalDateTime lastModifiedDate;

    Helper methods
    public boolean isActive() {

        return status == SubscriptionStatus.ACTIVE && endDate != null && endDate.isAfter(LocalDateTime.now());
    }

    public boolean isExpired() {

        return endDate != null && endDate.isBefore(LocalDateTime.now());
    }

    public boolean canViewProfile() {

        if (subscriptionPackage == null)
        return false;

        return profileViewsUsed < subscriptionPackage.getMaxProfileViews();
    }

    public boolean canSendContactRequest() {

        if (subscriptionPackage == null)
        return false;

        return contactRequestsUsed < subscriptionPackage.getMaxContactRequests();
    }

    public enum SubscriptionStatus {
        ACTIVE, EXPIRED, CANCELLED, SUSPENDED, PENDING_PAYMENT, TRIAL }

        public Long getId() {

            return this.id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public LocalDateTime getStartdate() {

            return this.startDate;
        }

        public void setStartdate(LocalDateTime startDate) {
            this.startDate = startDate;
        }

        public LocalDateTime getEnddate() {

            return this.endDate;
        }

        public void setEnddate(LocalDateTime endDate) {
            this.endDate = endDate;
        }

        public LocalDateTime getNextbillingdate() {

            return this.nextBillingDate;
        }

        public void setNextbillingdate(LocalDateTime nextBillingDate) {
            this.nextBillingDate = nextBillingDate;
        }

        public BigDecimal getAmountpaid() {

            return this.amountPaid;
        }

        public void setAmountpaid(BigDecimal amountPaid) {
            this.amountPaid = amountPaid;
        }

        public String getPaymentmethod() {

            return this.paymentMethod;
        }

        public void setPaymentmethod(String paymentMethod) {
            this.paymentMethod = paymentMethod;
        }

        public String getTransactionid() {

            return this.transactionId;
        }

        public void setTransactionid(String transactionId) {
            this.transactionId = transactionId;
        }

        public LocalDateTime getPaymentdate() {

            return this.paymentDate;
        }

        public void setPaymentdate(LocalDateTime paymentDate) {
            this.paymentDate = paymentDate;
        }

        public String getBillingaddress() {

            return this.billingAddress;
        }

        public void setBillingaddress(String billingAddress) {
            this.billingAddress = billingAddress;
        }

        public String getBillingcity() {

            return this.billingCity;
        }

        public void setBillingcity(String billingCity) {
            this.billingCity = billingCity;
        }

        public String getBillingcountry() {

            return this.billingCountry;
        }

        public void setBillingcountry(String billingCountry) {
            this.billingCountry = billingCountry;
        }

        public String getBillingpostalcode() {

            return this.billingPostalCode;
        }

        public void setBillingpostalcode(String billingPostalCode) {
            this.billingPostalCode = billingPostalCode;
        }

        public LocalDateTime getCancelleddate() {

            return this.cancelledDate;
        }

        public void setCancelleddate(LocalDateTime cancelledDate) {
            this.cancelledDate = cancelledDate;
        }

        public String getCancellationreason() {

            return this.cancellationReason;
        }

        public void setCancellationreason(String cancellationReason) {
            this.cancellationReason = cancellationReason;
        }

        public LocalDateTime getTrialenddate() {

            return this.trialEndDate;
        }

        public void setTrialenddate(LocalDateTime trialEndDate) {
            this.trialEndDate = trialEndDate;
        }

        public String getPromocode() {

            return this.promoCode;
        }

        public void setPromocode(String promoCode) {
            this.promoCode = promoCode;
        }

        public LocalDateTime getCreateddate() {

            return this.createdDate;
        }

        public void setCreateddate(LocalDateTime createdDate) {
            this.createdDate = createdDate;
        }

        public LocalDateTime getLastmodifieddate() {

            return this.lastModifiedDate;
        }

        public void setLastmodifieddate(LocalDateTime lastModifiedDate) {
            this.lastModifiedDate = lastModifiedDate;
        }
    }
