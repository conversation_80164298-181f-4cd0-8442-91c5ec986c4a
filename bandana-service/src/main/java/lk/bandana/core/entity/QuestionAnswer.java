package lk.bandana.matching.entity;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.List;

@

public class QuestionAnswer {

    @
    private Long id;

    @
    private lk.bandana.matching.entity.Question question;

    For single choice questions
    @
    private lk.bandana.matching.entity.QuestionOption selectedOption;

    For multiple choice questions
    @
    private List<lk.bandana.matching.entity.QuestionOption> selectedOptions;

    For text/number/date questions
    private String textAnswer;

    private Integer numberAnswer;

    private String dateAnswer;

    For scale questions (1-10)
    private Integer scaleAnswer;

    public QuestionAnswer() {
    }

    public QuestionAnswer(lk.bandana.matching.entity.Question question, lk.bandana.matching.entity.QuestionOption selectedOption) {
        this.question = question;
        this.selectedOption = selectedOption;
    }

    public QuestionAnswer(lk.bandana.matching.entity.Question question, String textAnswer) {
        this.question = question;
        this.textAnswer = textAnswer;
    }

    public QuestionAnswer(lk.bandana.matching.entity.Question question, Integer numberAnswer) {
        this.question = question;
        this.numberAnswer = numberAnswer;
    }

    public Long getId() {

        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<lk.bandana.matching.entity.QuestionOption> getSelectedoptions() {

        return this.selectedOptions;
    }

    public void setSelectedoptions(List<lk.bandana.matching.entity.QuestionOption> selectedOptions) {
        this.selectedOptions = selectedOptions;
    }

    public String getTextanswer() {

        return this.textAnswer;
    }

    public void setTextanswer(String textAnswer) {
        this.textAnswer = textAnswer;
    }

    public Integer getNumberanswer() {

        return this.numberAnswer;
    }

    public void setNumberanswer(Integer numberAnswer) {
        this.numberAnswer = numberAnswer;
    }

    public String getDateanswer() {

        return this.dateAnswer;
    }

    public void setDateanswer(String dateAnswer) {
        this.dateAnswer = dateAnswer;
    }

    public Integer getScaleanswer() {

        return this.scaleAnswer;
    }

    public void setScaleanswer(Integer scaleAnswer) {
        this.scaleAnswer = scaleAnswer;
    }
}
