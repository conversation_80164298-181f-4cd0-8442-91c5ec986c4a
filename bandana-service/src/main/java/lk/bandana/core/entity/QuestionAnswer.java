package lk.bandana.matching.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.List;

@Node
@Getter
@Setter
public class QuestionAnswer {

    @Id
    private Long id;

    @Relationship("ANSWER_TO")
    private lk.bandana.matching.entity.Question question;

    // For single choice questions
    @Relationship("SELECTED_OPTION")
    private lk.bandana.matching.entity.QuestionOption selectedOption;

    // For multiple choice questions
    @Relationship("SELECTED_OPTIONS")
    private List<lk.bandana.matching.entity.QuestionOption> selectedOptions;

    // For text/number/date questions
    private String textAnswer;
    private Integer numberAnswer;
    private String dateAnswer;

    // For scale questions (1-10)
    private Integer scaleAnswer;

    public QuestionAnswer() {}

    public QuestionAnswer(lk.bandana.matching.entity.Question question, lk.bandana.matching.entity.QuestionOption selectedOption) {
        this.question = question;
        this.selectedOption = selectedOption;
    }

    public QuestionAnswer(lk.bandana.matching.entity.Question question, String textAnswer) {
        this.question = question;
        this.textAnswer = textAnswer;
    }

    public QuestionAnswer(lk.bandana.matching.entity.Question question, Integer numberAnswer) {
        this.question = question;
        this.numberAnswer = numberAnswer;
    }
}
