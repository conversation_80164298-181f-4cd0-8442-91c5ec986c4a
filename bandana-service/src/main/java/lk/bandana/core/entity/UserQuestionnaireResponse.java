package lk.bandana.matching.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;
import java.util.List;

@Node
@Getter
@Setter
public class UserQuestionnaireResponse {

    @Id
    private Long id;

    @Relationship("RESPONSE_BY")
    private lk.bandana.profile.entity.UserProfile userProfile;

    @Relationship("RESPONSE_TO")
    private lk.bandana.matching.entity.Questionnaire questionnaire;

    @Relationship("HAS_ANSWER")
    private List<lk.bandana.matching.entity.QuestionAnswer> answers;

    private Boolean completed = false;
    private Integer completionPercentage = 0;
    private LocalDateTime completedDate;

    @CreatedDate
    private LocalDateTime createdDate;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;
}
