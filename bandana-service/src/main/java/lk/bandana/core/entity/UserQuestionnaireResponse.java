package lk.bandana.matching.entity; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.data.mongodb.core.mapping.DBRef; import org.springframework.data.annotation.CreatedDate; import org.springframework.data.annotation.LastModifiedDate; import java.time.LocalDateTime; import java.util.List; @Document public class UserQuestionnaireResponse { @Id private Long id; @DBRef private lk.bandana.profile.entity.UserProfile userProfile; @DBRef private lk.bandana.matching.entity.Questionnaire questionnaire; @DBRef private List<lk.bandana.matching.entity.QuestionAnswer> answers; private Boolean completed = false; private Integer completionPercentage = 0; private LocalDateTime completedDate; @CreatedDate private LocalDateTime createdDate; @LastModifiedDate private LocalDateTime lastModifiedDate; public Long getId() { return this.id; } public void setId(Long id) { this.id = id; } public List<lk.bandana.matching.entity.QuestionAnswer> getAnswers() { return this.answers; } public void setAnswers(List<lk.bandana.matching.entity.QuestionAnswer> answers) { this.answers = answers; } public LocalDateTime getCompleteddate() { return this.completedDate; } public void setCompleteddate(LocalDateTime completedDate) { this.completedDate = completedDate; } public LocalDateTime getCreateddate() { return this.createdDate; } public void setCreateddate(LocalDateTime createdDate) { this.createdDate = createdDate; } public LocalDateTime getLastmodifieddate() { return this.lastModifiedDate; } public void setLastmodifieddate(LocalDateTime lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; } }