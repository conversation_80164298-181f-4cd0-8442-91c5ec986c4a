package lk.bandana.core.entity; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.stereotype.Component; /** * Created by <PERSON><PERSON><PERSON> on 9/12/2018 */ @Document @Component public class MetaData { @Id private Integer id; private String category; private String name; private String value; public MetaData() { } public MetaData(String category, String name, String value) { this.category = category; this.name = name; this.value = value; } public Integer getId() { return this.id; } public void setId(Integer id) { this.id = id; } public String getCategory() { return this.category; } public void setCategory(String category) { this.category = category; } public String getName() { return this.name; } public void setName(String name) { this.name = name; } public String getValue() { return this.value; } public void setValue(String value) { this.value = value; } 
    @Override
    public String toString() {
        return """MetaData{
            "id=" + id + ", " + "category=" + category + ", " + "name=" + name + ", " + "value=" + value
        }""";
    }

    @Override
    public boolean equals(Object obj) {
        return switch (obj) {
            case null -> false;
            case MetaData other when this == other -> true;
            case MetaData other -> Objects.equals(id, other.id) && Objects.equals(category, other.category) && Objects.equals(name, other.name) && Objects.equals(value, other.value);
            default -> false;
        };
    }
    @Override
    public int hashCode() {
        return Objects.hash(id, category, name, value);
    }
}