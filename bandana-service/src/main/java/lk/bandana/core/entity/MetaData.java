package lk.bandana.core.entity; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.stereotype.Component; /** * Created by <PERSON><PERSON><PERSON> We<PERSON> on 9/12/2018 */ @Document @Component public class MetaData { @Id private Integer id; private String category; private String name; private String value; public MetaData() { } public MetaData(String category, String name, String value) { this.category = category; this.name = name; this.value = value; } public Integer getId() { return this.id; } public void setId(Integer id) { this.id = id; } public String getCategory() { return this.category; } public void setCategory(String category) { this.category = category; } public String getName() { return this.name; } public void setName(String name) { this.name = name; } public String getValue() { return this.value; } public void setValue(String value) { this.value = value; } }