package lk.bandana.core.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON> Weera<PERSON> on 9/12/2018
 */
@Node
@Component
@Getter
@Setter
public class MetaData {

    @Id
    private Integer id;

    private String category;

    private String name;

    private String value;

    public MetaData() {

    }

    public MetaData(String category, String name, String value) {
        this.category = category;
        this.name = name;
        this.value = value;
    }
}
