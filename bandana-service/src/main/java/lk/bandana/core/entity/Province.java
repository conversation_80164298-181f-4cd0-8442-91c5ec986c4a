package lk.bandana.location.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.List;

@Node
@Getter
@Setter
public class Province {

    @Id
    private Long id;

    private String name;
    private String nameInSinhala;
    private String nameInTamil;
    private String code; // e.g., "WP" for Western Province
    private Boolean active = true;

    @Relationship("HAS_DISTRICT")
    private List<lk.bandana.location.entity.District> districts;

    public Province() {}

    public Province(String name, String nameInSinhala, String nameInTamil, String code) {
        this.name = name;
        this.nameInSinhala = nameInSinhala;
        this.nameInTamil = nameInTamil;
        this.code = code;
    }
}
