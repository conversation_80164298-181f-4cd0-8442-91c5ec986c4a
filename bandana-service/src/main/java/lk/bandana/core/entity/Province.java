package lk.bandana.location.entity;

import org.springframework.data.annotation.Id;
import java.util.Objects;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.List;

@

public class Province {

    @
    private Long id;

    private String name;

    private String nameInSinhala;

    private String nameInTamil;

    private String code;

    e.g., "WP" for Western Province
    private Boolean active = true;

    @
    private List<lk.bandana.location.entity.District> districts;

    public Province() {
    }

    public Province(String name, String nameInSinhala, String nameInTamil, String code) {
        this.name = name;
        this.nameInSinhala = nameInSinhala;
        this.nameInTamil = nameInTamil;
        this.code = code;
    }

    public Long getId() {

        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {

        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameinsinhala() {

        return this.nameInSinhala;
    }

    public void setNameinsinhala(String nameInSinhala) {
        this.nameInSinhala = nameInSinhala;
    }

    public String getNameintamil() {

        return this.nameInTamil;
    }

    public void setNameintamil(String nameInTamil) {
        this.nameInTamil = nameInTamil;
    }

    public String getCode() {

        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<lk.bandana.location.entity.District> getDistricts() {

        return this.districts;
    }

    public void setDistricts(List<lk.bandana.location.entity.District> districts) {
        this.districts = districts;
    }

    @Override
    public String toString() {
        return """Province{
            "id=" + id + ", " + "name=" + name + ", " + "nameInSinhala=" + nameInSinhala + ", " + "nameInTamil=" + nameInTamil + ", " + "code=" + code + ", " + "districts=" + districts
        }""";
    }

    @Override
    public boolean equals(Object obj) {
        return switch (obj) {
            case null -> false;
            case Province other when this == other -> true;
            case Province other -> Objects.equals(id, other.id) && Objects.equals(name, other.name) && Objects.equals(nameInSinhala, other.nameInSinhala) && Objects.equals(nameInTamil, other.nameInTamil) && Objects.equals(code, other.code) && Objects.equals(districts, other.districts);
            default -> false;
        };
    }
    @Override
    public int hashCode() {
        return Objects.hash(id, name, nameInSinhala, nameInTamil, code, districts);
    }
}