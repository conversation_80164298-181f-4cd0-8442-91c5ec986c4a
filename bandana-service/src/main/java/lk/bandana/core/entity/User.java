package lk.bandana.user.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Document(collection = "users")
@Getter
@Setter
public class User {

    @Id
    private String id;

    @Field("username")
    private String username;

    @Field("email")
    private String email;

    @Field("password")
    private String password;

    @Field("active")
    private boolean active;

    @Field("first_name")
    private String firstName;

    @Field("last_name")
    private String lastName;

    @Field("enabled")
    private Boolean enabled;

    @Field("phone_number")
    private String phoneNumber;

    @Field("phone_verified")
    private Boolean phoneVerified = false;

    @Field("verification_code")
    private String verificationCode;

    @Field("verification_code_expiry")
    private LocalDateTime verificationCodeExpiry;

    @JsonIgnore
    @Field("last_password_reset_date")
    private Date lastPasswordResetDate;

    @CreatedDate
    @Field("created_date")
    private LocalDateTime createdDate;

    @LastModifiedDate
    @Field("last_modified_date")
    private LocalDateTime lastModifiedDate;

    @DBRef
    @Field("user_roles")
    private List<lk.bandana.user.entity.UserRole> userRoles;

    @Field("current_subscription_id")
    private String currentSubscriptionId;

    @Field("user_profile_id")
    private String userProfileId;

}
