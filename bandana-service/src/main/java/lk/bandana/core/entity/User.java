package lk.bandana.user.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Objects;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.mongodb.core.mapping.Field;

import org.springframework.data.mongodb.core.mapping.DBRef;

import org.springframework.data.annotation.CreatedDate;

import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;

import java.util.Date;

import java.util.List;

@(collection = "users")

public class User {

    @
    private String id;

    @("username")
    private String username;

    @("email")
    private String email;

    @("password")
    private String password;

    @("active")
    private boolean active;

    @("first_name")
    private String firstName;

    @("last_name")
    private String lastName;

    @("enabled")
    private Boolean enabled;

    @("phone_number")
    private String phoneNumber;

    @("phone_verified")
    private Boolean phoneVerified = false;

    @("verification_code")
    private String verificationCode;

    @("verification_code_expiry")
    private LocalDateTime verificationCodeExpiry;

    @
    @("last_password_reset_date")
    private Date lastPasswordResetDate;

    @
    @("created_date")
    private LocalDateTime createdDate;

    @
    @("last_modified_date")
    private LocalDateTime lastModifiedDate;

    @
    @("user_roles")
    private List<lk.bandana.user.entity.UserRole> userRoles;

    @("current_subscription_id")
    private String currentSubscriptionId;

    @("user_profile_id")
    private String userProfileId;

    public String getId() {

        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsername() {

        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {

        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {

        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isActive() {

        return this.active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getFirstname() {

        return this.firstName;
    }

    public void setFirstname(String firstName) {
        this.firstName = firstName;
    }

    public String getLastname() {

        return this.lastName;
    }

    public void setLastname(String lastName) {
        this.lastName = lastName;
    }

    public Boolean isEnabled() {

        return this.enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getPhonenumber() {

        return this.phoneNumber;
    }

    public void setPhonenumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getVerificationcode() {

        return this.verificationCode;
    }

    public void setVerificationcode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public LocalDateTime getVerificationcodeexpiry() {

        return this.verificationCodeExpiry;
    }

    public void setVerificationcodeexpiry(LocalDateTime verificationCodeExpiry) {
        this.verificationCodeExpiry = verificationCodeExpiry;
    }

    public Date getLastpasswordresetdate() {

        return this.lastPasswordResetDate;
    }

    public void setLastpasswordresetdate(Date lastPasswordResetDate) {
        this.lastPasswordResetDate = lastPasswordResetDate;
    }

    public LocalDateTime getCreateddate() {

        return this.createdDate;
    }

    public void setCreateddate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getLastmodifieddate() {

        return this.lastModifiedDate;
    }

    public void setLastmodifieddate(LocalDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public List<lk.bandana.user.entity.UserRole> getUserroles() {

        return this.userRoles;
    }

    public void setUserroles(List<lk.bandana.user.entity.UserRole> userRoles) {
        this.userRoles = userRoles;
    }

    public String getCurrentsubscriptionid() {

        return this.currentSubscriptionId;
    }

    public void setCurrentsubscriptionid(String currentSubscriptionId) {
        this.currentSubscriptionId = currentSubscriptionId;
    }

    public String getUserprofileid() {

        return this.userProfileId;
    }

    public void setUserprofileid(String userProfileId) {
        this.userProfileId = userProfileId;
    }

    @Override
    public String toString() {
        return """User{
            "id=" + id + ", " + "username=" + username + ", " + "email=" + email + ", " + "password=" + password + ", " + "active=" + active + ", " + "firstName=" + firstName + ", " + "lastName=" + lastName + ", " + "enabled=" + enabled + ", " + "phoneNumber=" + phoneNumber + ", " + "verificationCode=" + verificationCode + ", " + "verificationCodeExpiry=" + verificationCodeExpiry + ", " + "lastPasswordResetDate=" + lastPasswordResetDate + ", " + "createdDate=" + createdDate + ", " + "lastModifiedDate=" + lastModifiedDate + ", " + "userRoles=" + userRoles + ", " + "currentSubscriptionId=" + currentSubscriptionId + ", " + "userProfileId=" + userProfileId
        }""";
    }

    @Override
    public boolean equals(Object obj) {
        return switch (obj) {
            case null -> false;
            case User other when this == other -> true;
            case User other -> Objects.equals(id, other.id) && Objects.equals(username, other.username) && Objects.equals(email, other.email) && Objects.equals(password, other.password) && active == other.active && Objects.equals(firstName, other.firstName) && Objects.equals(lastName, other.lastName) && Objects.equals(enabled, other.enabled) && Objects.equals(phoneNumber, other.phoneNumber) && Objects.equals(verificationCode, other.verificationCode) && Objects.equals(verificationCodeExpiry, other.verificationCodeExpiry) && Objects.equals(lastPasswordResetDate, other.lastPasswordResetDate) && Objects.equals(createdDate, other.createdDate) && Objects.equals(lastModifiedDate, other.lastModifiedDate) && Objects.equals(userRoles, other.userRoles) && Objects.equals(currentSubscriptionId, other.currentSubscriptionId) && Objects.equals(userProfileId, other.userProfileId);
            default -> false;
        };
    }
    @Override
    public int hashCode() {
        return Objects.hash(id, username, email, password, active, firstName, lastName, enabled, phoneNumber, verificationCode, verificationCodeExpiry, lastPasswordResetDate, createdDate, lastModifiedDate, userRoles, currentSubscriptionId, userProfileId);
    }
}