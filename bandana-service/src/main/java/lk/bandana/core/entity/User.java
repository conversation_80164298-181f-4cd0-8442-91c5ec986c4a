package lk.bandana.user.entity; import com.fasterxml.jackson.annotation.JsonIgnore; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.data.mongodb.core.mapping.Field; import org.springframework.data.mongodb.core.mapping.DBRef; import org.springframework.data.annotation.CreatedDate; import org.springframework.data.annotation.LastModifiedDate; import java.time.LocalDateTime; import java.util.Date; import java.util.List; @Document(collection = "users") public class User { @Id private String id; @Field("username") private String username; @Field("email") private String email; @Field("password") private String password; @Field("active") private boolean active; @Field("first_name") private String firstName; @Field("last_name") private String lastName; @Field("enabled") private Boolean enabled; @Field("phone_number") private String phoneNumber; @Field("phone_verified") private Boolean phoneVerified = false; @Field("verification_code") private String verificationCode; @Field("verification_code_expiry") private LocalDateTime verificationCodeExpiry; @JsonIgnore @Field("last_password_reset_date") private Date lastPasswordResetDate; @CreatedDate @Field("created_date") private LocalDateTime createdDate; @LastModifiedDate @Field("last_modified_date") private LocalDateTime lastModifiedDate; @DBRef @Field("user_roles") private List<lk.bandana.user.entity.UserRole> userRoles; @Field("current_subscription_id") private String currentSubscriptionId; @Field("user_profile_id") private String userProfileId; public String getId() { return this.id; } public void setId(String id) { this.id = id; } public String getUsername() { return this.username; } public void setUsername(String username) { this.username = username; } public String getEmail() { return this.email; } public void setEmail(String email) { this.email = email; } public String getPassword() { return this.password; } public void setPassword(String password) { this.password = password; } public boolean isActive() { return this.active; } public void setActive(boolean active) { this.active = active; } public String getFirstname() { return this.firstName; } public void setFirstname(String firstName) { this.firstName = firstName; } public String getLastname() { return this.lastName; } public void setLastname(String lastName) { this.lastName = lastName; } public Boolean isEnabled() { return this.enabled; } public void setEnabled(Boolean enabled) { this.enabled = enabled; } public String getPhonenumber() { return this.phoneNumber; } public void setPhonenumber(String phoneNumber) { this.phoneNumber = phoneNumber; } public String getVerificationcode() { return this.verificationCode; } public void setVerificationcode(String verificationCode) { this.verificationCode = verificationCode; } public LocalDateTime getVerificationcodeexpiry() { return this.verificationCodeExpiry; } public void setVerificationcodeexpiry(LocalDateTime verificationCodeExpiry) { this.verificationCodeExpiry = verificationCodeExpiry; } public Date getLastpasswordresetdate() { return this.lastPasswordResetDate; } public void setLastpasswordresetdate(Date lastPasswordResetDate) { this.lastPasswordResetDate = lastPasswordResetDate; } public LocalDateTime getCreateddate() { return this.createdDate; } public void setCreateddate(LocalDateTime createdDate) { this.createdDate = createdDate; } public LocalDateTime getLastmodifieddate() { return this.lastModifiedDate; } public void setLastmodifieddate(LocalDateTime lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; } public List<lk.bandana.user.entity.UserRole> getUserroles() { return this.userRoles; } public void setUserroles(List<lk.bandana.user.entity.UserRole> userRoles) { this.userRoles = userRoles; } public String getCurrentsubscriptionid() { return this.currentSubscriptionId; } public void setCurrentsubscriptionid(String currentSubscriptionId) { this.currentSubscriptionId = currentSubscriptionId; } public String getUserprofileid() { return this.userProfileId; } public void setUserprofileid(String userProfileId) { this.userProfileId = userProfileId; } }