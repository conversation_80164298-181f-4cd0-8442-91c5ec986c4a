package lk.bandana.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;
import org.springframework.stereotype.Component;

import java.util.Date;

@Node
@Component
@Getter
@Setter
public class LoggedUser {

    @Id @GeneratedValue
    private Long id;

    private String token;

    @Relationship
    private User user;

    @JsonIgnore
    private Date lastLogin;

    public LoggedUser() {

    }

    public LoggedUser(String token) {
        this.token = token;
    }

}
