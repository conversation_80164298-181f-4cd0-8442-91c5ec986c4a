package lk.bandana.core.entity; import com.fasterxml.jackson.annotation.JsonIgnore; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.data.mongodb.core.mapping.DBRef; import org.springframework.stereotype.Component; import java.util.Date; @Document @Component public class LoggedUser { @Id private Long id; private String token; @DBRef private User user; @JsonIgnore private Date lastLogin; public LoggedUser() { } public LoggedUser(String token) { this.token = token; } public Long getId() { return this.id; } public void setId(Long id) { this.id = id; } public String getToken() { return this.token; } public void setToken(String token) { this.token = token; } public User getUser() { return this.user; } public void setUser(User user) { this.user = user; } public Date getLastlogin() { return this.lastLogin; } public void setLastlogin(Date lastLogin) { this.lastLogin = lastLogin; } }