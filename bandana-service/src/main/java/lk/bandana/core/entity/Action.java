package lk.bandana.core.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Node
@Component
@Getter
@Setter
public class Action {

    @Id
    @GeneratedValue
    private Long id;

    private MetaData type;

    private String reference;

    //operator ( + , -)
    private String operator;

    //quantity of change
    private String change;

    private String remark;

    @CreatedDate
    private LocalDateTime createdDate;

    @CreatedBy
    private String createdBy;

}
