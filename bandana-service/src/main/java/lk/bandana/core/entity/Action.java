package lk.bandana.core.entity; import org.springframework.data.annotation.CreatedBy; import org.springframework.data.annotation.CreatedDate; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.stereotype.Component; import java.time.LocalDateTime; @Document @Component public class Action { @Id private Long id; private MetaData type; private String reference; //operator ( + , -) private String operator; //quantity of change private String change; private String remark; @CreatedDate private LocalDateTime createdDate; @CreatedBy private String createdBy; public Long getId() { return this.id; } public void setId(Long id) { this.id = id; } public MetaData getType() { return this.type; } public void setType(MetaData type) { this.type = type; } public String getReference() { return this.reference; } public void setReference(String reference) { this.reference = reference; } public String getOperator() { return this.operator; } public void setOperator(String operator) { this.operator = operator; } public String getChange() { return this.change; } public void setChange(String change) { this.change = change; } public String getRemark() { return this.remark; } public void setRemark(String remark) { this.remark = remark; } public LocalDateTime getCreateddate() { return this.createdDate; } public void setCreateddate(LocalDateTime createdDate) { this.createdDate = createdDate; } public String getCreatedby() { return this.createdBy; } public void setCreatedby(String createdBy) { this.createdBy = createdBy; } }