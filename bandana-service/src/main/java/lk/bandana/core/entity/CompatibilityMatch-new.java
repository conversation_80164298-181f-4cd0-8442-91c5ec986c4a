package lk.bandana.core.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * CompatibilityMatch - Entity representing compatibility between two profiles
 */
@Document
public class CompatibilityMatch {

    @Id
    private String id;

    @DBRef
    private UserProfile profile1;

    @DBRef
    private UserProfile profile2;

    private Integer overallScore;
    private Map<String, Integer> categoryScores;
    private LocalDateTime calculatedAt;
    private LocalDateTime lastUpdated;
    private Boolean isVisible;
    private Boolean isVisibleToAgents;
    private MatchStatus status;

    // Constructors
    public CompatibilityMatch() {
        this.calculatedAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.isVisible = true;
        this.isVisibleToAgents = false;
        this.status = MatchStatus.ACTIVE;
    }

    public CompatibilityMatch(UserProfile profile1, UserProfile profile2, Integer overallScore) {
        this();
        this.profile1 = profile1;
        this.profile2 = profile2;
        this.overallScore = overallScore;
    }

    // Getters and setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public UserProfile getProfile1() {
        return profile1;
    }

    public void setProfile1(UserProfile profile1) {
        this.profile1 = profile1;
    }

    public UserProfile getProfile2() {
        return profile2;
    }

    public void setProfile2(UserProfile profile2) {
        this.profile2 = profile2;
    }

    public Integer getOverallScore() {
        return overallScore;
    }

    public void setOverallScore(Integer overallScore) {
        this.overallScore = overallScore;
    }

    public Map<String, Integer> getCategoryScores() {
        return categoryScores;
    }

    public void setCategoryScores(Map<String, Integer> categoryScores) {
        this.categoryScores = categoryScores;
    }

    public LocalDateTime getCalculatedAt() {
        return calculatedAt;
    }

    public void setCalculatedAt(LocalDateTime calculatedAt) {
        this.calculatedAt = calculatedAt;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public Boolean getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(Boolean isVisible) {
        this.isVisible = isVisible;
    }

    public Boolean getIsVisibleToAgents() {
        return isVisibleToAgents;
    }

    public void setIsVisibleToAgents(Boolean isVisibleToAgents) {
        this.isVisibleToAgents = isVisibleToAgents;
    }

    public MatchStatus getStatus() {
        return status;
    }

    public void setStatus(MatchStatus status) {
        this.status = status;
    }

    // Utility methods
    public boolean isHighQualityMatch() {
        return overallScore != null && overallScore >= 80;
    }

    public boolean isMediumQualityMatch() {
        return overallScore != null && overallScore >= 60 && overallScore < 80;
    }

    public boolean isLowQualityMatch() {
        return overallScore != null && overallScore < 60;
    }

    public void updateLastModified() {
        this.lastUpdated = LocalDateTime.now();
    }

    // Enum for match status
    public enum MatchStatus {
        ACTIVE,
        HIDDEN,
        ARCHIVED,
        BLOCKED
    }

    @Override
    public String toString() {
        return "CompatibilityMatch{" +
                "id='" + id + '\'' +
                ", overallScore=" + overallScore +
                ", calculatedAt=" + calculatedAt +
                ", status=" + status +
                '}';
    }
}
