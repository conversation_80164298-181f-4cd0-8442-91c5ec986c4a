package lk.bandana.user.entity;
/**
 * Created by <PERSON><PERSON><PERSON> on 4/10/2018
 */

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;
import java.util.List;

@Node
@Getter
@Setter
public class UserRole {

    @Id
    private Integer id;

    private String name;

    private String displayName;

    private String description;

    private Boolean active = true;

    // Role hierarchy and permissions
    private Integer level = 1; // 1=User, 2=Agent, 3=Admin
    private List<String> permissions;

    @CreatedDate
    private LocalDateTime createdDate;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    // Predefined role constants
    public static final String ROLE_USER = "ROLE_USER";
    public static final String ROLE_AGENT = "ROLE_AGENT";
    public static final String ROLE_ADMIN = "ROLE_ADMIN";
    public static final String ROLE_SUPER_ADMIN = "ROLE_SUPER_ADMIN";

    public UserRole() {}

    public UserRole(String name, String displayName, Integer level) {
        this.name = name;
        this.displayName = displayName;
        this.level = level;
    }
}
