package lk.bandana.user.entity; /** * Created by <PERSON><PERSON><PERSON> on 4/10/2018 */ import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.data.mongodb.core.mapping.Field; import org.springframework.data.annotation.CreatedDate; import org.springframework.data.annotation.LastModifiedDate; import java.time.LocalDateTime; import java.util.List; @Document(collection = "user_roles") public class UserRole { @Id private String id; @Field("name") private String name; @Field("display_name") private String displayName; @Field("description") private String description; @Field("active") private Boolean active = true; // Role hierarchy and permissions @Field("level") private Integer level = 1; // 1=User, 2=Agent, 3=Admin @Field("permissions") private List<String> permissions; @CreatedDate private LocalDateTime createdDate; @LastModifiedDate private LocalDateTime lastModifiedDate; // Predefined role constants public static final String ROLE_USER = "ROLE_USER"; public static final String ROLE_AGENT = "ROLE_AGENT"; public static final String ROLE_ADMIN = "ROLE_ADMIN"; public static final String ROLE_SUPER_ADMIN = "ROLE_SUPER_ADMIN"; public UserRole() {} public UserRole(String name, String displayName, Integer level) { this.name = name; this.displayName = displayName; this.level = level; } public String getId() { return this.id; } public void setId(String id) { this.id = id; } public String getName() { return this.name; } public void setName(String name) { this.name = name; } public String getDisplayname() { return this.displayName; } public void setDisplayname(String displayName) { this.displayName = displayName; } public String getDescription() { return this.description; } public void setDescription(String description) { this.description = description; } public List<String> getPermissions() { return this.permissions; } public void setPermissions(List<String> permissions) { this.permissions = permissions; } public LocalDateTime getCreateddate() { return this.createdDate; } public void setCreateddate(LocalDateTime createdDate) { this.createdDate = createdDate; } public LocalDateTime getLastmodifieddate() { return this.lastModifiedDate; } public void setLastmodifieddate(LocalDateTime lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; } }