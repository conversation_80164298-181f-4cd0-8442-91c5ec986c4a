package lk.bandana.core.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import java.time.Instant;
import java.time.format.DateTimeFormatter;

/**
 * Response record using Java 23 String Templates and modern patterns
 */
@Document
public record Response<T>(
    @Id String id,
    boolean success,
    String message,
    T data,
    String errorCode,
    Instant timestamp
) {
    
    // Compact constructor with validation using pattern matching
    public Response {
        timestamp = switch (timestamp) {
            case null -> Instant.now();
            case Instant t -> t;
        };
        
        message = switch (message) {
            case null when success -> "Operation completed successfully";
            case null when !success -> "Operation failed";
            case String m -> m;
        };
    }
    
    // Static factory methods using String Templates (Java 23 Preview)
    public static <T> Response<T> success(T data) {
        return new Response<>(
            null,
            true,
            STR."Successfully processed data of type \{data.getClass().getSimpleName()}",
            data,
            null,
            Instant.now()
        );
    }
    
    public static <T> Response<T> success(String operation, T data) {
        return new Response<>(
            null,
            true,
            STR."\{operation} completed successfully",
            data,
            null,
            Instant.now()
        );
    }
    
    public static <T> Response<T> error(String message, String errorCode) {
        return new Response<>(
            null,
            false,
            STR."Error [\{errorCode}]: \{message}",
            null,
            errorCode,
            Instant.now()
        );
    }
    
    // Pattern matching methods
    public String getFormattedMessage() {
        return switch (this) {
            case Response(_, true, var msg, _, _, var time) -> 
                STR."✓ \{msg} at \{DateTimeFormatter.ISO_INSTANT.format(time)}";
            case Response(_, false, var msg, _, var code, var time) -> 
                STR."✗ [\{code}] \{msg} at \{DateTimeFormatter.ISO_INSTANT.format(time)}";
        };
    }
    
    // Unnamed patterns for when we don't need all fields
    public boolean hasValidData() {
        return switch (this) {
            case Response(_, true, _, var data, _, _) when data != null -> true;
            case Response(_, _, _, _, _, _) -> false;
        };
    }
    
    // Modern toString using String Templates
    @Override
    public String toString() {
        return STR."""
            Response {
                success: \{success}
                message: "\{message}"
                hasData: \{data != null}
                timestamp: \{timestamp}
            }
            """;
    }
}
