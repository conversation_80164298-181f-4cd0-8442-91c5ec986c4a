package lk.bandana.core.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import java.time.Instant;
import java.time.format.DateTimeFormatter;

/**
 * Response record using Java 17 features
 */
@Document
public record Response<T>(
    @Id String id,
    boolean success,
    String message,
    T data,
    String errorCode,
    Instant timestamp
) {
    
    // Compact constructor with validation
    public Response {
        if (timestamp == null) {
            timestamp = Instant.now();
        }
        
        if (message == null) {
            message = success ? "Operation completed successfully" : "Operation failed";
        }
    }
    
    // Static factory methods
    public static <T> Response<T> success(T data) {
        return new Response<>(
            null,
            true,
            "Successfully processed data of type " + data.getClass().getSimpleName(),
            data,
            null,
            Instant.now()
        );
    }
    
    public static <T> Response<T> success(String operation, T data) {
        return new Response<>(
            null,
            true,
            operation + " completed successfully",
            data,
            null,
            Instant.now()
        );
    }
    
    public static <T> Response<T> error(String message, String errorCode) {
        return new Response<>(
            null,
            false,
            "Error [" + errorCode + "]: " + message,
            null,
            errorCode,
            Instant.now()
        );
    }
    
    // Pattern matching methods using Java 17 features
    public String getFormattedMessage() {
        var timeStr = DateTimeFormatter.ISO_INSTANT.format(timestamp);
        return success ? 
            "✓ " + message + " at " + timeStr :
            "✗ [" + errorCode + "] " + message + " at " + timeStr;
    }
    
    public boolean hasValidData() {
        return success && data != null;
    }
    
    // Modern toString using text blocks (Java 15+)
    @Override
    public String toString() {
        return """
            Response {
                success: %s
                message: "%s"
                hasData: %s
                timestamp: %s
            }
            """.formatted(success, message, data != null, timestamp);
    }
}
