package lk.bandana.matching.entity; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.data.mongodb.core.mapping.DBRef; import org.springframework.data.annotation.CreatedDate; import org.springframework.data.annotation.LastModifiedDate; import java.time.LocalDateTime; import java.util.Map; @Document public class CompatibilityMatch { @Id private Long id; @DBRef private lk.bandana.profile.entity.UserProfile profileA; @DBRef private lk.bandana.profile.entity.UserProfile profileB; // Overall compatibility score (0-100) private Integer overallScore; // Individual category scores private Integer ageCompatibility; private Integer locationCompatibility; private Integer educationCompatibility; private Integer occupationCompatibility; private Integer religionCompatibility; private Integer casteCompatibility; private Integer interestCompatibility; private Integer lifestyleCompatibility; private Integer familyCompatibility; private Integer horoscopeCompatibility; // Weighted scores based on user preferences private Map<String, Integer> categoryWeights; private Map<String, Integer> categoryScores; // Match status private MatchStatus status = MatchStatus.CALCULATED; private Boolean visible = true; // Agent visibility (for cross-agent matching) private Boolean visibleToAgents = false; private Integer minimumScoreForAgentVisibility = 80; // User interactions private Boolean profileAViewed = false; private Boolean profileBViewed = false; private LocalDateTime profileAViewedDate; private LocalDateTime profileBViewedDate; private Boolean contactRequestSent = false; private LocalDateTime contactRequestDate; private ContactRequestStatus contactRequestStatus; // Calculation metadata private String calculationVersion; // For tracking algorithm changes private LocalDateTime calculatedDate; private Boolean needsRecalculation = false; @CreatedDate private LocalDateTime createdDate; @LastModifiedDate private LocalDateTime lastModifiedDate; // Helper methods public boolean isHighQualityMatch() { return overallScore != null && overallScore >= 80; } public boolean isVisibleToAgent(lk.bandana.user.entity.User agent) { if (!visibleToAgents) return false; if (overallScore == null) return false; return overallScore >= minimumScoreForAgentVisibility; } public enum MatchStatus { CALCULATED, VIEWED, CONTACTED, HIDDEN, BLOCKED } public enum ContactRequestStatus { PENDING, ACCEPTED, REJECTED, EXPIRED } public Long getId() { return this.id; } public void setId(Long id) { this.id = id; } public Integer getOverallscore() { return this.overallScore; } public void setOverallscore(Integer overallScore) { this.overallScore = overallScore; } public Integer getAgecompatibility() { return this.ageCompatibility; } public void setAgecompatibility(Integer ageCompatibility) { this.ageCompatibility = ageCompatibility; } public Integer getLocationcompatibility() { return this.locationCompatibility; } public void setLocationcompatibility(Integer locationCompatibility) { this.locationCompatibility = locationCompatibility; } public Integer getEducationcompatibility() { return this.educationCompatibility; } public void setEducationcompatibility(Integer educationCompatibility) { this.educationCompatibility = educationCompatibility; } public Integer getOccupationcompatibility() { return this.occupationCompatibility; } public void setOccupationcompatibility(Integer occupationCompatibility) { this.occupationCompatibility = occupationCompatibility; } public Integer getReligioncompatibility() { return this.religionCompatibility; } public void setReligioncompatibility(Integer religionCompatibility) { this.religionCompatibility = religionCompatibility; } public Integer getCastecompatibility() { return this.casteCompatibility; } public void setCastecompatibility(Integer casteCompatibility) { this.casteCompatibility = casteCompatibility; } public Integer getInterestcompatibility() { return this.interestCompatibility; } public void setInterestcompatibility(Integer interestCompatibility) { this.interestCompatibility = interestCompatibility; } public Integer getLifestylecompatibility() { return this.lifestyleCompatibility; } public void setLifestylecompatibility(Integer lifestyleCompatibility) { this.lifestyleCompatibility = lifestyleCompatibility; } public Integer getFamilycompatibility() { return this.familyCompatibility; } public void setFamilycompatibility(Integer familyCompatibility) { this.familyCompatibility = familyCompatibility; } public Integer getHoroscopecompatibility() { return this.horoscopeCompatibility; } public void setHoroscopecompatibility(Integer horoscopeCompatibility) { this.horoscopeCompatibility = horoscopeCompatibility; } public Map<String, Integer> getCategoryweights() { return this.categoryWeights; } public void setCategoryweights(Map<String, Integer> categoryWeights) { this.categoryWeights = categoryWeights; } public Map<String, Integer> getCategoryscores() { return this.categoryScores; } public void setCategoryscores(Map<String, Integer> categoryScores) { this.categoryScores = categoryScores; } public LocalDateTime getProfileavieweddate() { return this.profileAViewedDate; } public void setProfileavieweddate(LocalDateTime profileAViewedDate) { this.profileAViewedDate = profileAViewedDate; } public LocalDateTime getProfilebvieweddate() { return this.profileBViewedDate; } public void setProfilebvieweddate(LocalDateTime profileBViewedDate) { this.profileBViewedDate = profileBViewedDate; } public LocalDateTime getContactrequestdate() { return this.contactRequestDate; } public void setContactrequestdate(LocalDateTime contactRequestDate) { this.contactRequestDate = contactRequestDate; } public ContactRequestStatus getContactrequeststatus() { return this.contactRequestStatus; } public void setContactrequeststatus(ContactRequestStatus contactRequestStatus) { this.contactRequestStatus = contactRequestStatus; } public String getCalculationversion() { return this.calculationVersion; } public void setCalculationversion(String calculationVersion) { this.calculationVersion = calculationVersion; } public LocalDateTime getCalculateddate() { return this.calculatedDate; } public void setCalculateddate(LocalDateTime calculatedDate) { this.calculatedDate = calculatedDate; } public LocalDateTime getCreateddate() { return this.createdDate; } public void setCreateddate(LocalDateTime createdDate) { this.createdDate = createdDate; } public LocalDateTime getLastmodifieddate() { return this.lastModifiedDate; } public void setLastmodifieddate(LocalDateTime lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; } }