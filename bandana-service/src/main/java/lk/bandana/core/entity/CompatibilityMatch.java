package lk.bandana.matching.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;
import java.util.Map;

@Node
@Getter
@Setter
public class CompatibilityMatch {

    @Id
    private Long id;

    @Relationship("PROFILE_A")
    private lk.bandana.profile.entity.UserProfile profileA;

    @Relationship("PROFILE_B")
    private lk.bandana.profile.entity.UserProfile profileB;

    // Overall compatibility score (0-100)
    private Integer overallScore;

    // Individual category scores
    private Integer ageCompatibility;
    private Integer locationCompatibility;
    private Integer educationCompatibility;
    private Integer occupationCompatibility;
    private Integer religionCompatibility;
    private Integer casteCompatibility;
    private Integer interestCompatibility;
    private Integer lifestyleCompatibility;
    private Integer familyCompatibility;
    private Integer horoscopeCompatibility;

    // Weighted scores based on user preferences
    private Map<String, Integer> categoryWeights;
    private Map<String, Integer> categoryScores;

    // Match status
    private MatchStatus status = MatchStatus.CALCULATED;
    private Boolean visible = true;

    // Agent visibility (for cross-agent matching)
    private Boolean visibleToAgents = false;
    private Integer minimumScoreForAgentVisibility = 80;

    // User interactions
    private Boolean profileAViewed = false;
    private Boolean profileBViewed = false;
    private LocalDateTime profileAViewedDate;
    private LocalDateTime profileBViewedDate;

    private Boolean contactRequestSent = false;
    private LocalDateTime contactRequestDate;
    private ContactRequestStatus contactRequestStatus;

    // Calculation metadata
    private String calculationVersion; // For tracking algorithm changes
    private LocalDateTime calculatedDate;
    private Boolean needsRecalculation = false;

    @CreatedDate
    private LocalDateTime createdDate;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    // Helper methods
    public boolean isHighQualityMatch() {
        return overallScore != null && overallScore >= 80;
    }

    public boolean isVisibleToAgent(lk.bandana.user.entity.User agent) {
        if (!visibleToAgents) return false;
        if (overallScore == null) return false;
        return overallScore >= minimumScoreForAgentVisibility;
    }

    public enum MatchStatus {
        CALCULATED,
        VIEWED,
        CONTACTED,
        HIDDEN,
        BLOCKED
    }

    public enum ContactRequestStatus {
        PENDING,
        ACCEPTED,
        REJECTED,
        EXPIRED
    }
}
