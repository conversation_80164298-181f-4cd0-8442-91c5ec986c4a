package lk.bandana.matching.entity; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.data.mongodb.core.mapping.DBRef; import org.springframework.data.annotation.CreatedDate; import org.springframework.data.annotation.LastModifiedDate; import java.time.LocalDateTime; import java.util.List; @Document public class Questionnaire { @Id private Long id; private String title; private String description; private QuestionnaireType type; private Boolean active = true; private Integer version = 1; private Integer sortOrder = 0; // Target audience private String targetGender; // MALE, FEMALE, ALL private String targetUserType; // REGULAR, AGENT, ALL @DBRef private List<lk.bandana.matching.entity.Question> questions; @CreatedDate private LocalDateTime createdDate; @LastModifiedDate private LocalDateTime lastModifiedDate; public enum QuestionnaireType { COMPATIBILITY, PERSONALITY, LIFESTYLE, FAMILY_VALUES, CAREER_GOALS, RELATIONSHIP_EXPECTATIONS } public Long getId() { return this.id; } public void setId(Long id) { this.id = id; } public String getTitle() { return this.title; } public void setTitle(String title) { this.title = title; } public String getDescription() { return this.description; } public void setDescription(String description) { this.description = description; } public QuestionnaireType getType() { return this.type; } public void setType(QuestionnaireType type) { this.type = type; } public String getTargetgender() { return this.targetGender; } public void setTargetgender(String targetGender) { this.targetGender = targetGender; } public String getTargetusertype() { return this.targetUserType; } public void setTargetusertype(String targetUserType) { this.targetUserType = targetUserType; } public List<lk.bandana.matching.entity.Question> getQuestions() { return this.questions; } public void setQuestions(List<lk.bandana.matching.entity.Question> questions) { this.questions = questions; } public LocalDateTime getCreateddate() { return this.createdDate; } public void setCreateddate(LocalDateTime createdDate) { this.createdDate = createdDate; } public LocalDateTime getLastmodifieddate() { return this.lastModifiedDate; } public void setLastmodifieddate(LocalDateTime lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; } }