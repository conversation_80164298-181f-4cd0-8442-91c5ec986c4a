package lk.bandana.matching.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;
import java.util.List;

@Node
@Getter
@Setter
public class Questionnaire {

    @Id
    private Long id;

    private String title;
    private String description;
    private QuestionnaireType type;
    private Boolean active = true;
    private Integer version = 1;
    private Integer sortOrder = 0;

    // Target audience
    private String targetGender; // MALE, FEMALE, ALL
    private String targetUserType; // REGULAR, AGENT, ALL

    @Relationship("HAS_QUESTION")
    private List<lk.bandana.matching.entity.Question> questions;

    @CreatedDate
    private LocalDateTime createdDate;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    public enum QuestionnaireType {
        COMPATIBILITY,
        PERSONALITY,
        LIFESTYLE,
        FAMILY_VALUES,
        CAREER_GOALS,
        REL<PERSON>IONSHIP_EXPECTATIONS
    }
}
