package lk.bandana.subscription.entity;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.mongodb.core.mapping.DBRef;

import org.springframework.data.annotation.CreatedDate;

import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;

import java.time.LocalDateTime;

@

public class Payment {

    @
    private Long id;

    @
    private lk.bandana.subscription.entity.UserSubscription subscription;

    @
    private lk.bandana.user.entity.User user;

    Payment details
    private BigDecimal amount;

    private String currency = "LKR";

    private PaymentStatus status = PaymentStatus.PENDING;

    private PaymentMethod paymentMethod;

    Transaction information
    private String transactionId;

    private String gatewayTransactionId;

    private String gatewayResponse;

    private String paymentGateway;

    PAYPAL, STRIPE, LOCAL_BANK, etc.
    Payment dates
    private LocalDateTime paymentDate;

    private LocalDateTime processedDate;

    Billing information
    private String billingName;

    private String billingEmail;

    private String billingPhone;

    private String billingAddress;

    private String billingCity;

    private String billingCountry;

    private String billingPostalCode;

    Card information (if applicable)
    private String cardLast4Digits;

    private String cardType;

    VISA, MASTERCARD, etc.
    Failure information
    private String failureReason;

    private String failureCode;

    private Integer retryCount = 0;

    Refund information
    private Boolean refunded = false;

    private BigDecimal refundAmount = BigDecimal.ZERO;

    private LocalDateTime refundDate;

    private String refundReason;

    Invoice information
    private String invoiceNumber;

    private String invoiceUrl;

    @
    private LocalDateTime createdDate;

    @
    private LocalDateTime lastModifiedDate;

    public enum PaymentStatus {
        PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED, REFUNDED, PARTIALLY_REFUNDED }

        public enum PaymentMethod {
            CREDIT_CARD, DEBIT_CARD, BANK_TRANSFER, PAYPAL, MOBILE_PAYMENT, CASH, OTHER }

            public Long getId() {

                return this.id;
            }

            public void setId(Long id) {
                this.id = id;
            }

            public BigDecimal getAmount() {

                return this.amount;
            }

            public void setAmount(BigDecimal amount) {
                this.amount = amount;
            }

            public PaymentMethod getPaymentmethod() {

                return this.paymentMethod;
            }

            public void setPaymentmethod(PaymentMethod paymentMethod) {
                this.paymentMethod = paymentMethod;
            }

            public String getTransactionid() {

                return this.transactionId;
            }

            public void setTransactionid(String transactionId) {
                this.transactionId = transactionId;
            }

            public String getGatewaytransactionid() {

                return this.gatewayTransactionId;
            }

            public void setGatewaytransactionid(String gatewayTransactionId) {
                this.gatewayTransactionId = gatewayTransactionId;
            }

            public String getGatewayresponse() {

                return this.gatewayResponse;
            }

            public void setGatewayresponse(String gatewayResponse) {
                this.gatewayResponse = gatewayResponse;
            }

            public String getPaymentgateway() {

                return this.paymentGateway;
            }

            public void setPaymentgateway(String paymentGateway) {
                this.paymentGateway = paymentGateway;
            }

            public LocalDateTime getPaymentdate() {

                return this.paymentDate;
            }

            public void setPaymentdate(LocalDateTime paymentDate) {
                this.paymentDate = paymentDate;
            }

            public LocalDateTime getProcesseddate() {

                return this.processedDate;
            }

            public void setProcesseddate(LocalDateTime processedDate) {
                this.processedDate = processedDate;
            }

            public String getBillingname() {

                return this.billingName;
            }

            public void setBillingname(String billingName) {
                this.billingName = billingName;
            }

            public String getBillingemail() {

                return this.billingEmail;
            }

            public void setBillingemail(String billingEmail) {
                this.billingEmail = billingEmail;
            }

            public String getBillingphone() {

                return this.billingPhone;
            }

            public void setBillingphone(String billingPhone) {
                this.billingPhone = billingPhone;
            }

            public String getBillingaddress() {

                return this.billingAddress;
            }

            public void setBillingaddress(String billingAddress) {
                this.billingAddress = billingAddress;
            }

            public String getBillingcity() {

                return this.billingCity;
            }

            public void setBillingcity(String billingCity) {
                this.billingCity = billingCity;
            }

            public String getBillingcountry() {

                return this.billingCountry;
            }

            public void setBillingcountry(String billingCountry) {
                this.billingCountry = billingCountry;
            }

            public String getBillingpostalcode() {

                return this.billingPostalCode;
            }

            public void setBillingpostalcode(String billingPostalCode) {
                this.billingPostalCode = billingPostalCode;
            }

            public String getCardlast4digits() {

                return this.cardLast4Digits;
            }

            public void setCardlast4digits(String cardLast4Digits) {
                this.cardLast4Digits = cardLast4Digits;
            }

            public String getCardtype() {

                return this.cardType;
            }

            public void setCardtype(String cardType) {
                this.cardType = cardType;
            }

            public String getFailurereason() {

                return this.failureReason;
            }

            public void setFailurereason(String failureReason) {
                this.failureReason = failureReason;
            }

            public String getFailurecode() {

                return this.failureCode;
            }

            public void setFailurecode(String failureCode) {
                this.failureCode = failureCode;
            }

            public LocalDateTime getRefunddate() {

                return this.refundDate;
            }

            public void setRefunddate(LocalDateTime refundDate) {
                this.refundDate = refundDate;
            }

            public String getRefundreason() {

                return this.refundReason;
            }

            public void setRefundreason(String refundReason) {
                this.refundReason = refundReason;
            }

            public String getInvoicenumber() {

                return this.invoiceNumber;
            }

            public void setInvoicenumber(String invoiceNumber) {
                this.invoiceNumber = invoiceNumber;
            }

            public String getInvoiceurl() {

                return this.invoiceUrl;
            }

            public void setInvoiceurl(String invoiceUrl) {
                this.invoiceUrl = invoiceUrl;
            }

            public LocalDateTime getCreateddate() {

                return this.createdDate;
            }

            public void setCreateddate(LocalDateTime createdDate) {
                this.createdDate = createdDate;
            }

            public LocalDateTime getLastmodifieddate() {

                return this.lastModifiedDate;
            }

            public void setLastmodifieddate(LocalDateTime lastModifiedDate) {
                this.lastModifiedDate = lastModifiedDate;
            }
        }
