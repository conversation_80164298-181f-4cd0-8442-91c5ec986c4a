package lk.bandana.subscription.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Node
@Getter
@Setter
public class Payment {

    @Id
    private Long id;

    @Relationship("PAYMENT_FOR")
    private lk.bandana.subscription.entity.UserSubscription subscription;

    @Relationship("PAID_BY")
    private lk.bandana.user.entity.User user;

    // Payment details
    private BigDecimal amount;
    private String currency = "LKR";
    private PaymentStatus status = PaymentStatus.PENDING;
    private PaymentMethod paymentMethod;

    // Transaction information
    private String transactionId;
    private String gatewayTransactionId;
    private String gatewayResponse;
    private String paymentGateway; // PAYPAL, STRIPE, LOCAL_BANK, etc.

    // Payment dates
    private LocalDateTime paymentDate;
    private LocalDateTime processedDate;

    // Billing information
    private String billingName;
    private String billingEmail;
    private String billingPhone;
    private String billingAddress;
    private String billingCity;
    private String billingCountry;
    private String billingPostalCode;

    // Card information (if applicable)
    private String cardLast4Digits;
    private String cardType; // VISA, MASTERCARD, etc.

    // Failure information
    private String failureReason;
    private String failureCode;
    private Integer retryCount = 0;

    // Refund information
    private Boolean refunded = false;
    private BigDecimal refundAmount = BigDecimal.ZERO;
    private LocalDateTime refundDate;
    private String refundReason;

    // Invoice information
    private String invoiceNumber;
    private String invoiceUrl;

    @CreatedDate
    private LocalDateTime createdDate;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    public enum PaymentStatus {
        PENDING,
        PROCESSING,
        COMPLETED,
        FAILED,
        CANCELLED,
        REFUNDED,
        PARTIALLY_REFUNDED
    }

    public enum PaymentMethod {
        CREDIT_CARD,
        DEBIT_CARD,
        BANK_TRANSFER,
        PAYPAL,
        MOBILE_PAYMENT,
        CASH,
        OTHER
    }
}
