package lk.bandana.configuration.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;

@Node
@Getter
@Setter
public class SystemConfiguration {

    @Id
    private Long id;

    private String configKey;
    private String configValue;
    private String description;
    private ConfigType configType;
    private String category;

    // Validation
    private String validationRule; // JSON or regex for validation
    private String defaultValue;
    private Boolean required = false;

    // UI display
    private String displayName;
    private String helpText;
    private Integer sortOrder = 0;
    private Boolean visible = true;

    @CreatedDate
    private LocalDateTime createdDate;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    public enum ConfigType {
        STRING,
        INTEGER,
        DECIMAL,
        BOOLEAN,
        JSON,
        EMAIL,
        URL,
        PHONE
    }

    // Common configuration keys as constants
    public static final String AGENT_MATCH_THRESHOLD = "agent.match.threshold";
    public static final String PROFILE_APPROVAL_REQUIRED = "profile.approval.required";
    public static final String SMS_VERIFICATION_ENABLED = "sms.verification.enabled";
    public static final String MAX_PROFILE_PHOTOS = "profile.photos.max";
    public static final String DEFAULT_SUBSCRIPTION_DAYS = "subscription.default.days";
    public static final String PAYMENT_GATEWAY_ENABLED = "payment.gateway.enabled";
    public static final String HOROSCOPE_MATCHING_WEIGHT = "horoscope.matching.weight";
    public static final String AUTO_PROFILE_APPROVAL = "profile.auto.approval";
    public static final String CONTACT_REQUEST_LIMIT = "contact.request.limit";
    public static final String PROFILE_VIEW_LIMIT = "profile.view.limit";

    public SystemConfiguration() {}

    public SystemConfiguration(String configKey, String configValue, String description, ConfigType configType) {
        this.configKey = configKey;
        this.configValue = configValue;
        this.description = description;
        this.configType = configType;
    }
}
