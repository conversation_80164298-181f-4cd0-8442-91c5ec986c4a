package lk.bandana.configuration.entity;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.annotation.CreatedDate;

import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;

@

public class SystemConfiguration {

    @
    private Long id;

    private String configKey;

    private String configValue;

    private String description;

    private ConfigType configType;

    private String category;

    Validation
    private String validationRule;

    JSON or regex for validation
    private String defaultValue;

    private Boolean required = false;

    UI display
    private String displayName;

    private String helpText;

    private Integer sortOrder = 0;

    private Boolean visible = true;

    @
    private LocalDateTime createdDate;

    @
    private LocalDateTime lastModifiedDate;

    public enum ConfigType {
        STRING, INTEGER, DECIMAL, BOOLEAN, JSON, EMAIL, URL, PHONE }

        Common configuration keys as constants
        public static final String AGENT_MATCH_THRESHOLD = "agent.match.threshold";

        public static final String PROFILE_APPROVAL_REQUIRED = "profile.approval.required";

        public static final String SMS_VERIFICATION_ENABLED = "sms.verification.enabled";

        public static final String MAX_PROFILE_PHOTOS = "profile.photos.max";

        public static final String DEFAULT_SUBSCRIPTION_DAYS = "subscription.default.days";

        public static final String PAYMENT_GATEWAY_ENABLED = "payment.gateway.enabled";

        public static final String HOROSCOPE_MATCHING_WEIGHT = "horoscope.matching.weight";

        public static final String AUTO_PROFILE_APPROVAL = "profile.auto.approval";

        public static final String CONTACT_REQUEST_LIMIT = "contact.request.limit";

        public static final String PROFILE_VIEW_LIMIT = "profile.view.limit";

        public SystemConfiguration() {
        }

        public SystemConfiguration(String configKey, String configValue, String description, ConfigType configType) {
            this.configKey = configKey;
            this.configValue = configValue;
            this.description = description;
            this.configType = configType;
        }

        public Long getId() {

            return this.id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getConfigkey() {

            return this.configKey;
        }

        public void setConfigkey(String configKey) {
            this.configKey = configKey;
        }

        public String getConfigvalue() {

            return this.configValue;
        }

        public void setConfigvalue(String configValue) {
            this.configValue = configValue;
        }

        public String getDescription() {

            return this.description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public ConfigType getConfigtype() {

            return this.configType;
        }

        public void setConfigtype(ConfigType configType) {
            this.configType = configType;
        }

        public String getCategory() {

            return this.category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getValidationrule() {

            return this.validationRule;
        }

        public void setValidationrule(String validationRule) {
            this.validationRule = validationRule;
        }

        public String getDefaultvalue() {

            return this.defaultValue;
        }

        public void setDefaultvalue(String defaultValue) {
            this.defaultValue = defaultValue;
        }

        public String getDisplayname() {

            return this.displayName;
        }

        public void setDisplayname(String displayName) {
            this.displayName = displayName;
        }

        public String getHelptext() {

            return this.helpText;
        }

        public void setHelptext(String helpText) {
            this.helpText = helpText;
        }

        public LocalDateTime getCreateddate() {

            return this.createdDate;
        }

        public void setCreateddate(LocalDateTime createdDate) {
            this.createdDate = createdDate;
        }

        public LocalDateTime getLastmodifieddate() {

            return this.lastModifiedDate;
        }

        public void setLastmodifieddate(LocalDateTime lastModifiedDate) {
            this.lastModifiedDate = lastModifiedDate;
        }
    }
