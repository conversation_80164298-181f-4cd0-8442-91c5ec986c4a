package lk.bandana.location.entity;

import org.springframework.data.annotation.Id;
import java.util.Objects;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.mongodb.core.mapping.DBRef;

@

public class District {

    @
    private Long id;

    private String name;

    private String nameInSinhala;

    private String nameInTamil;

    private String code;

    private Boolean active = true;

    @
    private lk.bandana.location.entity.Province province;

    public District() {
    }

    public District(String name, String nameInSinhala, String nameInTamil, String code, lk.bandana.location.entity.Province province) {
        this.name = name;
        this.nameInSinhala = nameInSinhala;
        this.nameInTamil = nameInTamil;
        this.code = code;
        this.province = province;
    }

    public Long getId() {

        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {

        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameinsinhala() {

        return this.nameInSinhala;
    }

    public void setNameinsinhala(String nameInSinhala) {
        this.nameInSinhala = nameInSinhala;
    }

    public String getNameintamil() {

        return this.nameInTamil;
    }

    public void setNameintamil(String nameInTamil) {
        this.nameInTamil = nameInTamil;
    }

    public String getCode() {

        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return """District{
            "id=" + id + ", " + "name=" + name + ", " + "nameInSinhala=" + nameInSinhala + ", " + "nameInTamil=" + nameInTamil + ", " + "code=" + code
        }""";
    }

    @Override
    public boolean equals(Object obj) {
        return switch (obj) {
            case null -> false;
            case District other when this == other -> true;
            case District other -> Objects.equals(id, other.id) && Objects.equals(name, other.name) && Objects.equals(nameInSinhala, other.nameInSinhala) && Objects.equals(nameInTamil, other.nameInTamil) && Objects.equals(code, other.code);
            default -> false;
        };
    }
    @Override
    public int hashCode() {
        return Objects.hash(id, name, nameInSinhala, nameInTamil, code);
    }
}