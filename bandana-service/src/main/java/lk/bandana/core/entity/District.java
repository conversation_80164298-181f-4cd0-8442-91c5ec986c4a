package lk.bandana.location.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

@Node
@Getter
@Setter
public class District {

    @Id
    private Long id;

    private String name;
    private String nameInSinhala;
    private String nameInTamil;
    private String code;
    private Boolean active = true;

    @Relationship("BELONGS_TO_PROVINCE")
    private lk.bandana.location.entity.Province province;

    public District() {}

    public District(String name, String nameInSinhala, String nameInTamil, String code, lk.bandana.location.entity.Province province) {
        this.name = name;
        this.nameInSinhala = nameInSinhala;
        this.nameInTamil = nameInTamil;
        this.code = code;
        this.province = province;
    }
}
