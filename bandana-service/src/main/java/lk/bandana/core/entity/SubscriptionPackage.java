package lk.bandana.subscription.entity;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.mongodb.core.mapping.Field;

import org.springframework.data.annotation.CreatedDate;

import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;

import java.time.LocalDateTime;

import java.util.List;

@(collection = "subscription_packages")

public class SubscriptionPackage {

    @
    private String id;

    @("package_name")
    private String packageName;

    @("description")
    private String description;

    @("price")
    private BigDecimal price;

    @("duration_days")
    private Integer durationDays;

    @("package_type")
    private PackageType packageType;

    @("target_user_type")
    private UserType targetUserType;

    Features and Limits
    private Integer maxProfileViews;

    private Integer maxContactRequests;

    private Integer maxPhotoUploads;

    private Boolean premiumMatchAccess;

    private Boolean prioritySupport;

    private Boolean profileHighlighting;

    private Boolean advancedSearch;

    private Boolean horoscopeMatching;

    Quality tier for matching (higher tier = access to better profiles)
    private Integer qualityTier = 1;

    Basic, 2=Premium, 3=Elite
    Agent-specific features
    private Integer maxClientProfiles;

    For agents only
    private Boolean crossAgentMatching;

    Can see other agents' profiles
    private Integer matchingThreshold = 80;

    Minimum compatibility score to view profiles
    Status and availability
    private Boolean active = true;

    private Boolean featured = false;

    private Integer sortOrder = 0;

    Billing configuration
    private BillingCycle billingCycle;

    private Boolean autoRenewal = true;

    private BigDecimal setupFee = BigDecimal.ZERO;

    Promotional pricing
    private BigDecimal discountPercentage = BigDecimal.ZERO;

    private LocalDateTime promotionStartDate;

    private LocalDateTime promotionEndDate;

    @
    private LocalDateTime createdDate;

    @
    private LocalDateTime lastModifiedDate;

    public enum PackageType {
        BASIC, PREMIUM, ELITE, AGENT_MONTHLY, AGENT_ANNUAL }

        public enum UserType {
            REGULAR_USER, AGENT, ADMIN }

            public enum BillingCycle {
                MONTHLY, QUARTERLY, SEMI_ANNUAL, ANNUAL }

                public String getId() {

                    return this.id;
                }

                public void setId(String id) {
                    this.id = id;
                }

                public String getPackagename() {

                    return this.packageName;
                }

                public void setPackagename(String packageName) {
                    this.packageName = packageName;
                }

                public String getDescription() {

                    return this.description;
                }

                public void setDescription(String description) {
                    this.description = description;
                }

                public BigDecimal getPrice() {

                    return this.price;
                }

                public void setPrice(BigDecimal price) {
                    this.price = price;
                }

                public Integer getDurationdays() {

                    return this.durationDays;
                }

                public void setDurationdays(Integer durationDays) {
                    this.durationDays = durationDays;
                }

                public PackageType getPackagetype() {

                    return this.packageType;
                }

                public void setPackagetype(PackageType packageType) {
                    this.packageType = packageType;
                }

                public UserType getTargetusertype() {

                    return this.targetUserType;
                }

                public void setTargetusertype(UserType targetUserType) {
                    this.targetUserType = targetUserType;
                }

                public Integer getMaxprofileviews() {

                    return this.maxProfileViews;
                }

                public void setMaxprofileviews(Integer maxProfileViews) {
                    this.maxProfileViews = maxProfileViews;
                }

                public Integer getMaxcontactrequests() {

                    return this.maxContactRequests;
                }

                public void setMaxcontactrequests(Integer maxContactRequests) {
                    this.maxContactRequests = maxContactRequests;
                }

                public Integer getMaxphotouploads() {

                    return this.maxPhotoUploads;
                }

                public void setMaxphotouploads(Integer maxPhotoUploads) {
                    this.maxPhotoUploads = maxPhotoUploads;
                }

                public Boolean isPremiummatchaccess() {

                    return this.premiumMatchAccess;
                }

                public void setPremiummatchaccess(Boolean premiumMatchAccess) {
                    this.premiumMatchAccess = premiumMatchAccess;
                }

                public Boolean isPrioritysupport() {

                    return this.prioritySupport;
                }

                public void setPrioritysupport(Boolean prioritySupport) {
                    this.prioritySupport = prioritySupport;
                }

                public Boolean isProfilehighlighting() {

                    return this.profileHighlighting;
                }

                public void setProfilehighlighting(Boolean profileHighlighting) {
                    this.profileHighlighting = profileHighlighting;
                }

                public Boolean isAdvancedsearch() {

                    return this.advancedSearch;
                }

                public void setAdvancedsearch(Boolean advancedSearch) {
                    this.advancedSearch = advancedSearch;
                }

                public Boolean isHoroscopematching() {

                    return this.horoscopeMatching;
                }

                public void setHoroscopematching(Boolean horoscopeMatching) {
                    this.horoscopeMatching = horoscopeMatching;
                }

                public Integer getMaxclientprofiles() {

                    return this.maxClientProfiles;
                }

                public void setMaxclientprofiles(Integer maxClientProfiles) {
                    this.maxClientProfiles = maxClientProfiles;
                }

                public Boolean isCrossagentmatching() {

                    return this.crossAgentMatching;
                }

                public void setCrossagentmatching(Boolean crossAgentMatching) {
                    this.crossAgentMatching = crossAgentMatching;
                }

                public BillingCycle getBillingcycle() {

                    return this.billingCycle;
                }

                public void setBillingcycle(BillingCycle billingCycle) {
                    this.billingCycle = billingCycle;
                }

                public LocalDateTime getPromotionstartdate() {

                    return this.promotionStartDate;
                }

                public void setPromotionstartdate(LocalDateTime promotionStartDate) {
                    this.promotionStartDate = promotionStartDate;
                }

                public LocalDateTime getPromotionenddate() {

                    return this.promotionEndDate;
                }

                public void setPromotionenddate(LocalDateTime promotionEndDate) {
                    this.promotionEndDate = promotionEndDate;
                }

                public LocalDateTime getCreateddate() {

                    return this.createdDate;
                }

                public void setCreateddate(LocalDateTime createdDate) {
                    this.createdDate = createdDate;
                }

                public LocalDateTime getLastmodifieddate() {

                    return this.lastModifiedDate;
                }

                public void setLastmodifieddate(LocalDateTime lastModifiedDate) {
                    this.lastModifiedDate = lastModifiedDate;
                }
            }
