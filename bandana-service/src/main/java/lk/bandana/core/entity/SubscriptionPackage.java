package lk.bandana.subscription.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Document(collection = "subscription_packages")
@Getter
@Setter
public class SubscriptionPackage {

    @Id
    private String id;

    @Field("package_name")
    private String packageName;

    @Field("description")
    private String description;

    @Field("price")
    private BigDecimal price;

    @Field("duration_days")
    private Integer durationDays;

    @Field("package_type")
    private PackageType packageType;

    @Field("target_user_type")
    private UserType targetUserType;

    // Features and Limits
    private Integer maxProfileViews;
    private Integer maxContactRequests;
    private Integer maxPhotoUploads;
    private Boolean premiumMatchAccess;
    private Boolean prioritySupport;
    private Boolean profileHighlighting;
    private Boolean advancedSearch;
    private Boolean horoscopeMatching;

    // Quality tier for matching (higher tier = access to better profiles)
    private Integer qualityTier = 1; // 1=Basic, 2=Premium, 3=Elite

    // Agent-specific features
    private Integer maxClientProfiles; // For agents only
    private Boolean crossAgentMatching; // Can see other agents' profiles
    private Integer matchingThreshold = 80; // Minimum compatibility score to view profiles

    // Status and availability
    private Boolean active = true;
    private Boolean featured = false;
    private Integer sortOrder = 0;

    // Billing configuration
    private BillingCycle billingCycle;
    private Boolean autoRenewal = true;
    private BigDecimal setupFee = BigDecimal.ZERO;

    // Promotional pricing
    private BigDecimal discountPercentage = BigDecimal.ZERO;
    private LocalDateTime promotionStartDate;
    private LocalDateTime promotionEndDate;

    @CreatedDate
    private LocalDateTime createdDate;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    public enum PackageType {
        BASIC,
        PREMIUM,
        ELITE,
        AGENT_MONTHLY,
        AGENT_ANNUAL
    }

    public enum UserType {
        REGULAR_USER,
        AGENT,
        ADMIN
    }

    public enum BillingCycle {
        MONTHLY,
        QUARTERLY,
        SEMI_ANNUAL,
        ANNUAL
    }
}
