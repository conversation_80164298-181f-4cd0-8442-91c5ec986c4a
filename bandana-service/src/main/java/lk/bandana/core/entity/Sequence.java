package lk.bandana.core.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.stereotype.Component;

@Node
@Component
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Sequence {

    @Id
    private Integer id;

    private String name;

    private Long counter;

    private String prefix;
}
