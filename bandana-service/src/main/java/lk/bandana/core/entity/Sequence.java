package lk.bandana.core.entity; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.stereotype.Component; @Document @Component public class Sequence { @Id private Integer id; private String name; private Long counter; private String prefix; public Integer getId() { return this.id; } public void setId(Integer id) { this.id = id; } public String getName() { return this.name; } public void setName(String name) { this.name = name; } public Long getCounter() { return this.counter; } public void setCounter(Long counter) { this.counter = counter; } public String getPrefix() { return this.prefix; } public void setPrefix(String prefix) { this.prefix = prefix; } }