package lk.bandana.profile.entity;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

@

public class Interest {

    @
    private Long id;

    private String name;

    private String category;

    SPORTS, MUSIC, ARTS, TRAVEL, etc.
    private String description;

    private Boolean active = true;

    private Integer sortOrder = 0;

    public Interest() {
    }

    public Interest(String name, String category) {
        this.name = name;
        this.category = category;
    }

    public Interest(String name, String category, String description) {
        this.name = name;
        this.category = category;
        this.description = description;
    }

    public Long getId() {

        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {

        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {

        return this.category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDescription() {

        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
