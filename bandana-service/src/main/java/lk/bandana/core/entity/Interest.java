package lk.bandana.profile.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;

@Node
@Getter
@Setter
public class Interest {

    @Id
    private Long id;

    private String name;
    private String category; // SPORTS, MUSIC, ARTS, TRAVEL, etc.
    private String description;
    private Boolean active = true;
    private Integer sortOrder = 0;

    public Interest() {}

    public Interest(String name, String category) {
        this.name = name;
        this.category = category;
    }

    public Interest(String name, String category, String description) {
        this.name = name;
        this.category = category;
        this.description = description;
    }
}
