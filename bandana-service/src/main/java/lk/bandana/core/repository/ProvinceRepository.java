package lk.bandana.core.repository;

import lk.bandana.core.entity.Province;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProvinceRepository extends Neo4jRepository<Province, Long> {

    List<Province> findByActiveTrueOrderByName();

    Optional<Province> findByCode(String code);

    Optional<Province> findByName(String name);

    List<Province> findByNameContainingIgnoreCase(String name);
}
