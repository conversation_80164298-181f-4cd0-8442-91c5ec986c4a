package lk.bandana.core.repository; import lk.bandana.core.entity.Province; import org.springframework.data.mongodb.repository.MongoRepository; import org.springframework.stereotype.Repository; import java.util.List; import java.util.Optional; @Repository public interface ProvinceRepository extends MongoRepository<Province, Long> { List<Province> findByActiveTrueOrderByName(); Optional<Province> findByCode(String code); Optional<Province> findByName(String name); List<Province> findByNameContainingIgnoreCase(String name); } 