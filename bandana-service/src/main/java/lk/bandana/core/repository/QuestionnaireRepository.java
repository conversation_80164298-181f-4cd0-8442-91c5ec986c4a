package lk.bandana.core.repository;

import lk.bandana.core.entity.Questionnaire;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QuestionnaireRepository extends Neo4jRepository<Questionnaire, Long> {

    List<Questionnaire> findByActiveTrueOrderBySortOrder();

    List<Questionnaire> findByTypeAndActiveTrueOrderBySortOrder(Questionnaire.QuestionnaireType type);

    List<Questionnaire> findByTargetGenderAndActiveTrueOrderBySortOrder(String targetGender);

    List<Questionnaire> findByTargetUserTypeAndActiveTrueOrderBySortOrder(String targetUserType);
}
