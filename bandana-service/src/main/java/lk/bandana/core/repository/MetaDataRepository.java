package lk.bandana.core.repository;


import lk.bandana.core.entity.MetaData;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 4/18/2018
 */
@Repository
public interface MetaDataRepository extends Neo4jRepository<MetaData, Integer> {

    MetaData findByValueAndCategory(String value, String category);

    MetaData findByValue(String value);

    List<MetaData> findByCategory(String category);

}
