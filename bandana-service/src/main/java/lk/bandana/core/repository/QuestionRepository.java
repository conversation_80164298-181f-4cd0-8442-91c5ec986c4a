package lk.bandana.core.repository; import lk.bandana.core.entity.Question; import org.springframework.data.mongodb.repository.MongoRepository; import org.springframework.data.neo4j.repository.query.Query; import org.springframework.data.repository.query.Param; import org.springframework.stereotype.Repository; import java.util.List; @Repository public interface QuestionRepository extends MongoRepository<Question, Long> { List<Question> findByActiveTrueOrderBySortOrder(); @Query("MATCH (q:Questionnaire)-[:HAS_QUESTION]->(question:Question) WHERE q.id = $questionnaireId AND question.active = true RETURN question ORDER BY question.sortOrder") List<Question> findByQuestionnaireId(@Param("questionnaireId") Long questionnaireId); List<Question> findByTypeAndActiveTrueOrderBySortOrder(Question.QuestionType type); List<Question> findByMatchingCategoryAndActiveTrueOrderBySortOrder(String matchingCategory); } 