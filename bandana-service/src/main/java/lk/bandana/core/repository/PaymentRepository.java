package lk.bandana.core.repository;

import lk.bandana.core.entity.Payment;

import org.springframework.data.mongodb.repository.MongoRepository;

import org.springframework.data.neo4j.repository.query.Query;

import org.springframework.data.repository.query.Param;

import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

import java.time.LocalDateTime;

import java.util.List;

import java.util.Optional;

@

public interface PaymentRepository extends MongoRepository<Payment, Long> {

    Find payments by user
    @("MATCH (p:Payment)-[:PAID_BY]->(u:User) WHERE u.id = $userId RETURN p ORDER BY p.createdDate DESC") List<Payment> findByUserId(
    @("userId") Long userId);

    Find payments by status List<Payment> findByStatus(Payment.PaymentStatus status);

    Find payments by transaction ID Optional<Payment> findByTransactionId(String transactionId);

    Find payments by gateway transaction ID Optional<Payment> findByGatewayTransactionId(String gatewayTransactionId);

    Find successful payments List<Payment> findByStatusOrderByPaymentDateDesc(Payment.PaymentStatus status);

    Find payments by date range
    @("MATCH (p:Payment) WHERE p.paymentDate >= $startDate AND p.paymentDate <= $endDate RETURN p") List<Payment> findByPaymentDateRange(
    @("startDate") LocalDateTime startDate,
    @("endDate") LocalDateTime endDate);

    Find failed payments for retry
    @("MATCH (p:Payment) WHERE p.status = 'FAILED' AND p.retryCount < 3 RETURN p") List<Payment> findFailedPaymentsForRetry();

    Calculate total revenue by date range
    @("MATCH (p:Payment) WHERE p.status = 'COMPLETED' AND p.paymentDate >= $startDate AND p.paymentDate <= $endDate " + "RETURN sum(p.amount)") BigDecimal calculateRevenueByDateRange(
    @("startDate") LocalDateTime startDate,
    @("endDate") LocalDateTime endDate);

    Find payments by payment method List<Payment> findByPaymentMethod(Payment.PaymentMethod paymentMethod);

    Find refunded payments List<Payment> findByRefundedTrue();

    Find payments by subscription
    @("MATCH (p:Payment)-[:PAYMENT_FOR]->(s:UserSubscription) WHERE s.id = $subscriptionId RETURN p") List<Payment> findBySubscriptionId(
    @("subscriptionId") Long subscriptionId);

    Count payments by status
    @("MATCH (p:Payment) WHERE p.status = $status RETURN count(p)") Long countByStatus(
    @("status") String status);

    Find pending payments older than specified time
    @("MATCH (p:Payment) WHERE p.status = 'PENDING' AND p.createdDate < $cutoffDate RETURN p") List<Payment> findPendingPaymentsOlderThan(
    @("cutoffDate") LocalDateTime cutoffDate);
}
