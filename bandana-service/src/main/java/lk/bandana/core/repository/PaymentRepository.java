package lk.bandana.core.repository;

import lk.bandana.core.entity.Payment;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PaymentRepository extends Neo4jRepository<Payment, Long> {

    // Find payments by user
    @Query("MATCH (p:Payment)-[:PAID_BY]->(u:User) WHERE u.id = $userId RETURN p ORDER BY p.createdDate DESC")
    List<Payment> findByUserId(@Param("userId") Long userId);

    // Find payments by status
    List<Payment> findByStatus(Payment.PaymentStatus status);

    // Find payments by transaction ID
    Optional<Payment> findByTransactionId(String transactionId);

    // Find payments by gateway transaction ID
    Optional<Payment> findByGatewayTransactionId(String gatewayTransactionId);

    // Find successful payments
    List<Payment> findByStatusOrderByPaymentDateDesc(Payment.PaymentStatus status);

    // Find payments by date range
    @Query("MATCH (p:Payment) WHERE p.paymentDate >= $startDate AND p.paymentDate <= $endDate RETURN p")
    List<Payment> findByPaymentDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    // Find failed payments for retry
    @Query("MATCH (p:Payment) WHERE p.status = 'FAILED' AND p.retryCount < 3 RETURN p")
    List<Payment> findFailedPaymentsForRetry();

    // Calculate total revenue by date range
    @Query("MATCH (p:Payment) WHERE p.status = 'COMPLETED' AND p.paymentDate >= $startDate AND p.paymentDate <= $endDate " +
           "RETURN sum(p.amount)")
    BigDecimal calculateRevenueByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    // Find payments by payment method
    List<Payment> findByPaymentMethod(Payment.PaymentMethod paymentMethod);

    // Find refunded payments
    List<Payment> findByRefundedTrue();

    // Find payments by subscription
    @Query("MATCH (p:Payment)-[:PAYMENT_FOR]->(s:UserSubscription) WHERE s.id = $subscriptionId RETURN p")
    List<Payment> findBySubscriptionId(@Param("subscriptionId") Long subscriptionId);

    // Count payments by status
    @Query("MATCH (p:Payment) WHERE p.status = $status RETURN count(p)")
    Long countByStatus(@Param("status") String status);

    // Find pending payments older than specified time
    @Query("MATCH (p:Payment) WHERE p.status = 'PENDING' AND p.createdDate < $cutoffDate RETURN p")
    List<Payment> findPendingPaymentsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);
}
