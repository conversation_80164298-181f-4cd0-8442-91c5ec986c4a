package lk.bandana.core.repository;

import lk.bandana.core.entity.UserQuestionnaireResponse;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserQuestionnaireResponseRepository extends Neo4jRepository<UserQuestionnaireResponse, Long> {

    @Query("MATCH (uqr:UserQuestionnaireResponse)-[:RESPONSE_BY]->(up:UserProfile) WHERE up.id = $profileId RETURN uqr")
    List<UserQuestionnaireResponse> findByUserProfileId(@Param("profileId") Long profileId);

    @Query("MATCH (uqr:UserQuestionnaireResponse)-[:RESPONSE_TO]->(q:Questionnaire) WHERE q.id = $questionnaireId RETURN uqr")
    List<UserQuestionnaireResponse> findByQuestionnaireId(@Param("questionnaireId") Long questionnaireId);

    @Query("MATCH (uqr:UserQuestionnaireResponse)-[:RESPONSE_BY]->(up:UserProfile), " +
           "(uqr)-[:RESPONSE_TO]->(q:Questionnaire) " +
           "WHERE up.id = $profileId AND q.id = $questionnaireId RETURN uqr")
    Optional<UserQuestionnaireResponse> findByUserProfileAndQuestionnaire(@Param("profileId") Long profileId, @Param("questionnaireId") Long questionnaireId);

    @Query("MATCH (uqr:UserQuestionnaireResponse) WHERE uqr.completed = true RETURN uqr")
    List<UserQuestionnaireResponse> findCompletedResponses();

    @Query("MATCH (uqr:UserQuestionnaireResponse) WHERE uqr.completed = false RETURN uqr")
    List<UserQuestionnaireResponse> findIncompleteResponses();
}
