package lk.bandana.core.repository; import lk.bandana.core.entity.UserSubscription; import org.springframework.data.mongodb.repository.MongoRepository; import org.springframework.data.neo4j.repository.query.Query; import org.springframework.data.repository.query.Param; import org.springframework.stereotype.Repository; import java.time.LocalDateTime; import java.util.List; import java.util.Optional; @Repository public interface UserSubscriptionRepository extends MongoRepository<UserSubscription, Long> { // Find current active subscription for user @Query("MATCH (us:UserSubscription)-[:BELONGS_TO]->(u:User) " + "WHERE u.id = $userId AND us.status = 'ACTIVE' AND us.endDate > $now " + "RETURN us ORDER BY us.endDate DESC LIMIT 1") Optional<UserSubscription> findCurrentActiveSubscription(@Param("userId") Long userId, @Param("now") LocalDateTime now); // Find all subscriptions for user @Query("MATCH (us:UserSubscription)-[:BELONGS_TO]->(u:User) WHERE u.id = $userId RETURN us ORDER BY us.createdDate DESC") List<UserSubscription> findByUserId(@Param("userId") Long userId); // Find subscriptions by status List<UserSubscription> findByStatus(UserSubscription.SubscriptionStatus status); // Find expiring subscriptions @Query("MATCH (us:UserSubscription) WHERE us.status = 'ACTIVE' AND us.endDate <= $expiryDate RETURN us") List<UserSubscription> findExpiringSubscriptions(@Param("expiryDate") LocalDateTime expiryDate); // Find subscriptions due for renewal @Query("MATCH (us:UserSubscription) WHERE us.status = 'ACTIVE' AND us.autoRenewal = true AND us.nextBillingDate <= $date RETURN us") List<UserSubscription> findSubscriptionsDueForRenewal(@Param("date") LocalDateTime date); // Find trial subscriptions @Query("MATCH (us:UserSubscription) WHERE us.isTrial = true RETURN us") List<UserSubscription> findTrialSubscriptions(); // Find expired subscriptions @Query("MATCH (us:UserSubscription) WHERE us.status = 'ACTIVE' AND us.endDate < $now RETURN us") List<UserSubscription> findExpiredSubscriptions(@Param("now") LocalDateTime now); // Count active subscriptions by package @Query("MATCH (us:UserSubscription)-[:SUBSCRIBES_TO]->(sp:SubscriptionPackage) " + "WHERE us.status = 'ACTIVE' AND sp.id = $packageId RETURN count(us)") Long countActiveSubscriptionsByPackage(@Param("packageId") Long packageId); // Find subscriptions by date range @Query("MATCH (us:UserSubscription) WHERE us.createdDate >= $startDate AND us.createdDate <= $endDate RETURN us") List<UserSubscription> findByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate); // Find subscriptions with specific payment method List<UserSubscription> findByPaymentMethod(String paymentMethod); // Check if user has any active subscription @Query("MATCH (us:UserSubscription)-[:BELONGS_TO]->(u:User) " + "WHERE u.id = $userId AND us.status = 'ACTIVE' AND us.endDate > $now " + "RETURN count(us) > 0") Boolean hasActiveSubscription(@Param("userId") Long userId, @Param("now") LocalDateTime now); } 