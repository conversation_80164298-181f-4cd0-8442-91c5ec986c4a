package lk.bandana.core.repository;

import lk.bandana.core.entity.CompatibilityMatch;

import org.springframework.data.domain.Page;

import org.springframework.data.domain.Pageable;

import org.springframework.data.mongodb.repository.MongoRepository;

import org.springframework.data.neo4j.repository.query.Query;

import org.springframework.data.repository.query.Param;

import org.springframework.stereotype.Repository;

import java.util.List;

import java.util.Optional;

@

public interface CompatibilityMatchRepository extends MongoRepository<CompatibilityMatch, Long> {

    Find matches for a specific profile
    @("MATCH (cm:CompatibilityMatch) WHERE " + "(cm)-[:PROFILE_A]->(p:UserProfile {
        id: $profileId}
        ) OR " + "(cm)-[:PROFILE_B]->(p:UserProfile {
            id: $profileId}
            ) " + "RETURN cm ORDER BY cm.overallScore DESC") List<CompatibilityMatch> findMatchesForProfile(
            @("profileId") Long profileId);

            Find high-quality matches (score >= threshold)
            @("MATCH (cm:CompatibilityMatch) WHERE " + "((cm)-[:PROFILE_A]->(p:UserProfile {
                id: $profileId}
                ) OR " + "(cm)-[:PROFILE_B]->(p:UserProfile {
                    id: $profileId}
                    )) " + "AND cm.overallScore >= $minScore AND cm.visible = true " + "RETURN cm ORDER BY cm.overallScore DESC") List<CompatibilityMatch> findHighQualityMatchesForProfile(
                    @("profileId") Long profileId,
                    @("minScore") Integer minScore);

                    Find matches visible to agents
                    @("MATCH (cm:CompatibilityMatch) WHERE " + "cm.visibleToAgents = true AND cm.overallScore >= $minScore " + "RETURN cm ORDER BY cm.overallScore DESC") List<CompatibilityMatch> findMatchesVisibleToAgents(
                    @("minScore") Integer minScore);

                    Find matches between two specific profiles
                    @("MATCH (cm:CompatibilityMatch) WHERE " + "((cm)-[:PROFILE_A]->(p1:UserProfile {
                        id: $profileId1}
                        ) AND (cm)-[:PROFILE_B]->(p2:UserProfile {
                            id: $profileId2}
                            )) OR " + "((cm)-[:PROFILE_A]->(p2:UserProfile {
                                id: $profileId2}
                                ) AND (cm)-[:PROFILE_B]->(p1:UserProfile {
                                    id: $profileId1}
                                    )) " + "RETURN cm") Optional<CompatibilityMatch> findMatchBetweenProfiles(
                                    @("profileId1") Long profileId1,
                                    @("profileId2") Long profileId2);

                                    Find matches that need recalculation
                                    @("MATCH (cm:CompatibilityMatch) WHERE cm.needsRecalculation = true RETURN cm") List<CompatibilityMatch> findMatchesNeedingRecalculation();

                                    Find matches by status List<CompatibilityMatch> findByStatus(CompatibilityMatch.MatchStatus status);

                                    Find matches with contact requests
                                    @("MATCH (cm:CompatibilityMatch) WHERE cm.contactRequestSent = true RETURN cm") List<CompatibilityMatch> findMatchesWithContactRequests();

                                    Find matches for agent's clients
                                    @("MATCH (cm:CompatibilityMatch)-[:PROFILE_A|PROFILE_B]->(p:UserProfile)-[:MANAGED_BY]->(agent:User {
                                        id: $agentId}
                                        ) " + "WHERE cm.overallScore >= $minScore AND cm.visible = true " + "RETURN cm ORDER BY cm.overallScore DESC") List<CompatibilityMatch> findMatchesForAgentClients(
                                        @("agentId") Long agentId,
                                        @("minScore") Integer minScore);

                                        Find cross-agent matches (matches between profiles managed by different agents)
                                        @("MATCH (cm:CompatibilityMatch)-[:PROFILE_A]->(p1:UserProfile)-[:MANAGED_BY]->(agent1:User), " + "(cm)-[:PROFILE_B]->(p2:UserProfile)-[:MANAGED_BY]->(agent2:User) " + "WHERE agent1.id <> agent2.id AND cm.overallScore >= $minScore AND cm.visibleToAgents = true " + "RETURN cm ORDER BY cm.overallScore DESC") List<CompatibilityMatch> findCrossAgentMatches(
                                        @("minScore") Integer minScore);

                                        Count matches for profile
                                        @("MATCH (cm:CompatibilityMatch) WHERE " + "(cm)-[:PROFILE_A]->(p:UserProfile {
                                            id: $profileId}
                                            ) OR " + "(cm)-[:PROFILE_B]->(p:UserProfile {
                                                id: $profileId}
                                                ) " + "RETURN count(cm)") Long countMatchesForProfile(
                                                @("profileId") Long profileId);

                                                Find matches by score range
                                                @("MATCH (cm:CompatibilityMatch) WHERE " + "cm.overallScore >= $minScore AND cm.overallScore <= $maxScore " + "RETURN cm ORDER BY cm.overallScore DESC") List<CompatibilityMatch> findMatchesByScoreRange(
                                                @("minScore") Integer minScore,
                                                @("maxScore") Integer maxScore);

                                                Find recent matches
                                                @("MATCH (cm:CompatibilityMatch) WHERE cm.calculatedDate >= $since RETURN cm ORDER BY cm.calculatedDate DESC") List<CompatibilityMatch> findRecentMatches(
                                                @("since") String since);

                                                Find matches for premium users (based on subscription tier)
                                                @("MATCH (cm:CompatibilityMatch)-[:PROFILE_A|PROFILE_B]->(p:UserProfile)<-[:HAS_PROFILE]-(u:User)-[:HAS_SUBSCRIPTION]->(s:UserSubscription)-[:SUBSCRIBES_TO]->(sp:SubscriptionPackage) " + "WHERE sp.qualityTier >= $minTier AND cm.visible = true " + "RETURN cm ORDER BY cm.overallScore DESC") Page<CompatibilityMatch> findMatchesForPremiumUsers(
                                                @("minTier") Integer minTier, Pageable pageable);
                                            }
