package lk.bandana.core.repository;

import lk.bandana.core.entity.CompatibilityMatch;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CompatibilityMatchRepository extends Neo4jRepository<CompatibilityMatch, Long> {

    // Find matches for a specific profile
    @Query("MATCH (cm:CompatibilityMatch) WHERE " +
           "(cm)-[:PROFILE_A]->(p:UserProfile {id: $profileId}) OR " +
           "(cm)-[:PROFILE_B]->(p:UserProfile {id: $profileId}) " +
           "RETURN cm ORDER BY cm.overallScore DESC")
    List<CompatibilityMatch> findMatchesForProfile(@Param("profileId") Long profileId);

    // Find high-quality matches (score >= threshold)
    @Query("MATCH (cm:CompatibilityMatch) WHERE " +
           "((cm)-[:PROFILE_A]->(p:UserProfile {id: $profileId}) OR " +
           "(cm)-[:PROFILE_B]->(p:UserProfile {id: $profileId})) " +
           "AND cm.overallScore >= $minScore AND cm.visible = true " +
           "RETURN cm ORDER BY cm.overallScore DESC")
    List<CompatibilityMatch> findHighQualityMatchesForProfile(@Param("profileId") Long profileId, @Param("minScore") Integer minScore);

    // Find matches visible to agents
    @Query("MATCH (cm:CompatibilityMatch) WHERE " +
           "cm.visibleToAgents = true AND cm.overallScore >= $minScore " +
           "RETURN cm ORDER BY cm.overallScore DESC")
    List<CompatibilityMatch> findMatchesVisibleToAgents(@Param("minScore") Integer minScore);

    // Find matches between two specific profiles
    @Query("MATCH (cm:CompatibilityMatch) WHERE " +
           "((cm)-[:PROFILE_A]->(p1:UserProfile {id: $profileId1}) AND (cm)-[:PROFILE_B]->(p2:UserProfile {id: $profileId2})) OR " +
           "((cm)-[:PROFILE_A]->(p2:UserProfile {id: $profileId2}) AND (cm)-[:PROFILE_B]->(p1:UserProfile {id: $profileId1})) " +
           "RETURN cm")
    Optional<CompatibilityMatch> findMatchBetweenProfiles(@Param("profileId1") Long profileId1, @Param("profileId2") Long profileId2);

    // Find matches that need recalculation
    @Query("MATCH (cm:CompatibilityMatch) WHERE cm.needsRecalculation = true RETURN cm")
    List<CompatibilityMatch> findMatchesNeedingRecalculation();

    // Find matches by status
    List<CompatibilityMatch> findByStatus(CompatibilityMatch.MatchStatus status);

    // Find matches with contact requests
    @Query("MATCH (cm:CompatibilityMatch) WHERE cm.contactRequestSent = true RETURN cm")
    List<CompatibilityMatch> findMatchesWithContactRequests();

    // Find matches for agent's clients
    @Query("MATCH (cm:CompatibilityMatch)-[:PROFILE_A|PROFILE_B]->(p:UserProfile)-[:MANAGED_BY]->(agent:User {id: $agentId}) " +
           "WHERE cm.overallScore >= $minScore AND cm.visible = true " +
           "RETURN cm ORDER BY cm.overallScore DESC")
    List<CompatibilityMatch> findMatchesForAgentClients(@Param("agentId") Long agentId, @Param("minScore") Integer minScore);

    // Find cross-agent matches (matches between profiles managed by different agents)
    @Query("MATCH (cm:CompatibilityMatch)-[:PROFILE_A]->(p1:UserProfile)-[:MANAGED_BY]->(agent1:User), " +
           "(cm)-[:PROFILE_B]->(p2:UserProfile)-[:MANAGED_BY]->(agent2:User) " +
           "WHERE agent1.id <> agent2.id AND cm.overallScore >= $minScore AND cm.visibleToAgents = true " +
           "RETURN cm ORDER BY cm.overallScore DESC")
    List<CompatibilityMatch> findCrossAgentMatches(@Param("minScore") Integer minScore);

    // Count matches for profile
    @Query("MATCH (cm:CompatibilityMatch) WHERE " +
           "(cm)-[:PROFILE_A]->(p:UserProfile {id: $profileId}) OR " +
           "(cm)-[:PROFILE_B]->(p:UserProfile {id: $profileId}) " +
           "RETURN count(cm)")
    Long countMatchesForProfile(@Param("profileId") Long profileId);

    // Find matches by score range
    @Query("MATCH (cm:CompatibilityMatch) WHERE " +
           "cm.overallScore >= $minScore AND cm.overallScore <= $maxScore " +
           "RETURN cm ORDER BY cm.overallScore DESC")
    List<CompatibilityMatch> findMatchesByScoreRange(@Param("minScore") Integer minScore, @Param("maxScore") Integer maxScore);

    // Find recent matches
    @Query("MATCH (cm:CompatibilityMatch) WHERE cm.calculatedDate >= $since RETURN cm ORDER BY cm.calculatedDate DESC")
    List<CompatibilityMatch> findRecentMatches(@Param("since") String since);

    // Find matches for premium users (based on subscription tier)
    @Query("MATCH (cm:CompatibilityMatch)-[:PROFILE_A|PROFILE_B]->(p:UserProfile)<-[:HAS_PROFILE]-(u:User)-[:HAS_SUBSCRIPTION]->(s:UserSubscription)-[:SUBSCRIBES_TO]->(sp:SubscriptionPackage) " +
           "WHERE sp.qualityTier >= $minTier AND cm.visible = true " +
           "RETURN cm ORDER BY cm.overallScore DESC")
    Page<CompatibilityMatch> findMatchesForPremiumUsers(@Param("minTier") Integer minTier, Pageable pageable);
}
