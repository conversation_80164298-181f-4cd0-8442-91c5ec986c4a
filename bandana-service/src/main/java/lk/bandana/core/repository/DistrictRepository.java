package lk.bandana.core.repository;

import lk.bandana.core.entity.District;

import org.springframework.data.mongodb.repository.MongoRepository;

import org.springframework.data.neo4j.repository.query.Query;

import org.springframework.data.repository.query.Param;

import org.springframework.stereotype.Repository;

import java.util.List;

import java.util.Optional;

@

public interface DistrictRepository extends MongoRepository<District, Long> {
    List<District> findByActiveTrueOrderByName();
    Optional<District> findByCode(String code);
    Optional<District> findByName(String name);
    List<District> findByNameContainingIgnoreCase(String name);

    @("MATCH (d:District)-[:BELONGS_TO_PROVINCE]->(p:Province) WHERE p.id = $provinceId AND d.active = true RETURN d ORDER BY d.name") List<District> findByProvinceId(
    @("provinceId") Long provinceId);

    @("MATCH (d:District)-[:BELONGS_TO_PROVINCE]->(p:Province) WHERE p.code = $provinceCode AND d.active = true RETURN d ORDER BY d.name") List<District> findByProvinceCode(
    @("provinceCode") String provinceCode);
}
