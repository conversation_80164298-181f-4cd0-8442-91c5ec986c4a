package lk.bandana.profile.repository; import lk.bandana.profile.entity.UserProfile; import org.springframework.data.domain.Page; import org.springframework.data.domain.Pageable; import org.springframework.data.mongodb.repository.MongoRepository; import org.springframework.data.mongodb.repository.Query; import org.springframework.data.repository.query.Param; import org.springframework.stereotype.Repository; import java.util.List; import java.util.Optional; @Repository public interface UserProfileRepository extends MongoRepository<UserProfile, String> { // Find by status List<UserProfile> findByStatus(UserProfile.ProfileStatus status); Page<UserProfile> findByStatus(UserProfile.ProfileStatus status, Pageable pageable); // Find by gender List<UserProfile> findByGender(String gender); Page<UserProfile> findByGender(String gender, Pageable pageable); // Find by age range @Query("MATCH (p:UserProfile) WHERE p.age >= $minAge AND p.age <= $maxAge RETURN p") List<UserProfile> findByAgeRange(@Param("minAge") Integer minAge, @Param("maxAge") Integer maxAge); // Find by location @Query("MATCH (p:UserProfile)-[:LIVES_IN_PROVINCE]->(pr:Province) WHERE pr.name = $provinceName RETURN p") List<UserProfile> findByProvince(@Param("provinceName") String provinceName); @Query("MATCH (p:UserProfile)-[:LIVES_IN_DISTRICT]->(d:District) WHERE d.name = $districtName RETURN p") List<UserProfile> findByDistrict(@Param("districtName") String districtName); // Find approved profiles for matching @Query("MATCH (p:UserProfile) WHERE p.status = 'APPROVED' AND p.profileVisible = true RETURN p") List<UserProfile> findApprovedAndVisibleProfiles(); @Query("MATCH (p:UserProfile) WHERE p.status = 'APPROVED' AND p.profileVisible = true AND p.gender = $gender RETURN p") List<UserProfile> findApprovedAndVisibleProfilesByGender(@Param("gender") String gender); // Find profiles managed by agent @Query("MATCH (p:UserProfile)-[:MANAGED_BY]->(u:User) WHERE u.id = $agentId RETURN p") List<UserProfile> findProfilesManagedByAgent(@Param("agentId") Long agentId); // Find high-quality profiles (for premium subscriptions) @Query("MATCH (p:UserProfile) WHERE p.status = 'APPROVED' AND p.profileQualityScore >= $minScore RETURN p") List<UserProfile> findHighQualityProfiles(@Param("minScore") Integer minScore); // Search profiles with filters @Query("MATCH (p:UserProfile) WHERE p.status = 'APPROVED' AND p.profileVisible = true " + "AND ($gender IS NULL OR p.gender = $gender) " + "AND ($minAge IS NULL OR p.age >= $minAge) " + "AND ($maxAge IS NULL OR p.age <= $maxAge) " + "AND ($minHeight IS NULL OR p.heightCm >= $minHeight) " + "AND ($maxHeight IS NULL OR p.heightCm <= $maxHeight) " + "RETURN p") Page<UserProfile> searchProfiles(@Param("gender") String gender, @Param("minAge") Integer minAge, @Param("maxAge") Integer maxAge, @Param("minHeight") Integer minHeight, @Param("maxHeight") Integer maxHeight, Pageable pageable); // Find profiles pending approval @Query("MATCH (p:UserProfile) WHERE p.status = 'PENDING' ORDER BY p.createdDate ASC RETURN p") List<UserProfile> findProfilesPendingApproval(); // Count profiles by status @Query("MATCH (p:UserProfile) WHERE p.status = $status RETURN count(p)") Long countByStatus(@Param("status") String status); // Find incomplete profiles @Query("MATCH (p:UserProfile) WHERE p.completionPercentage < 100 RETURN p") List<UserProfile> findIncompleteProfiles(); // Find profiles by education level List<UserProfile> findByEducationLevel(String educationLevel); // Find profiles by occupation List<UserProfile> findByOccupationContainingIgnoreCase(String occupation); // Find profiles by religion List<UserProfile> findByReligion(String religion); // Find profiles with photos @Query("MATCH (p:UserProfile) WHERE p.profilePhotoUrl IS NOT NULL RETURN p") List<UserProfile> findProfilesWithPhotos(); // Find recently created profiles @Query("MATCH (p:UserProfile) WHERE p.createdDate >= $since RETURN p ORDER BY p.createdDate DESC") List<UserProfile> findRecentProfiles(@Param("since") String since); } 