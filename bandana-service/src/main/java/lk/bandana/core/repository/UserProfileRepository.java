package lk.bandana.profile.repository;

import lk.bandana.profile.entity.UserProfile;

import org.springframework.data.domain.Page;

import org.springframework.data.domain.Pageable;

import org.springframework.data.mongodb.repository.MongoRepository;

import org.springframework.data.mongodb.repository.Query;

import org.springframework.data.repository.query.Param;

import org.springframework.stereotype.Repository;

import java.util.List;

import java.util.Optional;

@

public interface UserProfileRepository extends MongoRepository<UserProfile, String> {

    Find by status List<UserProfile> findByStatus(UserProfile.ProfileStatus status);
    Page<UserProfile> findByStatus(UserProfile.ProfileStatus status, Pageable pageable);

    Find by gender List<UserProfile> findByGender(String gender);
    Page<UserProfile> findByGender(String gender, Pageable pageable);

    Find by age range
    @("MATCH (p:UserProfile) WHERE p.age >= $minAge AND p.age <= $maxAge RETURN p") List<UserProfile> findByAgeRange(
    @("minAge") Integer minAge,
    @("maxAge") Integer maxAge);

    Find by location
    @("MATCH (p:UserProfile)-[:LIVES_IN_PROVINCE]->(pr:Province) WHERE pr.name = $provinceName RETURN p") List<UserProfile> findByProvince(
    @("provinceName") String provinceName);

    @("MATCH (p:UserProfile)-[:LIVES_IN_DISTRICT]->(d:District) WHERE d.name = $districtName RETURN p") List<UserProfile> findByDistrict(
    @("districtName") String districtName);

    Find approved profiles for matching
    @("MATCH (p:UserProfile) WHERE p.status = 'APPROVED' AND p.profileVisible = true RETURN p") List<UserProfile> findApprovedAndVisibleProfiles();

    @("MATCH (p:UserProfile) WHERE p.status = 'APPROVED' AND p.profileVisible = true AND p.gender = $gender RETURN p") List<UserProfile> findApprovedAndVisibleProfilesByGender(
    @("gender") String gender);

    Find profiles managed by agent
    @("MATCH (p:UserProfile)-[:MANAGED_BY]->(u:User) WHERE u.id = $agentId RETURN p") List<UserProfile> findProfilesManagedByAgent(
    @("agentId") Long agentId);

    Find high-quality profiles (for premium subscriptions)
    @("MATCH (p:UserProfile) WHERE p.status = 'APPROVED' AND p.profileQualityScore >= $minScore RETURN p") List<UserProfile> findHighQualityProfiles(
    @("minScore") Integer minScore);

    Search profiles with filters
    @("MATCH (p:UserProfile) WHERE p.status = 'APPROVED' AND p.profileVisible = true " + "AND ($gender IS NULL OR p.gender = $gender) " + "AND ($minAge IS NULL OR p.age >= $minAge) " + "AND ($maxAge IS NULL OR p.age <= $maxAge) " + "AND ($minHeight IS NULL OR p.heightCm >= $minHeight) " + "AND ($maxHeight IS NULL OR p.heightCm <= $maxHeight) " + "RETURN p") Page<UserProfile> searchProfiles(
    @("gender") String gender,
    @("minAge") Integer minAge,
    @("maxAge") Integer maxAge,
    @("minHeight") Integer minHeight,
    @("maxHeight") Integer maxHeight, Pageable pageable);

    Find profiles pending approval
    @("MATCH (p:UserProfile) WHERE p.status = 'PENDING' ORDER BY p.createdDate ASC RETURN p") List<UserProfile> findProfilesPendingApproval();

    Count profiles by status
    @("MATCH (p:UserProfile) WHERE p.status = $status RETURN count(p)") Long countByStatus(
    @("status") String status);

    Find incomplete profiles
    @("MATCH (p:UserProfile) WHERE p.completionPercentage < 100 RETURN p") List<UserProfile> findIncompleteProfiles();

    Find profiles by education level List<UserProfile> findByEducationLevel(String educationLevel);

    Find profiles by occupation List<UserProfile> findByOccupationContainingIgnoreCase(String occupation);

    Find profiles by religion List<UserProfile> findByReligion(String religion);

    Find profiles with photos
    @("MATCH (p:UserProfile) WHERE p.profilePhotoUrl IS NOT NULL RETURN p") List<UserProfile> findProfilesWithPhotos();

    Find recently created profiles
    @("MATCH (p:UserProfile) WHERE p.createdDate >= $since RETURN p ORDER BY p.createdDate DESC") List<UserProfile> findRecentProfiles(
    @("since") String since);
}
