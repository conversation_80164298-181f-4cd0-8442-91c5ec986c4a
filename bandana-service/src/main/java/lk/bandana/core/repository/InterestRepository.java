package lk.bandana.core.repository;

import lk.bandana.core.entity.Interest;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InterestRepository extends Neo4jRepository<Interest, Long> {

    List<Interest> findByActiveTrueOrderBySortOrderAscNameAsc();

    List<Interest> findByCategoryAndActiveTrueOrderBySortOrderAscNameAsc(String category);

    List<Interest> findByNameContainingIgnoreCaseAndActiveTrue(String name);

    List<Interest> findDistinctCategoryByActiveTrue();
}
