package lk.bandana.core.repository; import lk.bandana.core.entity.Interest; import org.springframework.data.mongodb.repository.MongoRepository; import org.springframework.stereotype.Repository; import java.util.List; @Repository public interface InterestRepository extends MongoRepository<Interest, Long> { List<Interest> findByActiveTrueOrderBySortOrderAscNameAsc(); List<Interest> findByCategoryAndActiveTrueOrderBySortOrderAscNameAsc(String category); List<Interest> findByNameContainingIgnoreCaseAndActiveTrue(String name); List<Interest> findDistinctCategoryByActiveTrue(); } 