package lk.bandana.core.repository;

import lk.bandana.core.entity.SubscriptionPackage;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * SubscriptionPackageRepository - MongoDB Repository Interface
 */
@Repository
public interface SubscriptionPackageRepository extends MongoRepository<SubscriptionPackage, Long> {

    List<SubscriptionPackage> findByActiveTrue();

    List<SubscriptionPackage> findByActiveTrueOrderByPriceAsc();

    List<SubscriptionPackage> findByActiveTrueOrderByPriceDesc();

    @Query("{ 'active' : true, 'userType' : ?0 }")
    List<SubscriptionPackage> findActivePackagesByUserType(String userType);

    @Query("{ 'active' : true, 'price' : { $gte : ?0, $lte : ?1 } }")
    List<SubscriptionPackage> findActivePackagesByPriceRange(Double minPrice, Double maxPrice);

    @Query("{ 'active' : true, 'features' : { $in : ?0 } }")
    List<SubscriptionPackage> findActivePackagesByFeatures(List<String> features);

    @Query("{ 'active' : true, 'durationDays' : ?0 }")
    List<SubscriptionPackage> findActivePackagesByDuration(Integer durationDays);

    @Query("{ 'active' : true, 'isPopular' : true }")
    List<SubscriptionPackage> findPopularPackages();
}
