package lk.bandana.core.repository; import lk.bandana.core.entity.SubscriptionPackage; import org.springframework.data.mongodb.repository.MongoRepository; import org.springframework.data.neo4j.repository.query.Query; import org.springframework.data.repository.query.Param; import org.springframework.stereotype.Repository; import java.util.List; @Repository public interface SubscriptionPackageRepository extends MongoRepository<SubscriptionPackage, Long> { // Find active packages List<SubscriptionPackage> findByActiveTrue(); // Find packages by type List<SubscriptionPackage> findByPackageType(SubscriptionPackage.PackageType packageType); // Find packages by target user type List<SubscriptionPackage> findByTargetUserType(SubscriptionPackage.UserType targetUserType); // Find active packages for specific user type @Query("MATCH (sp:SubscriptionPackage) WHERE sp.active = true AND sp.targetUserType = $userType ORDER BY sp.sortOrder, sp.price") List<SubscriptionPackage> findActivePackagesForUserType(@Param("userType") String userType); // Find featured packages List<SubscriptionPackage> findByFeaturedTrueAndActiveTrueOrderBySortOrder(); // Find packages by billing cycle List<SubscriptionPackage> findByBillingCycleAndActiveTrue(SubscriptionPackage.BillingCycle billingCycle); // Find packages by quality tier @Query("MATCH (sp:SubscriptionPackage) WHERE sp.active = true AND sp.qualityTier >= $minTier ORDER BY sp.qualityTier DESC") List<SubscriptionPackage> findByMinimumQualityTier(@Param("minTier") Integer minTier); // Find agent packages @Query("MATCH (sp:SubscriptionPackage) WHERE sp.active = true AND sp.targetUserType = 'AGENT' ORDER BY sp.price") List<SubscriptionPackage> findAgentPackages(); // Find regular user packages @Query("MATCH (sp:SubscriptionPackage) WHERE sp.active = true AND sp.targetUserType = 'REGULAR_USER' ORDER BY sp.price") List<SubscriptionPackage> findRegularUserPackages(); } 