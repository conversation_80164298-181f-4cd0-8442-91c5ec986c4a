package lk.bandana.core.repository; import lk.bandana.core.entity.SystemConfiguration; import org.springframework.data.mongodb.repository.MongoRepository; import org.springframework.stereotype.Repository; import java.util.List; import java.util.Optional; @Repository public interface SystemConfigurationRepository extends MongoRepository<SystemConfiguration, Long> { Optional<SystemConfiguration> findByConfigKey(String configKey); List<SystemConfiguration> findByCategoryOrderBySortOrder(String category); List<SystemConfiguration> findByVisibleTrueOrderByCategoryAscSortOrderAsc(); List<SystemConfiguration> findByConfigTypeOrderByCategory(SystemConfiguration.ConfigType configType); } 