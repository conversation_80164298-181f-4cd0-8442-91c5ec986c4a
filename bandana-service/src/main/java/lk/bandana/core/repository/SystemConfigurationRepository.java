package lk.bandana.core.repository;

import lk.bandana.core.entity.SystemConfiguration;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SystemConfigurationRepository extends Neo4jRepository<SystemConfiguration, Long> {

    Optional<SystemConfiguration> findByConfigKey(String configKey);

    List<SystemConfiguration> findByCategoryOrderBySortOrder(String category);

    List<SystemConfiguration> findByVisibleTrueOrderByCategoryAscSortOrderAsc();

    List<SystemConfiguration> findByConfigTypeOrderByCategory(SystemConfiguration.ConfigType configType);
}
