package lk.bandana.core.controller;

import lk.bandana.business.kendara.entity.Person;

import lk.bandana.core.service.PersonService;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.data.domain.PageRequest;

import org.springframework.http.HttpStatus;

import org.springframework.http.MediaType;

import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.*;

@
@("/person")

public class PersonController {

    @PersonService personService;

    @(value = "/find_by_name", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByPerson(
    @("name") String name ,
    @("personTypeId") Integer personTypeId) {

        try {

            return ResponseEntity.ok(personService.searchPersonNameLike(name,personTypeId));
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.CONFLICT).build();
    }
}

@(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
@
private ResponseEntity<?> save(
@Person person) {

    try {

        return ResponseEntity.ok(personService.save(person));
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.CONFLICT).build();
}
}

@(value = "/find_all", method = RequestMethod.GET)
private ResponseEntity<?> findAll(
@("page") String page,
@("pageSize") String pageSize,
@("personTypeId") String personType) {

    try {

        return ResponseEntity.ok(personService.findAllByType(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize)), personType));
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.CONFLICT).build();
}
}
}
