package lk.bandana.core.controller;

import lk.bandana.core.entity.MetaData;

import lk.bandana.core.service.MetaDataService;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.http.HttpStatus;

import org.springframework.http.MediaType;

import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.*;

@
@("/metaData")

public class MetaDataController {

    @MetaDataService metaDataService;

    @(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @
    private ResponseEntity<?> save(
    @MetaData metaData) {

        try {

            return ResponseEntity.ok(metaDataService.save(metaData));
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.CONFLICT).build();
    }
}

@(value = "/findByCategory", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
private ResponseEntity<?> findByCategory(
@("any") String any) {

    try {

        return ResponseEntity.ok(metaDataService.findByCategory(any));
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.CONFLICT).build();
}
}

@(value = "/findMetaDataByCatVal", method = RequestMethod.GET)
private ResponseEntity<?> findAll(
@("value") String value,
@("category")String cate) {

    try {

        return ResponseEntity.ok(metaDataService.searchMetaData(value,cate));
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.CONFLICT).build();
}
}

@(value = "/findMetaDataByCatValBool", method = RequestMethod.GET)
private ResponseEntity<?> findMetaDataByCatValBool(
@("value") String value,
@("category")String cate) {

    try {

        return ResponseEntity.ok(metaDataService.searchMetaData(value,cate));
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.CONFLICT).build();
}
}

@(value = "/findById", method = RequestMethod.GET)
private ResponseEntity<?> findById(
@("id") Integer id) {

    try {

        return ResponseEntity.ok(metaDataService.findById(id));
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.CONFLICT).build();
}
}
}
