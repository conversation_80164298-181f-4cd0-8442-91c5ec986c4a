package lk.bandana.core.controller;

import lk.bandana.core.entity.User;

import lk.bandana.core.service.UserService;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.http.HttpStatus;

import org.springframework.http.MediaType;

import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.*;

import java.util.List;

@
@("/user")

public class UserController {

    @
    private UserService userService;

    @(value = "/findAll", method = RequestMethod.GET)
    public List<User> listUser() {

        return userService.findAll();
    }

    @(value = "/find/{
        id}
        ", method = RequestMethod.GET)
        public User getOne(
        @(value = "id") Long id) {

            return userService.findById(id).get();
        }

        @(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
        @
        private ResponseEntity<?> save(
        @User user) {

            try {

                return ResponseEntity.ok(userService.save(user));
            }

        } catch (Exception e) {

            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @(value = "/delete", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> delete(
    @Long id) {

        try {

            return ResponseEntity.ok(userService.delete(id));
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.CONFLICT).build();
    }
}
}
