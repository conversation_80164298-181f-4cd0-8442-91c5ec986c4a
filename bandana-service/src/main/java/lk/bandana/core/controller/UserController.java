package lk.bandana.core.controller; import lk.bandana.core.entity.User; import lk.bandana.core.service.UserService; import org.springframework.beans.factory.annotation.Autowired; import org.springframework.http.HttpStatus; import org.springframework.http.MediaType; import org.springframework.http.ResponseEntity; import org.springframework.web.bind.annotation.*; import java.util.List; @RestController @RequestMapping("/user") public class UserController { @Autowired private UserService userService; @RequestMapping(value = "/findAll", method = RequestMethod.GET) public List<User> listUser() { return userService.findAll(); } @RequestMapping(value = "/find/{id}", method = RequestMethod.GET) public User getOne(@PathVariable(value = "id") Long id) { return userService.findById(id).get(); } @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE) @ResponseBody private ResponseEntity<?> save(@RequestBody User user) { try { return ResponseEntity.ok(userService.save(user)); } catch (Exception e) { return ResponseEntity.status(HttpStatus.CONFLICT).build(); } } @RequestMapping(value = "/delete", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE) private ResponseEntity<?> delete(@RequestParam Long id) { try { return ResponseEntity.ok(userService.delete(id)); } catch (Exception e) { return ResponseEntity.status(HttpStatus.CONFLICT).build(); } } } 