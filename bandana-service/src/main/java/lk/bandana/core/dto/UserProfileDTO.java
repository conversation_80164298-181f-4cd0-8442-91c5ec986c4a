package lk.bandana.core.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * User Profile DTO record for API communication
 * Modern Java 21 record implementation
 */
public record UserProfileDTO(
    String id,
    String firstName,
    String lastName,
    String email,
    String phone,
    LocalDate dateOfBirth,
    Integer age,
    String gender,
    Integer height,
    String occupation,
    String education,
    String religion,
    String caste,
    String province,
    String district,
    List<String> interests,
    List<String> photoUrls,
    String profileStatus,
    Integer completionPercentage,
    Integer qualityScore,
    LocalDateTime createdAt,
    LocalDateTime updatedAt
) {
    
    // Compact constructor for validation
    public UserProfileDTO {
        if (firstName != null) {
            firstName = firstName.trim();
        }
        if (lastName != null) {
            lastName = lastName.trim();
        }
        if (email != null) {
            email = email.toLowerCase().trim();
        }
    }
    
    // Convenience methods
    public String getFullName() {
        return STR."\{firstName} \{lastName}";
    }
    
    public boolean isComplete() {
        return completionPercentage != null && completionPercentage >= 80;
    }
    
    public boolean isHighQuality() {
        return qualityScore != null && qualityScore >= 80;
    }
}
