package lk.bandana.management.controller;

import lk.bandana.core.entity.UserProfile;
import lk.bandana.core.service.UserProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controller for profile approval workflow management
 * Accessible only by administrators via bandana-management-ui
 */
@RestController
@RequestMapping("/api/management/profile-approval")
@PreAuthorize("hasRole('ADMIN')")
public class ProfileApprovalController {

    @Autowired
    private UserProfileService userProfileService;

    /**
     * Get all profiles pending approval
     */
    @GetMapping("/pending")
    public ResponseEntity<List<UserProfile>> getPendingProfiles() {
        try {
            List<UserProfile> pendingProfiles = userProfileService.findProfilesPendingApproval();
            return ResponseEntity.ok(pendingProfiles);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get profiles by status with pagination
     */
    @GetMapping("/by-status/{status}")
    public ResponseEntity<Page<UserProfile>> getProfilesByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            UserProfile.ProfileStatus profileStatus = UserProfile.ProfileStatus.valueOf(status.toUpperCase());
            Pageable pageable = PageRequest.of(page, size);
            Page<UserProfile> profiles = userProfileService.findByStatus(profileStatus, pageable);
            return ResponseEntity.ok(profiles);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get detailed profile information for review
     */
    @GetMapping("/profile/{profileId}")
    public ResponseEntity<UserProfile> getProfileForReview(@PathVariable Long profileId) {
        try {
            return userProfileService.findById(profileId)
                    .map(profile -> ResponseEntity.ok(profile))
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Approve a profile
     */
    @PostMapping("/approve/{profileId}")
    public ResponseEntity<UserProfile> approveProfile(
            @PathVariable Long profileId,
            @RequestBody Map<String, String> request) {
        try {
            String approvedBy = request.get("approvedBy");
            String comments = request.get("comments");
            
            if (approvedBy == null || approvedBy.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            UserProfile approvedProfile = userProfileService.approveProfile(profileId, approvedBy, comments);
            return ResponseEntity.ok(approvedProfile);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Reject a profile
     */
    @PostMapping("/reject/{profileId}")
    public ResponseEntity<UserProfile> rejectProfile(
            @PathVariable Long profileId,
            @RequestBody Map<String, String> request) {
        try {
            String rejectedBy = request.get("rejectedBy");
            String reason = request.get("reason");
            
            if (rejectedBy == null || rejectedBy.trim().isEmpty() || 
                reason == null || reason.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            UserProfile rejectedProfile = userProfileService.rejectProfile(profileId, rejectedBy, reason);
            return ResponseEntity.ok(rejectedProfile);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Suspend a profile
     */
    @PostMapping("/suspend/{profileId}")
    public ResponseEntity<UserProfile> suspendProfile(
            @PathVariable Long profileId,
            @RequestBody Map<String, String> request) {
        try {
            String suspendedBy = request.get("suspendedBy");
            String reason = request.get("reason");
            
            if (suspendedBy == null || suspendedBy.trim().isEmpty() || 
                reason == null || reason.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            UserProfile suspendedProfile = userProfileService.suspendProfile(profileId, suspendedBy, reason);
            return ResponseEntity.ok(suspendedProfile);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get profile approval statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getApprovalStatistics() {
        try {
            Map<String, Object> stats = Map.of(
                "pending", userProfileService.countProfilesByStatus(UserProfile.ProfileStatus.PENDING),
                "approved", userProfileService.countProfilesByStatus(UserProfile.ProfileStatus.APPROVED),
                "rejected", userProfileService.countProfilesByStatus(UserProfile.ProfileStatus.REJECTED),
                "suspended", userProfileService.countProfilesByStatus(UserProfile.ProfileStatus.SUSPENDED),
                "incomplete", userProfileService.countProfilesByStatus(UserProfile.ProfileStatus.INCOMPLETE)
            );
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Bulk approve profiles
     */
    @PostMapping("/bulk-approve")
    public ResponseEntity<Map<String, Object>> bulkApproveProfiles(
            @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> profileIds = (List<Long>) request.get("profileIds");
            String approvedBy = (String) request.get("approvedBy");
            String comments = (String) request.get("comments");
            
            if (profileIds == null || profileIds.isEmpty() || 
                approvedBy == null || approvedBy.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            int successCount = 0;
            int failureCount = 0;
            
            for (Long profileId : profileIds) {
                try {
                    userProfileService.approveProfile(profileId, approvedBy, comments);
                    successCount++;
                } catch (Exception e) {
                    failureCount++;
                }
            }
            
            Map<String, Object> result = Map.of(
                "success", successCount,
                "failures", failureCount,
                "total", profileIds.size()
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get recent profile activities
     */
    @GetMapping("/recent-activities")
    public ResponseEntity<List<UserProfile>> getRecentActivities(
            @RequestParam(defaultValue = "7") int days) {
        try {
            List<UserProfile> recentProfiles = userProfileService.findRecentProfiles(days);
            return ResponseEntity.ok(recentProfiles);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Search profiles for admin review
     */
    @GetMapping("/search")
    public ResponseEntity<Page<UserProfile>> searchProfiles(
            @RequestParam(required = false) String gender,
            @RequestParam(required = false) Integer minAge,
            @RequestParam(required = false) Integer maxAge,
            @RequestParam(required = false) String province,
            @RequestParam(required = false) String district,
            @RequestParam(required = false) String education,
            @RequestParam(required = false) String occupation,
            @RequestParam(required = false) String religion,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<UserProfile> profiles = userProfileService.searchProfiles(
                gender, minAge, maxAge, null, null, province, district, 
                education, occupation, religion, pageable);
            return ResponseEntity.ok(profiles);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
