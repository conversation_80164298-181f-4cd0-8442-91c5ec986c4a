package lk.bandana.management.controller;

import lk.bandana.core.entity.UserProfile;

import lk.bandana.core.service.UserProfileService;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.data.domain.Page;

import org.springframework.data.domain.PageRequest;

import org.springframework.data.domain.Pageable;

import org.springframework.http.HttpStatus;

import org.springframework.http.ResponseEntity;

import org.springframework.security.access.prepost.PreAuthorize;

import org.springframework.web.bind.annotation.*;

import java.util.List;

import java.util.Map;

/** * Controller for profile approval workflow management * Accessible only by administrators via bandana-management-ui */
@
@("/api/management/profile-approval")
@("hasRole('ADMIN')")

public class ProfileApprovalController {

    @
    private UserProfileService userProfileService;

    /** * Get all profiles pending approval */
    @("/pending")
    public ResponseEntity<List<UserProfile>> getPendingProfiles() {

        try {
            List<UserProfile> pendingProfiles = userProfileService.findProfilesPendingApproval();

            return ResponseEntity.ok(pendingProfiles);
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}

/** * Get profiles by status with pagination */
@("/by-status/{
    status}
    ")
    public ResponseEntity<Page<UserProfile>> getProfilesByStatus(
    @String status,
    @(defaultValue = "0") int page,
    @(defaultValue = "10") int size) {

        try {
            UserProfile.ProfileStatus profileStatus = UserProfile.ProfileStatus.valueOf(status.toUpperCase());
            Pageable pageable = PageRequest.of(page, size);
            Page<UserProfile> profiles = userProfileService.findByStatus(profileStatus, pageable);

            return ResponseEntity.ok(profiles);
        }

    } catch (IllegalArgumentException e) {

        return ResponseEntity.badRequest().build();
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Get detailed profile information for review */
@("/profile/{
    profileId}
    ")
    public ResponseEntity<UserProfile> getProfileForReview(
    @Long profileId) {

        try {

            return userProfileService.findById(profileId) .map(profile -> ResponseEntity.ok(profile)) .orElse(ResponseEntity.notFound().build());
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}

/** * Approve a profile */
@("/approve/{
    profileId}
    ")
    public ResponseEntity<UserProfile> approveProfile(
    @Long profileId,
    @Map<String, String> request) {

        try {
            String approvedBy = request.get("approvedBy");
            String comments = request.get("comments");

            if (approvedBy == null || approvedBy.trim().isEmpty()) {

                return ResponseEntity.badRequest().build();
            }
            UserProfile approvedProfile = userProfileService.approveProfile(profileId, approvedBy, comments);

            return ResponseEntity.ok(approvedProfile);
        }

    } catch (RuntimeException e) {

        return ResponseEntity.notFound().build();
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Reject a profile */
@("/reject/{
    profileId}
    ")
    public ResponseEntity<UserProfile> rejectProfile(
    @Long profileId,
    @Map<String, String> request) {

        try {
            String rejectedBy = request.get("rejectedBy");
            String reason = request.get("reason");

            if (rejectedBy == null || rejectedBy.trim().isEmpty() || reason == null || reason.trim().isEmpty()) {

                return ResponseEntity.badRequest().build();
            }
            UserProfile rejectedProfile = userProfileService.rejectProfile(profileId, rejectedBy, reason);

            return ResponseEntity.ok(rejectedProfile);
        }

    } catch (RuntimeException e) {

        return ResponseEntity.notFound().build();
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Suspend a profile */
@("/suspend/{
    profileId}
    ")
    public ResponseEntity<UserProfile> suspendProfile(
    @Long profileId,
    @Map<String, String> request) {

        try {
            String suspendedBy = request.get("suspendedBy");
            String reason = request.get("reason");

            if (suspendedBy == null || suspendedBy.trim().isEmpty() || reason == null || reason.trim().isEmpty()) {

                return ResponseEntity.badRequest().build();
            }
            UserProfile suspendedProfile = userProfileService.suspendProfile(profileId, suspendedBy, reason);

            return ResponseEntity.ok(suspendedProfile);
        }

    } catch (RuntimeException e) {

        return ResponseEntity.notFound().build();
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Get profile approval statistics */
@("/statistics")
public ResponseEntity<Map<String, Object>> getApprovalStatistics() {

    try {
        Map<String, Object> stats = Map.of( "pending", userProfileService.countProfilesByStatus(UserProfile.ProfileStatus.PENDING), "approved", userProfileService.countProfilesByStatus(UserProfile.ProfileStatus.APPROVED), "rejected", userProfileService.countProfilesByStatus(UserProfile.ProfileStatus.REJECTED), "suspended", userProfileService.countProfilesByStatus(UserProfile.ProfileStatus.SUSPENDED), "incomplete", userProfileService.countProfilesByStatus(UserProfile.ProfileStatus.INCOMPLETE) );

        return ResponseEntity.ok(stats);
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Bulk approve profiles */
@("/bulk-approve")
public ResponseEntity<Map<String, Object>> bulkApproveProfiles(
@Map<String, Object> request) {

    try {

        @("unchecked") List<Long> profileIds = (List<Long>) request.get("profileIds");
        String approvedBy = (String) request.get("approvedBy");
        String comments = (String) request.get("comments");

        if (profileIds == null || profileIds.isEmpty() || approvedBy == null || approvedBy.trim().isEmpty()) {

            return ResponseEntity.badRequest().build();
        }
        int successCount = 0;
        int failureCount = 0;

        for (Long profileId : profileIds) {

            try {
                userProfileService.approveProfile(profileId, approvedBy, comments);
                successCount++;
            }

        } catch (Exception e) {
            failureCount++;
        }
    }
    Map<String, Object> result = Map.of( "success", successCount, "failures", failureCount, "total", profileIds.size() );

    return ResponseEntity.ok(result);
}

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Get recent profile activities */
@("/recent-activities")
public ResponseEntity<List<UserProfile>> getRecentActivities(
@(defaultValue = "7") int days) {

    try {
        List<UserProfile> recentProfiles = userProfileService.findRecentProfiles(days);

        return ResponseEntity.ok(recentProfiles);
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Search profiles for admin review */
@("/search")
public ResponseEntity<Page<UserProfile>> searchProfiles(
@(required = false) String gender,
@(required = false) Integer minAge,
@(required = false) Integer maxAge,
@(required = false) String province,
@(required = false) String district,
@(required = false) String education,
@(required = false) String occupation,
@(required = false) String religion,
@(defaultValue = "0") int page,
@(defaultValue = "10") int size) {

    try {
        Pageable pageable = PageRequest.of(page, size);
        Page<UserProfile> profiles = userProfileService.searchProfiles( gender, minAge, maxAge, null, null, province, district, education, occupation, religion, pageable);

        return ResponseEntity.ok(profiles);
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}
}
