package lk.bandana.management.controller; import lk.bandana.core.entity.SystemConfiguration; import lk.bandana.core.service.SystemConfigurationService; import org.springframework.beans.factory.annotation.Autowired; import org.springframework.http.HttpStatus; import org.springframework.http.ResponseEntity; import org.springframework.security.access.prepost.PreAuthorize; import org.springframework.web.bind.annotation.*; import java.util.List; import java.util.Map; /** * Controller for system configuration management * Accessible only by administrators via bandana-management-ui */ @RestController @RequestMapping("/api/management/system-config") @PreAuthorize("hasRole('ADMIN')") public class SystemConfigurationController { @Autowired private SystemConfigurationService systemConfigurationService; /** * Get all visible configurations */ @GetMapping public ResponseEntity<List<SystemConfiguration>> getAllConfigurations() { try { List<SystemConfiguration> configurations = systemConfigurationService.findVisibleConfigurations(); return ResponseEntity.ok(configurations); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get configurations by category */ @GetMapping("/category/{category}") public ResponseEntity<List<SystemConfiguration>> getConfigurationsByCategory(@PathVariable String category) { try { List<SystemConfiguration> configurations = systemConfigurationService.findByCategory(category); return ResponseEntity.ok(configurations); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get specific configuration by key */ @GetMapping("/key/{configKey}") public ResponseEntity<SystemConfiguration> getConfigurationByKey(@PathVariable String configKey) { try { return systemConfigurationService.findByKey(configKey) .map(config -> ResponseEntity.ok(config)) .orElse(ResponseEntity.notFound().build()); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Update configuration value */ @PutMapping("/key/{configKey}") public ResponseEntity<SystemConfiguration> updateConfiguration( @PathVariable String configKey, @RequestBody Map<String, String> request) { try { String value = request.get("value"); if (value == null) { return ResponseEntity.badRequest().build(); } // Validate the configuration value if (!systemConfigurationService.validateConfigValue(configKey, value)) { return ResponseEntity.badRequest().build(); } SystemConfiguration updated = systemConfigurationService.updateStringValue(configKey, value); return ResponseEntity.ok(updated); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Create new configuration */ @PostMapping public ResponseEntity<SystemConfiguration> createConfiguration(@RequestBody SystemConfiguration configuration) { try { SystemConfiguration saved = systemConfigurationService.save(configuration); return ResponseEntity.status(HttpStatus.CREATED).body(saved); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get agent match threshold */ @GetMapping("/agent-match-threshold") public ResponseEntity<Map<String, Integer>> getAgentMatchThreshold() { try { Integer threshold = systemConfigurationService.getAgentMatchThreshold(); return ResponseEntity.ok(Map.of("threshold", threshold)); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Update agent match threshold */ @PutMapping("/agent-match-threshold") public ResponseEntity<Map<String, Integer>> updateAgentMatchThreshold(@RequestBody Map<String, Integer> request) { try { Integer threshold = request.get("threshold"); if (threshold == null || threshold < 0 || threshold > 100) { return ResponseEntity.badRequest().build(); } systemConfigurationService.setAgentMatchThreshold(threshold); return ResponseEntity.ok(Map.of("threshold", threshold)); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get profile approval settings */ @GetMapping("/profile-approval") public ResponseEntity<Map<String, Object>> getProfileApprovalSettings() { try { Boolean approvalRequired = systemConfigurationService.isProfileApprovalRequired(); Boolean autoApproval = systemConfigurationService.isAutoProfileApprovalEnabled(); Map<String, Object> settings = Map.of( "approvalRequired", approvalRequired, "autoApproval", autoApproval ); return ResponseEntity.ok(settings); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Update profile approval settings */ @PutMapping("/profile-approval") public ResponseEntity<Map<String, Object>> updateProfileApprovalSettings(@RequestBody Map<String, Boolean> request) { try { Boolean approvalRequired = request.get("approvalRequired"); Boolean autoApproval = request.get("autoApproval"); if (approvalRequired != null) { systemConfigurationService.setProfileApprovalRequired(approvalRequired); } if (autoApproval != null) { systemConfigurationService.setAutoProfileApprovalEnabled(autoApproval); } Map<String, Object> settings = Map.of( "approvalRequired", systemConfigurationService.isProfileApprovalRequired(), "autoApproval", systemConfigurationService.isAutoProfileApprovalEnabled() ); return ResponseEntity.ok(settings); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get SMS verification settings */ @GetMapping("/sms-verification") public ResponseEntity<Map<String, Boolean>> getSmsVerificationSettings() { try { Boolean enabled = systemConfigurationService.isSmsVerificationEnabled(); return ResponseEntity.ok(Map.of("enabled", enabled)); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Update SMS verification settings */ @PutMapping("/sms-verification") public ResponseEntity<Map<String, Boolean>> updateSmsVerificationSettings(@RequestBody Map<String, Boolean> request) { try { Boolean enabled = request.get("enabled"); if (enabled != null) { systemConfigurationService.setSmsVerificationEnabled(enabled); } return ResponseEntity.ok(Map.of("enabled", systemConfigurationService.isSmsVerificationEnabled())); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get payment gateway settings */ @GetMapping("/payment-gateway") public ResponseEntity<Map<String, Boolean>> getPaymentGatewaySettings() { try { Boolean enabled = systemConfigurationService.isPaymentGatewayEnabled(); return ResponseEntity.ok(Map.of("enabled", enabled)); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Update payment gateway settings */ @PutMapping("/payment-gateway") public ResponseEntity<Map<String, Boolean>> updatePaymentGatewaySettings(@RequestBody Map<String, Boolean> request) { try { Boolean enabled = request.get("enabled"); if (enabled != null) { systemConfigurationService.setPaymentGatewayEnabled(enabled); } return ResponseEntity.ok(Map.of("enabled", systemConfigurationService.isPaymentGatewayEnabled())); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get all configuration categories */ @GetMapping("/categories") public ResponseEntity<List<String>> getConfigurationCategories() { try { List<String> categories = systemConfigurationService.getAllCategories(); return ResponseEntity.ok(categories); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Initialize default configurations */ @PostMapping("/initialize-defaults") public ResponseEntity<Map<String, String>> initializeDefaultConfigurations() { try { systemConfigurationService.initializeDefaultConfigurations(); return ResponseEntity.ok(Map.of("message", "Default configurations initialized successfully")); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Reset all configurations to defaults */ @PostMapping("/reset-defaults") public ResponseEntity<Map<String, String>> resetToDefaults() { try { systemConfigurationService.resetToDefaults(); return ResponseEntity.ok(Map.of("message", "All configurations reset to defaults")); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Export configurations */ @GetMapping("/export") public ResponseEntity<Map<String, String>> exportConfigurations() { try { String configData = systemConfigurationService.exportConfigurations(); return ResponseEntity.ok(Map.of("configData", configData)); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Import configurations */ @PostMapping("/import") public ResponseEntity<Map<String, String>> importConfigurations(@RequestBody Map<String, String> request) { try { String configData = request.get("configData"); if (configData == null || configData.trim().isEmpty()) { return ResponseEntity.badRequest().build(); } systemConfigurationService.importConfigurations(configData); return ResponseEntity.ok(Map.of("message", "Configurations imported successfully")); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } } 