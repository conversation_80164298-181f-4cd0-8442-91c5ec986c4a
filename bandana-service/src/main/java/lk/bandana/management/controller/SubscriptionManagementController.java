package lk.bandana.management.controller;

import lk.bandana.core.entity.SubscriptionPackage;

import lk.bandana.core.service.SubscriptionService;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.http.HttpStatus;

import org.springframework.http.ResponseEntity;

import org.springframework.security.access.prepost.PreAuthorize;

import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

import java.util.List;

import java.util.Map;

/** * Controller for subscription
package management * Accessible only by administrators via bandana-management-ui */
@
@("/api/management/subscriptions")
@("hasRole('ADMIN')")

public class SubscriptionManagementController {

    @
    private SubscriptionService subscriptionService;

    /** * Get all subscription packages */
    @("/packages")
    public ResponseEntity<List<SubscriptionPackage>> getAllPackages() {

        try {
            List<SubscriptionPackage> packages = subscriptionService.findActivePackages();

            return ResponseEntity.ok(packages);
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}

/** * Get packages by user type */
@("/packages/user-type/{
    userType}
    ")
    public ResponseEntity<List<SubscriptionPackage>> getPackagesByUserType(
    @String userType) {

        try {
            SubscriptionPackage.UserType type = SubscriptionPackage.UserType.valueOf(userType.toUpperCase());
            List<SubscriptionPackage> packages = subscriptionService.findPackagesForUserType(type);

            return ResponseEntity.ok(packages);
        }

    } catch (IllegalArgumentException e) {

        return ResponseEntity.badRequest().build();
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Create new subscription
package */
@("/packages")
public ResponseEntity<SubscriptionPackage> createPackage(
@SubscriptionPackage subscriptionPackage) {

    try {
        SubscriptionPackage savedPackage = subscriptionService.savePackage(subscriptionPackage);

        return ResponseEntity.status(HttpStatus.CREATED).body(savedPackage);
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Update subscription
package */
@("/packages/{
    packageId}
    ")
    public ResponseEntity<SubscriptionPackage> updatePackage(
    @Long packageId,
    @SubscriptionPackage subscriptionPackage) {

        try {
            subscriptionPackage.setId(packageId);
            SubscriptionPackage updatedPackage = subscriptionService.savePackage(subscriptionPackage);

            return ResponseEntity.ok(updatedPackage);
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}

/** * Update
package features */
@("/packages/{
    packageId}
    /features")
    public ResponseEntity<SubscriptionPackage> updatePackageFeatures(
    @Long packageId,
    @Map<String, Object> features) {

        try {
            Integer maxProfileViews = (Integer) features.get("maxProfileViews");
            Integer maxContactRequests = (Integer) features.get("maxContactRequests");
            Boolean premiumAccess = (Boolean) features.get("premiumAccess");
            SubscriptionPackage updatedPackage = subscriptionService.updatePackageFeatures( packageId, maxProfileViews, maxContactRequests, premiumAccess);

            return ResponseEntity.ok(updatedPackage);
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}

/** * Update
package pricing */
@("/packages/{
    packageId}
    /pricing")
    public ResponseEntity<SubscriptionPackage> updatePackagePricing(
    @Long packageId,
    @Map<String, Object> pricing) {

        try {
            BigDecimal price = new BigDecimal(pricing.get("price").toString());
            BigDecimal discountPercentage = pricing.get("discountPercentage") != null ? new BigDecimal(pricing.get("discountPercentage").toString()) : BigDecimal.ZERO;
            SubscriptionPackage updatedPackage = subscriptionService.updatePackagePricing( packageId, price, discountPercentage);

            return ResponseEntity.ok(updatedPackage);
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}

/** * Get subscription analytics */
@("/analytics")
public ResponseEntity<Map<String, Object>> getSubscriptionAnalytics() {

    try {
        BigDecimal monthlyRevenue = subscriptionService.calculateMonthlyRevenue();
        Map<String, Object> analytics = Map.of( "monthlyRevenue", monthlyRevenue, "activeSubscriptions", "TODO: Implement count", "newSubscriptionsThisMonth", "TODO: Implement count", "churnRate", "TODO: Implement calculation" );

        return ResponseEntity.ok(analytics);
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Get
package usage statistics */
@("/packages/{
    packageId}
    /usage")
    public ResponseEntity<Map<String, Object>> getPackageUsage(
    @Long packageId) {

        try {
            Long activeSubscriptions = subscriptionService.countActiveSubscriptionsByPackage(packageId);
            Map<String, Object> usage = Map.of( "activeSubscriptions", activeSubscriptions, "totalRevenue", "TODO: Implement calculation", "averageSubscriptionDuration", "TODO: Implement calculation" );

            return ResponseEntity.ok(usage);
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}

/** * Get agent packages */
@("/packages/agent")
public ResponseEntity<List<SubscriptionPackage>> getAgentPackages() {

    try {
        List<SubscriptionPackage> packages = subscriptionService.findAgentPackages();

        return ResponseEntity.ok(packages);
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Get regular user packages */
@("/packages/regular")
public ResponseEntity<List<SubscriptionPackage>> getRegularUserPackages() {

    try {
        List<SubscriptionPackage> packages = subscriptionService.findRegularUserPackages();

        return ResponseEntity.ok(packages);
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Get featured packages */
@("/packages/featured")
public ResponseEntity<List<SubscriptionPackage>> getFeaturedPackages() {

    try {
        List<SubscriptionPackage> packages = subscriptionService.findFeaturedPackages();

        return ResponseEntity.ok(packages);
    }

} catch (Exception e) {

    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
}

/** * Toggle
package active status */
@("/packages/{
    packageId}
    /toggle-active")
    public ResponseEntity<SubscriptionPackage> togglePackageActive(
    @Long packageId) {

        try {

            return subscriptionService.findPackageById(packageId) .map(pkg -> {
                pkg.setActive(!pkg.getActive());
                SubscriptionPackage updated = subscriptionService.savePackage(pkg);

                return ResponseEntity.ok(updated);
            }
            ) .orElse(ResponseEntity.notFound().build());
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}

/** * Set
package as featured */
@("/packages/{
    packageId}
    /featured")
    public ResponseEntity<SubscriptionPackage> setPackageFeatured(
    @Long packageId,
    @Map<String, Boolean> request) {

        try {
            Boolean featured = request.get("featured");

            return subscriptionService.findPackageById(packageId) .map(pkg -> {
                pkg.setFeatured(featured);
                SubscriptionPackage updated = subscriptionService.savePackage(pkg);

                return ResponseEntity.ok(updated);
            }
            ) .orElse(ResponseEntity.notFound().build());
        }

    } catch (Exception e) {

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}
}
