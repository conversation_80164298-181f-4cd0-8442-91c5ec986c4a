package lk.bandana.management.controller;

import lk.bandana.core.entity.Response;
import lk.bandana.core.entity.SubscriptionPackage;
import lk.bandana.core.service.SubscriptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * SubscriptionManagementController - REST Controller for subscription management
 */
@RestController
@RequestMapping("/api/management/subscriptions")
public class SubscriptionManagementController {

    @Autowired
    private SubscriptionService subscriptionService;

    @GetMapping("/packages")
    public ResponseEntity<Response<List<SubscriptionPackage>>> getAllPackages() {
        try {
            List<SubscriptionPackage> packages = subscriptionService.findAllPackages();
            return ResponseEntity.ok(Response.success("Packages retrieved successfully", packages));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(Response.error("Failed to retrieve packages: " + e.getMessage(), "RETRIEVAL_ERROR"));
        }
    }

    @GetMapping("/packages/{id}")
    public ResponseEntity<Response<SubscriptionPackage>> getPackageById(@PathVariable Long id) {
        try {
            var packageOpt = subscriptionService.findPackageById(id);
            if (packageOpt.isPresent()) {
                return ResponseEntity.ok(Response.success("Package found", packageOpt.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(Response.error("Failed to retrieve package: " + e.getMessage(), "RETRIEVAL_ERROR"));
        }
    }

    @PostMapping("/packages")
    public ResponseEntity<Response<SubscriptionPackage>> createPackage(@RequestBody SubscriptionPackage subscriptionPackage) {
        try {
            SubscriptionPackage savedPackage = subscriptionService.savePackage(subscriptionPackage);
            return ResponseEntity.ok(Response.success("Package created successfully", savedPackage));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(Response.error("Failed to create package: " + e.getMessage(), "CREATION_ERROR"));
        }
    }

    @PutMapping("/packages/{id}")
    public ResponseEntity<Response<SubscriptionPackage>> updatePackage(@PathVariable Long id, 
                                                                      @RequestBody SubscriptionPackage subscriptionPackage) {
        try {
            subscriptionPackage.setId(id);
            SubscriptionPackage updatedPackage = subscriptionService.savePackage(subscriptionPackage);
            return ResponseEntity.ok(Response.success("Package updated successfully", updatedPackage));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(Response.error("Failed to update package: " + e.getMessage(), "UPDATE_ERROR"));
        }
    }

    @DeleteMapping("/packages/{id}")
    public ResponseEntity<Response<Void>> deletePackage(@PathVariable Long id) {
        try {
            subscriptionService.deletePackage(id);
            return ResponseEntity.ok(Response.success("Package deleted successfully", null));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(Response.error("Failed to delete package: " + e.getMessage(), "DELETION_ERROR"));
        }
    }

    @GetMapping("/active-subscriptions")
    public ResponseEntity<Response<List<Object>>> getActiveSubscriptions() {
        try {
            // TODO: Implement active subscriptions retrieval
            return ResponseEntity.ok(Response.success("Active subscriptions retrieved", List.of()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(Response.error("Failed to retrieve active subscriptions: " + e.getMessage(), "RETRIEVAL_ERROR"));
        }
    }

    @GetMapping("/subscription-stats")
    public ResponseEntity<Response<Object>> getSubscriptionStats() {
        try {
            // TODO: Implement subscription statistics
            return ResponseEntity.ok(Response.success("Subscription stats retrieved", new Object()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(Response.error("Failed to retrieve subscription stats: " + e.getMessage(), "STATS_ERROR"));
        }
    }
}
