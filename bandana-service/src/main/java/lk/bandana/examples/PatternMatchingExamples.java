package lk.bandana.examples;

import java.util.List;

/**
 * Examples of Java 23 Pattern Matching features
 */
public class PatternMatchingExamples {
    
    // Sealed interface for user types
    public sealed interface UserType 
        permits RegularUser, PremiumUser, AgentUser {
        
        String getUserId();
    }
    
    public record RegularUser(String userId, int profileViews) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    public record PremiumUser(String userId, int profileViews, List<String> premiumFeatures) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    public record AgentUser(String userId, String agencyName, int clientCount) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    // Pattern matching with switch expressions
    public static String getUserDescription(UserType user) {
        if (user instanceof RegularUser) {
            RegularUser regularUser = (RegularUser) user;
            return String.format("Regular user %s with %d profile views", regularUser.userId(), regularUser.profileViews());
        } else if (user instanceof PremiumUser) {
            PremiumUser premiumUser = (PremiumUser) user;
            return String.format("Premium user %s with %d views and features: %s", premiumUser.userId(), premiumUser.profileViews(), String.join(", ", premiumUser.premiumFeatures()));
        } else if (user instanceof AgentUser) {
            AgentUser agentUser = (AgentUser) user;
            return String.format("Agent %s from %s managing %d clients", agentUser.userId(), agentUser.agencyName(), agentUser.clientCount());
        }
        return "Unknown user type";
    }
    
    // Pattern matching with guards
    public static String getAccessLevel(UserType user) {
        if (user instanceof RegularUser) {
            RegularUser regularUser = (RegularUser) user;
            if (regularUser.profileViews() > 100) {
                return "High Activity Regular";
            } else {
                return "Standard Regular";
            }
        } else if (user instanceof PremiumUser) {
            PremiumUser premiumUser = (PremiumUser) user;
            if (premiumUser.premiumFeatures().contains("unlimited_views")) {
                return "Premium Unlimited";
            } else {
                return "Premium Limited";
            }
        } else if (user instanceof AgentUser) {
            AgentUser agentUser = (AgentUser) user;
            if (agentUser.clientCount() > 50) {
                return "Senior Agent";
            } else {
                return "Junior Agent";
            }
        }
        return "Unknown";
    }
    
    // Unnamed patterns for when we don't need all values
    public static boolean isHighValueUser(UserType user) {
        if (user instanceof RegularUser) {
            return ((RegularUser) user).profileViews() > 200;
        } else if (user instanceof PremiumUser) {
            return true;
        } else if (user instanceof AgentUser) {
            return ((AgentUser) user).clientCount() > 20;
        }
        return false;
    }
    
    // Pattern matching with collections
    public static String analyzeUserList(List<UserType> users) {
        if (users.isEmpty()) {
            return "No users";
        } else if (users.size() == 1) {
            return "Single user: " + getUserDescription(users.get(0));
        } else if (users.size() <= 5) {
            return "Small group of " + users.size() + " users";
        } else {
            return "Large group of " + users.size() + " users";
        }
    }
}
