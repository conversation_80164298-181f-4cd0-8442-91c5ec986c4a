package lk.bandana.examples;

import java.util.List;

/**
 * Examples of Java 23 Pattern Matching features
 */
public class PatternMatchingExamples {
    
    // Sealed interface for user types
    public sealed interface UserType 
        permits RegularUser, PremiumUser, AgentUser {
        
        String getUserId();
    }
    
    public record RegularUser(String userId, int profileViews) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    public record PremiumUser(String userId, int profileViews, List<String> premiumFeatures) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    public record AgentUser(String userId, String agencyName, int clientCount) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    // Pattern matching with switch expressions
    public static String getUserDescription(UserType user) {
        return switch (user) {
            case RegularUser(var id, var views) -> 
                STR."Regular user \{id} with \{views} profile views";
            case PremiumUser(var id, var views, var features) -> 
                STR."Premium user \{id} with \{views} views and features: \{String.join(", ", features)}";
            case AgentUser(var id, var agency, var clients) -> 
                STR."Agent \{id} from \{agency} managing \{clients} clients";
        };
    }
    
    // Pattern matching with guards
    public static String getAccessLevel(UserType user) {
        return switch (user) {
            case RegularUser(_, var views) when views > 100 -> "High Activity Regular";
            case RegularUser(_, _) -> "Standard Regular";
            case PremiumUser(_, _, var features) when features.contains("unlimited_views") -> "Premium Unlimited";
            case PremiumUser(_, _, _) -> "Premium Limited";
            case AgentUser(_, _, var clients) when clients > 50 -> "Senior Agent";
            case AgentUser(_, _, _) -> "Junior Agent";
        };
    }
    
    // Unnamed patterns for when we don't need all values
    public static boolean isHighValueUser(UserType user) {
        return switch (user) {
            case RegularUser(_, var views) when views > 200 -> true;
            case PremiumUser(_, _, _) -> true;
            case AgentUser(_, _, var clients) when clients > 20 -> true;
            case _ -> false; // Unnamed pattern for all other cases
        };
    }
    
    // Pattern matching with collections
    public static String analyzeUserList(List<UserType> users) {
        return switch (users) {
            case List<UserType> list when list.isEmpty() -> "No users";
            case List<UserType> list when list.size() == 1 -> 
                STR."Single user: \{getUserDescription(list.getFirst())}";
            case List<UserType> list when list.size() <= 5 -> 
                STR."Small group of \{list.size()} users";
            case List<UserType> list -> 
                STR."Large group of \{list.size()} users";
        };
    }
}
