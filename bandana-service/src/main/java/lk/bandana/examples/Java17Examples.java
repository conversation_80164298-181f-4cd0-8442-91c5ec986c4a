package lk.bandana.examples;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Examples of Java 17 features
 */
public class Java17Examples {
    
    // Sealed interface (Java 17)
    public sealed interface UserType 
        permits RegularUser, PremiumUser, AgentUser {
        String getUserId();
    }
    
    public record RegularUser(String userId, int profileViews) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    public record PremiumUser(String userId, int profileViews, List<String> features) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    public record AgentUser(String userId, String agencyName, int clientCount) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    // Switch expressions (Java 14+)
    public static String getUserDescription(UserType user) {
        return switch (user) {
            case RegularUser(var id, var views) -> 
                "Regular user %s with %d profile views".formatted(id, views);
            case PremiumUser(var id, var views, var features) -> 
                "Premium user %s with %d views and features: %s".formatted(id, views, String.join(", ", features));
            case AgentUser(var id, var agency, var clients) -> 
                "Agent %s from %s managing %d clients".formatted(id, agency, clients);
        };
    }
    
    // Pattern matching for instanceof (Java 16+)
    public static String getAccessLevel(UserType user) {
        if (user instanceof RegularUser regular && regular.profileViews() > 100) {
            return "High Activity Regular";
        } else if (user instanceof RegularUser) {
            return "Standard Regular";
        } else if (user instanceof PremiumUser premium && premium.features().contains("unlimited_views")) {
            return "Premium Unlimited";
        } else if (user instanceof PremiumUser) {
            return "Premium Limited";
        } else if (user instanceof AgentUser agent && agent.clientCount() > 50) {
            return "Senior Agent";
        } else if (user instanceof AgentUser) {
            return "Junior Agent";
        }
        return "Unknown";
    }
    
    // Text blocks (Java 15+)
    public static String generateProfileSummary(String name, int age, String location) {
        return """
            Profile Summary:
            Name: %s
            Age: %d years old
            Location: %s
            Last updated: %s
            """.formatted(name, age, location, LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
    }
    
    // Records with validation
    public record UserNotification(String userName, String message, LocalDateTime timestamp) {
        public UserNotification {
            if (userName == null || userName.isBlank()) {
                throw new IllegalArgumentException("User name cannot be null or blank");
            }
            if (message == null || message.isBlank()) {
                throw new IllegalArgumentException("Message cannot be null or blank");
            }
            if (timestamp == null) {
                timestamp = LocalDateTime.now();
            }
        }
        
        public String getFormattedNotification() {
            return """
                📧 Notification for %s
                Message: %s
                Sent: %s
                """.formatted(userName, message, timestamp.format(DateTimeFormatter.ofPattern("MMM dd, yyyy 'at' HH:mm")));
        }
    }
}
