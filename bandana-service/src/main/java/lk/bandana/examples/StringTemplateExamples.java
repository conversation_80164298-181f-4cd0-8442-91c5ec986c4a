package lk.bandana.examples;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Examples of Java 23 String Templates (Preview Feature)
 */
public class StringTemplateExamples {
    
    public static void demonstrateStringTemplates() {
        var userName = "Kasun <PERSON>";
        var age = 28;
        var location = "Colombo, Sri Lanka";
        var timestamp = LocalDateTime.now();
        
        // Basic String Template
        var greeting = STR."Hello \{userName}, welcome to Bandana!";
        
        // Multi-line String Template
        var profileSummary = STR."""
            Profile Summary:
            Name: \{userName}
            Age: \{age} years old
            Location: \{location}
            Last updated: \{timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)}
            """;
        
        // String Template with expressions
        var ageCategory = STR."Age Category: \{age < 25 ? "Young Adult" : age < 35 ? "Adult" : "Mature Adult"}";
        
        // String Template with method calls
        var formattedProfile = STR."""
            \{userName.toUpperCase()} - \{location.substring(0, location.indexOf(","))}
            Profile created on: \{formatDate(timestamp)}
            """;
        
        System.out.println(greeting);
        System.out.println(profileSummary);
        System.out.println(ageCategory);
        System.out.println(formattedProfile);
    }
    
    private static String formatDate(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatter.ofPattern("MMMM dd, yyyy"));
    }
    
    // String Template in record
    public record UserNotification(String userName, String message, LocalDateTime timestamp) {
        public String getFormattedNotification() {
            return STR."""
                📧 Notification for \{userName}
                Message: \{message}
                Sent: \{timestamp.format(DateTimeFormatter.ofPattern("MMM dd, yyyy 'at' HH:mm"))}
                """;
        }
    }
}
