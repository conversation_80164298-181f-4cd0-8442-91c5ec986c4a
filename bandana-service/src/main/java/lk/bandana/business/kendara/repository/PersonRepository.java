package lk.bandana.business.kendara.repository;

import lk.bandana.business.kendara.entity.Person;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PersonRepository extends MongoRepository<Person, Long> {
    List<Person> findPersonByFirstNameLike(String name, String nic);

    Person findByFirstName(String name);

    Page<Person> findAllByPersonTypeId(Pageable pageable, String id);

    List<Person> findPersonByFirstNameLikeIgnoreCase(String name);

    List<Person> findAllByPersonTypeAndFirstNameLikeIgnoreCase(Integer personType, String firstName);

    List<Person> findAllByFirstNameLike(String type, String name);
}