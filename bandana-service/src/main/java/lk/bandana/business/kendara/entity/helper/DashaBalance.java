package lk.bandana.business.kendara.entity.helper; import org.springframework.data.annotation.Id; import org.springframework.data.mongodb.core.mapping.Document; import org.springframework.stereotype.Component; import java.time.LocalDate; import java.time.Period; @Document @Component public class DashaBalance { @Id private String planet; private LocalDate startDate; private LocalDate endDate; private Period duration; public String getPlanet() { return this.planet; } public void setPlanet(String planet) { this.planet = planet; } public LocalDate getStartdate() { return this.startDate; } public void setStartdate(LocalDate startDate) { this.startDate = startDate; } public LocalDate getEnddate() { return this.endDate; } public void setEnddate(LocalDate endDate) { this.endDate = endDate; } public Period getDuration() { return this.duration; } public void setDuration(Period duration) { this.duration = duration; } }