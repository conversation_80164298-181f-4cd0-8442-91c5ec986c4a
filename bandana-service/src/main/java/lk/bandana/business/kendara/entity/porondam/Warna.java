package lk.bandana.business.kendara.entity.porondam;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

@Document
@Component
public class Warna {
    @Id
    private Integer id;
    private String name;
    private String snName;

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSnname() {
        return this.snName;
    }

    public void setSnname(String snName) {
        this.snName = snName;
    }
}