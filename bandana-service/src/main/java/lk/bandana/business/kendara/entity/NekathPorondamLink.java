package lk.bandana.business.kendara.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
public class NekathPorondamLink {

    @Id
    private Integer id;

    private Integer nekathId;

    private String nekatha;

    private String ghana;

    private String yoni;

    private String wruksha;

    private String linga;

    private String nadi;

    private String pakshi;

    private String gothra;

    private String warna;

    private String rajju;

    private String bhootha;

    public Integer getId() {

        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getNekathid() {

        return this.nekathId;
    }

    public void setNekathid(Integer nekathId) {
        this.nekathId = nekathId;
    }

    public String getNekatha() {

        return this.nekatha;
    }

    public void setNekatha(String nekatha) {
        this.nekatha = nekatha;
    }

    public String getGhana() {

        return this.ghana;
    }

    public void setGhana(String ghana) {
        this.ghana = ghana;
    }

    public String getYoni() {

        return this.yoni;
    }

    public void setYoni(String yoni) {
        this.yoni = yoni;
    }

    public String getWruksha() {

        return this.wruksha;
    }

    public void setWruksha(String wruksha) {
        this.wruksha = wruksha;
    }

    public String getLinga() {

        return this.linga;
    }

    public void setLinga(String linga) {
        this.linga = linga;
    }

    public String getNadi() {

        return this.nadi;
    }

    public void setNadi(String nadi) {
        this.nadi = nadi;
    }

    public String getPakshi() {

        return this.pakshi;
    }

    public void setPakshi(String pakshi) {
        this.pakshi = pakshi;
    }

    public String getGothra() {

        return this.gothra;
    }

    public void setGothra(String gothra) {
        this.gothra = gothra;
    }

    public String getWarna() {

        return this.warna;
    }

    public void setWarna(String warna) {
        this.warna = warna;
    }

    public String getRajju() {

        return this.rajju;
    }

    public void setRajju(String rajju) {
        this.rajju = rajju;
    }

    public String getBhootha() {

        return this.bhootha;
    }

    public void setBhootha(String bhootha) {
        this.bhootha = bhootha;
    }
}
