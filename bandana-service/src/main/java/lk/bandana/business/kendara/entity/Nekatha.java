package lk.bandana.business.kendara.entity;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.stereotype.Component;

@Node
@Component
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class Nekatha {

    @Id
    private Integer id;

    private String name;

    private String snName;

}
