package lk.bandana.business.kendara.entity;

import lk.bandana.core.entity.MetaData;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON> Weera<PERSON> on 4/24/2018
 */
@Node
@Component
@Getter
@Setter
public class Person {

    @Id
    private Long id;

    private String firstName;

    private String lastName;

    private String birthCity;

    private Long birthLat;

    private Long birthLong;

    private Long currentLat;

    private Long currentLong;

    private Date dob;

    private String gender;

    private String email;

    private String telephone;

    private String addressLine1;

    private String addressLine2;

    private String addressLine3;

    private String country;

    @Relationship
    private MetaData personType;

    private boolean active;

}
