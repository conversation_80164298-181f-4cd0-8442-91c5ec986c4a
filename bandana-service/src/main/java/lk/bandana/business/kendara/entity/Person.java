package lk.bandana.business.kendara.entity;

import lk.bandana.core.entity.MetaData;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.mongodb.core.mapping.DBRef;

import org.springframework.stereotype.Component;

import java.util.Date;

/** * Created by <PERSON><PERSON><PERSON> on 4/24/2018 */
@
@

public class Person {

    @
    private Long id;

    private String firstName;

    private String lastName;

    private String birthCity;

    private Long birthLat;

    private Long birthLong;

    private Long currentLat;

    private Long currentLong;

    private Date dob;

    private String gender;

    private String email;

    private String telephone;

    private String addressLine1;

    private String addressLine2;

    private String addressLine3;

    private String country;

    @
    private MetaData personType;

    private boolean active;

    public Long getId() {

        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFirstname() {

        return this.firstName;
    }

    public void setFirstname(String firstName) {
        this.firstName = firstName;
    }

    public String getLastname() {

        return this.lastName;
    }

    public void setLastname(String lastName) {
        this.lastName = lastName;
    }

    public String getBirthcity() {

        return this.birthCity;
    }

    public void setBirthcity(String birthCity) {
        this.birthCity = birthCity;
    }

    public Long getBirthlat() {

        return this.birthLat;
    }

    public void setBirthlat(Long birthLat) {
        this.birthLat = birthLat;
    }

    public Long getBirthlong() {

        return this.birthLong;
    }

    public void setBirthlong(Long birthLong) {
        this.birthLong = birthLong;
    }

    public Long getCurrentlat() {

        return this.currentLat;
    }

    public void setCurrentlat(Long currentLat) {
        this.currentLat = currentLat;
    }

    public Long getCurrentlong() {

        return this.currentLong;
    }

    public void setCurrentlong(Long currentLong) {
        this.currentLong = currentLong;
    }

    public Date getDob() {

        return this.dob;
    }

    public void setDob(Date dob) {
        this.dob = dob;
    }

    public String getGender() {

        return this.gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getEmail() {

        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelephone() {

        return this.telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getAddressline1() {

        return this.addressLine1;
    }

    public void setAddressline1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressline2() {

        return this.addressLine2;
    }

    public void setAddressline2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getAddressline3() {

        return this.addressLine3;
    }

    public void setAddressline3(String addressLine3) {
        this.addressLine3 = addressLine3;
    }

    public String getCountry() {

        return this.country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public MetaData getPersontype() {

        return this.personType;
    }

    public void setPersontype(MetaData personType) {
        this.personType = personType;
    }

    public boolean isActive() {

        return this.active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }
}
