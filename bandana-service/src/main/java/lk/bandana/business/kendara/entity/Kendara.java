package lk.bandana.business.kendara.entity;

import lk.bandana.business.kendara.entity.helper.DashaBalance;

import lk.bandana.business.kendara.entity.helper.LagnaSquare;

import org.springframework.data.annotation.Id;

import org.springframework.data.mongodb.core.mapping.Document;

import org.springframework.data.neo4j.core.schema.Property;

import org.springframework.data.mongodb.core.mapping.DBRef;

import org.springframework.stereotype.Component;

import java.util.List;

/** * Created by <PERSON><PERSON><PERSON> Weera<PERSON> on 4/14/2023 */
@
@

public class Kendara {

    @
    private Long id;

    @
    private Person person;

    private long personId;

    @("Name")
    private String personName;

    private String lagna;

    private String birthDay;

    private List<LagnaSquare> sq1;

    private List<LagnaSquare> sq2;

    private List<LagnaSquare> sq3;

    private List<LagnaSquare> sq4;

    private List<LagnaSquare> sq5;

    private List<LagnaSquare> sq6;

    private List<LagnaSquare> sq7;

    private List<LagnaSquare> sq8;

    private List<LagnaSquare> sq9;

    private List<LagnaSquare> sq10;

    private List<LagnaSquare> sq11;

    private List<LagnaSquare> sq12;

    private String nekatha;

    private int nekathPada;

    private double ayanamsa;

    private double obliquity;

    private double siderealTime;

    private double lagnaSputa;

    private List<DashaBalance> dashaBalances;

    public Long getId() {

        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Person getPerson() {

        return this.person;
    }

    public void setPerson(Person person) {
        this.person = person;
    }

    public long getPersonid() {

        return this.personId;
    }

    public void setPersonid(long personId) {
        this.personId = personId;
    }

    public String getPersonname() {

        return this.personName;
    }

    public void setPersonname(String personName) {
        this.personName = personName;
    }

    public String getLagna() {

        return this.lagna;
    }

    public void setLagna(String lagna) {
        this.lagna = lagna;
    }

    public String getBirthday() {

        return this.birthDay;
    }

    public void setBirthday(String birthDay) {
        this.birthDay = birthDay;
    }

    public List<LagnaSquare> getSq1() {

        return this.sq1;
    }

    public void setSq1(List<LagnaSquare> sq1) {
        this.sq1 = sq1;
    }

    public List<LagnaSquare> getSq2() {

        return this.sq2;
    }

    public void setSq2(List<LagnaSquare> sq2) {
        this.sq2 = sq2;
    }

    public List<LagnaSquare> getSq3() {

        return this.sq3;
    }

    public void setSq3(List<LagnaSquare> sq3) {
        this.sq3 = sq3;
    }

    public List<LagnaSquare> getSq4() {

        return this.sq4;
    }

    public void setSq4(List<LagnaSquare> sq4) {
        this.sq4 = sq4;
    }

    public List<LagnaSquare> getSq5() {

        return this.sq5;
    }

    public void setSq5(List<LagnaSquare> sq5) {
        this.sq5 = sq5;
    }

    public List<LagnaSquare> getSq6() {

        return this.sq6;
    }

    public void setSq6(List<LagnaSquare> sq6) {
        this.sq6 = sq6;
    }

    public List<LagnaSquare> getSq7() {

        return this.sq7;
    }

    public void setSq7(List<LagnaSquare> sq7) {
        this.sq7 = sq7;
    }

    public List<LagnaSquare> getSq8() {

        return this.sq8;
    }

    public void setSq8(List<LagnaSquare> sq8) {
        this.sq8 = sq8;
    }

    public List<LagnaSquare> getSq9() {

        return this.sq9;
    }

    public void setSq9(List<LagnaSquare> sq9) {
        this.sq9 = sq9;
    }

    public List<LagnaSquare> getSq10() {

        return this.sq10;
    }

    public void setSq10(List<LagnaSquare> sq10) {
        this.sq10 = sq10;
    }

    public List<LagnaSquare> getSq11() {

        return this.sq11;
    }

    public void setSq11(List<LagnaSquare> sq11) {
        this.sq11 = sq11;
    }

    public List<LagnaSquare> getSq12() {

        return this.sq12;
    }

    public void setSq12(List<LagnaSquare> sq12) {
        this.sq12 = sq12;
    }

    public String getNekatha() {

        return this.nekatha;
    }

    public void setNekatha(String nekatha) {
        this.nekatha = nekatha;
    }

    public int getNekathpada() {

        return this.nekathPada;
    }

    public void setNekathpada(int nekathPada) {
        this.nekathPada = nekathPada;
    }

    public double getAyanamsa() {

        return this.ayanamsa;
    }

    public void setAyanamsa(double ayanamsa) {
        this.ayanamsa = ayanamsa;
    }

    public double getObliquity() {

        return this.obliquity;
    }

    public void setObliquity(double obliquity) {
        this.obliquity = obliquity;
    }

    public double getSiderealtime() {

        return this.siderealTime;
    }

    public void setSiderealtime(double siderealTime) {
        this.siderealTime = siderealTime;
    }

    public double getLagnasputa() {

        return this.lagnaSputa;
    }

    public void setLagnasputa(double lagnaSputa) {
        this.lagnaSputa = lagnaSputa;
    }

    public List<DashaBalance> getDashabalances() {

        return this.dashaBalances;
    }

    public void setDashabalances(List<DashaBalance> dashaBalances) {
        this.dashaBalances = dashaBalances;
    }
}
