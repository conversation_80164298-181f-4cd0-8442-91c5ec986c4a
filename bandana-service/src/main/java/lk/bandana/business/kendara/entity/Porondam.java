package lk.bandana.business.kendara.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

@Document
@Component
public class Porondam {
    @Id
    private Integer id;
    private String name;
    private String nameSn;

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNamesn() {
        return this.nameSn;
    }

    public void setNamesn(String nameSn) {
        this.nameSn = nameSn;
    }
}