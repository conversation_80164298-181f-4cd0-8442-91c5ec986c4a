package lk.bandana.custom.entity;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.stereotype.Component;

@Node
@Component
@Setter
@Getter
@RequiredArgsConstructor
@NoArgsConstructor
public class Planet {

    @Id @NonNull
    private Integer id;

    @NonNull
    private String name;

    @NonNull
    private String snName;
}
