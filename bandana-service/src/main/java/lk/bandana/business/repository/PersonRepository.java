package lk.bandana.custom.repository;

import lk.bandana.custom.entity.Person;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PersonRepository extends Neo4jRepository<Person, Long> {

    List<Person> findPersonByFirstNameLike(String name, String nic);

    Person findByFirstName(String name);

    Page<Person> findAllByPersonTypeId(Pageable pageable, String id);

    List<Person> findPersonByFirstNameLikeIgnoreCase(String name);

    List<Person> findAllByPersonTypeAndFirstNameLikeIgnoreCase(Integer personType, String firstName);

    List<Person> findAllByFirstNameLike(String type, String name);

}
