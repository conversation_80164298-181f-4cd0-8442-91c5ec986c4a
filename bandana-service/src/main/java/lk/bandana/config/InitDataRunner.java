package lk.bandana.config;

import lk.bandana.business.kendara.entity.porondam.*;

import lk.bandana.business.kendara.repository.porondam.*;

import lk.bandana.core.entity.Sequence;

import lk.bandana.core.entity.User;

import lk.bandana.core.entity.UserRole;

import lk.bandana.core.repository.SequenceRepository;

import lk.bandana.core.repository.UserRepository;

import lk.bandana.core.repository.UserRoleRepository;

import lk.bandana.business.kendara.entity.Day;

import lk.bandana.business.kendara.entity.Nekatha;

import lk.bandana.business.kendara.entity.Planet;

import lk.bandana.business.kendara.entity.Rashi;

import lk.bandana.business.kendara.repository.DayRepository;

import lk.bandana.business.kendara.repository.NekathaRepository;

import lk.bandana.business.kendara.repository.PlanetRepository;

import lk.bandana.business.kendara.repository.RashiRepository;

import lk.bandana.business.kendara.service.KendaraService;

import lk.bandana.core.service.DataInitializationService;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.boot.CommandLineRunner;

import org.springframework.context.annotation.ComponentScan;

import org.springframework.context.annotation.Configuration;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

@
@({
    "lk.bandana"}
    )

    public class InitDataRunner implements CommandLineRunner {

        @
        private UserRepository userRepository;

        @
        private UserRoleRepository userRoleRepository;

        @
        private NekathaRepository nekathaRepository;

        @
        private RashiRepository rashiRepository;

        @
        private PlanetRepository planetRepository;

        @
        private DayRepository dayRepository;

        @
        private SequenceRepository sequenceRepository;

        @
        private GanaRepository ganaRepository;

        @
        private YoniRepository yoniRepository;

        @
        private WrukshaRepository wrukshaRepository;

        @
        private LingaRepository lingaRepository;

        @
        private NadiRepository nadiRepository;

        @
        private PakshiRepository pakshiRepository;

        @
        private GothraRepository gothraRepository;

        @
        private WarnaRepository warnaRepository;

        @
        private RajjuRepository rajjuRepository;

        @
        private BhoothaRepository bhoothaRepository;

        @KendaraService kendaraService;

        @DataInitializationService dataInitializationService;
        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();

        @
        public void run(String... strings) throws Exception {

            try {

                if (userRoleRepository.count() < 1) {
                    UserRole admin = new UserRole();
                    admin.setId(1);
                    admin.setName("ADMIN");
                    admin.setDisplayName("Admin");
                    userRoleRepository.save(admin);
                    UserRole manager = new UserRole();
                    manager.setId(2);
                    manager.setName("MANAGER");
                    manager.setDisplayName("Manager");
                    userRoleRepository.save(manager);
                    UserRole matchMaker = new UserRole();
                    matchMaker.setId(3);
                    matchMaker.setName("MATCH_MAKER");
                    matchMaker.setDisplayName("Match Maker");
                    userRoleRepository.save(matchMaker);
                    UserRole candidate = new UserRole();
                    candidate.setId(4);
                    candidate.setName("CANDIDATE");
                    candidate.setDisplayName("Candidate");
                    userRoleRepository.save(candidate);
                    UserRole visitor = new UserRole();
                    visitor.setId(5);
                    visitor.setName("VISITOR");
                    visitor.setDisplayName("Visitor");
                    userRoleRepository.save(visitor);
                }

                if (sequenceRepository.findAll().size() < 1) {
                    Sequence userSeq = new Sequence(1, "user", 0L, "");
                    sequenceRepository.save(userSeq);
                    Sequence kendaraSeq = new Sequence(2, "kendara", 0L, "");
                    sequenceRepository.save(kendaraSeq);
                    Sequence invoiceSeq = new Sequence(3, "invoice", 0L, "");
                    sequenceRepository.save(invoiceSeq);
                }

                if (userRepository.count() < 1) {
                    User user = new User();
                    user.setId(sequenceRepository.findByName("user").getCounter());
                    user.setUserRoles(userRoleRepository.findAll());
                    user.setEmail("admin
                    @.lk");
                    user.setActive(true);
                    user.setFirstName("admin");
                    user.setLastName("admin");
                    user.setUsername("admin");
                    user.setPassword(bCryptPasswordEncoder.encode("admin"));
                    userRepository.save(user);
                    Sequence userSeq = sequenceRepository.findByName("user");
                    userSeq.setCounter(userSeq.getCounter() + 1);
                    sequenceRepository.save(userSeq);
                }

                if (nekathaRepository.findAll().size() < 1) {
                    Nekatha asvida = new Nekatha(1, "ASVIDA", "අස්විද");
                    nekathaRepository.save(asvida);
                    Nekatha berana = new Nekatha(2, "BERANA", "බෙරණ");
                    nekathaRepository.save(berana);
                    Nekatha ketti = new Nekatha(3, "KETTI", "කැති");
                    nekathaRepository.save(ketti);
                    Nekatha rehena = new Nekatha(4, "REHENA", "රෙහෙණ");
                    nekathaRepository.save(rehena);
                    Nekatha muwasirasa = new Nekatha(5, "MUWASIRASA", "මුවසිරස");
                    nekathaRepository.save(muwasirasa);
                    Nekatha ada = new Nekatha(6, "ADA", "අද");
                    nekathaRepository.save(ada);
                    Nekatha punawasa = new Nekatha(7, "PUNAWASA", "පුනාවස");
                    nekathaRepository.save(punawasa);
                    Nekatha pusa = new Nekatha(8, "PUSA", "පුෂ");
                    nekathaRepository.save(pusa);
                    Nekatha aslisa = new Nekatha(9, "ASLISA", "අස්ලිය");
                    nekathaRepository.save(aslisa);
                    Nekatha ma = new Nekatha(10, "MA", "මා");
                    nekathaRepository.save(ma);
                    Nekatha puwapal = new Nekatha(11, "PUWAPAL", "පුවපල්");
                    nekathaRepository.save(puwapal);
                    Nekatha uttrapal = new Nekatha(12, "UTTRAPAL", "උත්‍රපල්");
                    nekathaRepository.save(uttrapal);
                    Nekatha hatha = new Nekatha(13, "HATHA", "හත");
                    nekathaRepository.save(hatha);
                    Nekatha sitha = new Nekatha(14, "SITHA", "සිත");
                    nekathaRepository.save(sitha);
                    Nekatha sa = new Nekatha(15, "SA", "සා");
                    nekathaRepository.save(sa);
                    Nekatha visa = new Nekatha(16, "VISA", "විසා");
                    nekathaRepository.save(visa);
                    Nekatha anura = new Nekatha(17, "ANURA", "අනුර");
                    nekathaRepository.save(anura);
                    Nekatha dheta = new Nekatha(18, "DHETA", "දෙට");
                    nekathaRepository.save(dheta);
                    Nekatha mula = new Nekatha(19, "MULA", "මුල");
                    nekathaRepository.save(mula);
                    Nekatha puwasala = new Nekatha(20, "PUWASALA", "පුවසල");
                    nekathaRepository.save(puwasala);
                    Nekatha uttrasala = new Nekatha(21, "UTTRASALA", "උත්රසල");
                    nekathaRepository.save(uttrasala);
                    Nekatha dhenata = new Nekatha(22, "DHENATA", "දෙනට");
                    nekathaRepository.save(dhenata);
                    Nekatha suwana = new Nekatha(23, "SUWANA", "සුවණ");
                    nekathaRepository.save(suwana);
                    Nekatha siyawasa = new Nekatha(24, "SIYAWASA", "සියාවස");
                    nekathaRepository.save(siyawasa);
                    Nekatha puwaputupa = new Nekatha(25, "PUWAPUTUPA", "පුවපුටුප");
                    nekathaRepository.save(puwaputupa);
                    Nekatha uttraputupa = new Nekatha(26, "UTTRAPUTUPA", "උත්‍රපුටුප");
                    nekathaRepository.save(uttraputupa);
                    Nekatha rhewatee = new Nekatha(27, "RHEWATEE", "රේවතී");
                    nekathaRepository.save(rhewatee);
                }

                if (rashiRepository.findAll().size() < 1) {
                    Rashi mesha = new Rashi(1, "MESHA", "මේෂ", "KUJA");
                    rashiRepository.save(mesha);
                    Rashi vrushaba = new Rashi(2, "VRUSHABA", "වෘෂභ", "SIKURU");
                    rashiRepository.save(vrushaba);
                    Rashi mithuna = new Rashi(3, "MITHUNA", "මිථුන", "BUDA");
                    rashiRepository.save(mithuna);
                    Rashi kataka = new Rashi(4, "KATAKA", "කටක", "KUJA");
                    rashiRepository.save(kataka);
                    Rashi sinha = new Rashi(5, "SINHA", "සිංහ", "RAVI");
                    rashiRepository.save(sinha);
                    Rashi kanya = new Rashi(6, "KANNYA", "කන්‍යා", "SADU");
                    rashiRepository.save(kanya);
                    Rashi thula = new Rashi(7, "THULA", "තුලා", "SIKURU");
                    rashiRepository.save(thula);
                    Rashi vruschika = new Rashi(8, "VHUSCHIKA", "වෘශ්චික", "KUJA");
                    rashiRepository.save(vruschika);
                    Rashi dhanu = new Rashi(9, "DHANU", "ධනු", "GURU");
                    rashiRepository.save(dhanu);
                    Rashi makara = new Rashi(10, "MAKARA", "මකර", "SHANI");
                    rashiRepository.save(makara);
                    Rashi kumba = new Rashi(11, "KUMBA", "කුම්භ", "SHANI");
                    rashiRepository.save(kumba);
                    Rashi meena = new Rashi(12, "MEENA", "මීන", "GURU");
                    rashiRepository.save(meena);
                }

                if (planetRepository.findAll().size() < 1) {
                    Planet ketu = new Planet(1, "KETU", "කේතු");
                    planetRepository.save(ketu);
                    Planet sikuru = new Planet(2, "SIKURU", "සිකුරු");
                    planetRepository.save(sikuru);
                    Planet ravi = new Planet(3, "RAVI", "රවි");
                    planetRepository.save(ravi);
                    Planet sadu = new Planet(4, "SADU", "සඳු");
                    planetRepository.save(sadu);
                    Planet kuja = new Planet(5, "KUJA", "කුජ");
                    planetRepository.save(kuja);
                    Planet rahu = new Planet(6, "RAHU", "රාහු");
                    planetRepository.save(rahu);
                    Planet guru = new Planet(7, "GURU", "ගුරු");
                    planetRepository.save(guru);
                    Planet shani = new Planet(8, "SHANI", "ශනි");
                    planetRepository.save(shani);
                    Planet buda = new Planet(9, "BUDA", "බුධ");
                    planetRepository.save(buda);
                }

                if (dayRepository.findAll().size() < 1) {
                    Day monday = new Day(1, "MONDAY", "සඳුදා");
                    dayRepository.save(monday);
                    Day tuesday = new Day(2, "TUESDAY", "අඟහරුවාදා");
                    dayRepository.save(tuesday);
                    Day wednesday = new Day(3, "WEDNESDAY", "බදාදා");
                    dayRepository.save(wednesday);
                    Day thursday = new Day(4, "THURSDAY", "බ්‍රහස්පතින්දා");
                    dayRepository.save(thursday);
                    Day friday = new Day(5, "FRIDAY", "සිකුරාදා");
                    dayRepository.save(friday);
                    Day saturday = new Day(6, "SATURDAY", "සෙනසුරාදා");
                    dayRepository.save(saturday);
                    Day sunday = new Day(7, "SUNDAY", "ඉරිදා");
                    dayRepository.save(sunday);
                }

                if (ganaRepository.findAll().size() < 1) {
                    Gana deva = new Gana(1, "Deva", "දේව");
                    ganaRepository.save(deva);
                    Gana manushya = new Gana(2, "Manushya", "මනුෂ්‍ය");
                    ganaRepository.save(manushya);
                    Gana raksha = new Gana(3, "Raksha", "රාක්ෂ");
                    ganaRepository.save(raksha);
                }

                if (yoniRepository.findAll().size() < 1) {
                    Yoni ashwa = new Yoni(1, "Ashwa", "අශ්ව", false);
                    yoniRepository.save(ashwa);
                    Yoni ath = new Yoni(2, "Ath", "ඇත්", false);
                    yoniRepository.save(ath);
                    Yoni eludena = new Yoni(3, "Eludena", "එළුදෙන", true);
                    yoniRepository.save(eludena);
                    Yoni sarpa = new Yoni(4, "Sarpa", "සර්ප", false);
                    yoniRepository.save(sarpa);
                    Yoni sapini = new Yoni(5, "Sapini", "සැපිනි", true);
                    yoniRepository.save(sapini);
                    Yoni sunakha = new Yoni(6, "Sunaka", "සුනඛ", false);
                    yoniRepository.save(sunakha);
                    Yoni balal = new Yoni(7, "Balal", "බලල්", false);
                    yoniRepository.save(balal);
                    Yoni elu = new Yoni(8, "Elu", "එළු", false);
                    yoniRepository.save(elu);
                    Yoni balali = new Yoni(9, "Balali", "බැළලී", true);
                    yoniRepository.save(balali);
                    Yoni mushika = new Yoni(10, "Mushika", "මුෂික", false);
                    yoniRepository.save(mushika);
                    Yoni mushikadena = new Yoni(11, "Mushikadena", "මුශිකදෙන", true);
                    yoniRepository.save(mushikadena);
                    Yoni gawadena = new Yoni(12, "Gawadena", "ගවදෙන", true);
                    yoniRepository.save(gawadena);
                    Yoni meedena = new Yoni(13, "Meedena", "මීදෙන", true);
                    yoniRepository.save(meedena);
                    Yoni wyagra = new Yoni(14, "Wyagra", "ව්‍යග්‍රයා", false);
                    yoniRepository.save(wyagra);
                    Yoni meegon = new Yoni(15, "Meegon", "මීගොන්", false);
                    yoniRepository.save(meegon);
                    Yoni wyagradena = new Yoni(16, "Wyagradena", "ව්‍යාග්‍රදෙන", true);
                    yoniRepository.save(wyagradena);
                    Yoni muwadena = new Yoni(17, "Muwadena", "මුවදෙන", true);
                    yoniRepository.save(muwadena);
                    Yoni muwa = new Yoni(18, "Muwa", "මුවා", false);
                    yoniRepository.save(muwa);
                    Yoni sunakhi = new Yoni(19, "Sunakhi", "සුනඛී", true);
                    yoniRepository.save(sunakhi);
                    Yoni wandiri = new Yoni(20, "Wandiri", "වැඳිරී", true);
                    yoniRepository.save(wandiri);
                    Yoni mugatidena = new Yoni(21, "Mugatidena", "මුගටිදෙන", true);
                    yoniRepository.save(mugatidena);
                    Yoni wandura = new Yoni(22, "Wandura", "වඳුරා", false);
                    yoniRepository.save(wandura);
                    Yoni sinhadena = new Yoni(23, "Sinhadena", "සිංහදෙන", true);
                    yoniRepository.save(sinhadena);
                    Yoni welamba = new Yoni(24, "Welamba", "වෙළඹ", true);
                    yoniRepository.save(welamba);
                    Yoni sinhaya = new Yoni(25, "Sinhaya", "සිංහයා", false);
                    yoniRepository.save(sinhaya);
                    Yoni gon = new Yoni(26, "Gon", "ගොන්", false);
                    yoniRepository.save(gon);
                    Yoni athini = new Yoni(27, "Athini", "ඇතිනි", true);
                    yoniRepository.save(athini);
                }

                if (wrukshaRepository.findAll().size() < 1) {
                    Wruksha godaka = new Wruksha(1, "Godaka", "ගොඩක", false);
                    wrukshaRepository.save(godaka);
                    Wruksha nelli = new Wruksha(2, "Nelli", "නෙල්ලි", false);
                    wrukshaRepository.save(nelli);
                    Wruksha aththikka = new Wruksha(3, "Aththikka", "අත්තික්කා", false);
                    wrukshaRepository.save(aththikka);
                    Wruksha madan = new Wruksha(4, "Madam", "මාදන්", false);
                    wrukshaRepository.save(madan);
                    Wruksha kaluwara = new Wruksha(5, "Kaluwara", "කළුවර", false);
                    wrukshaRepository.save(kaluwara);
                    Wruksha kihiri = new Wruksha(6, "Kihiri", "කිහිරි", false);
                    wrukshaRepository.save(kihiri);
                    Wruksha una = new Wruksha(7, "Una", "උණ", false);
                    wrukshaRepository.save(una);
                    Wruksha bo = new Wruksha(8, "Bo", "බෝ", true);
                    wrukshaRepository.save(bo);
                    Wruksha domba = new Wruksha(9, "Domba", "දොඹ", true);
                    wrukshaRepository.save(domba);
                    Wruksha nuga = new Wruksha(10, "Nuga", "නුග", true);
                    wrukshaRepository.save(nuga);
                    Wruksha kela = new Wruksha(11, "Kela", "කෑල", true);
                    wrukshaRepository.save(kela);
                    Wruksha aliri = new Wruksha(12, "Aliri", "අලිරි", true);
                    wrukshaRepository.save(aliri);
                    Wruksha dimbul = new Wruksha(13, "Dimbul", "දිඹුල්", false);
                    wrukshaRepository.save(dimbul);
                    Wruksha beli = new Wruksha(14, "Beli", "බෙලි", false);
                    wrukshaRepository.save(beli);
                    Wruksha kumbuk = new Wruksha(15, "Kumbuk", "කුඹුක්", false);
                    wrukshaRepository.save(kumbuk);
                    Wruksha sapu = new Wruksha(16, "Sapu", "සපු", false);
                    wrukshaRepository.save(sapu);
                    Wruksha munamal = new Wruksha(17, "Munamal", "මුනමල්", false);
                    wrukshaRepository.save(munamal);
                    Wruksha wetake = new Wruksha(18, "Wetake", "වැටකේ", true);
                    wrukshaRepository.save(wetake);
                    Wruksha imbul = new Wruksha(19, "Imbul", "ඉඹුල්", true);
                    wrukshaRepository.save(imbul);
                    Wruksha hopalu = new Wruksha(20, "Hopalu", "හෝපලු", true);
                    wrukshaRepository.save(hopalu);
                    Wruksha kos = new Wruksha(21, "Kos", "කොස්", true);
                    wrukshaRepository.save(kos);
                    Wruksha wara = new Wruksha(22, "Wara", "වරා", true);
                    wrukshaRepository.save(wara);
                    Wruksha samadara = new Wruksha(23, "Samadara", "සමදරා", false);
                    wrukshaRepository.save(samadara);
                    Wruksha kolon = new Wruksha(24, "Kolon", "කොලොන්", false);
                    wrukshaRepository.save(kolon);
                    Wruksha meeAmba = new Wruksha(25, "MeeAmba", "මීඅඹ", true);
                    wrukshaRepository.save(meeAmba);
                    Wruksha kohomba = new Wruksha(26, "Kohomba", "කොහොඹ", false);
                    wrukshaRepository.save(kohomba);
                    Wruksha mee = new Wruksha(27, "Mee", "මී", true);
                    wrukshaRepository.save(mee);
                }

                if (lingaRepository.findAll().size() < 1) {
                    Linga purusha = new Linga(1, "Purusha", "පුරුෂ");
                    lingaRepository.save(purusha);
                    Linga sthree = new Linga(2, "Sthree", "ස්ත්‍රී");
                    lingaRepository.save(sthree);
                    Linga napunsaka = new Linga(3, "Napunsaka", "නපුන්සක");
                    lingaRepository.save(napunsaka);
                }

                if (nadiRepository.findAll().size() < 1) {
                    Nadi poorwa = new Nadi(1, "Poorwa", "පූර්ව");
                    nadiRepository.save(poorwa);
                    Nadi madya = new Nadi(2, "Madya", "මධ්‍ය");
                    nadiRepository.save(madya);
                    Nadi anthya = new Nadi(3, "Anthya", "අන්ත්‍ය");
                    nadiRepository.save(anthya);
                }

                if (pakshiRepository.findAll().size() < 1) {
                    Pakshi rajaliya = new Pakshi(1, "Rajaliya", "රාජාලියා");
                    pakshiRepository.save(rajaliya);
                    Pakshi bakamuna = new Pakshi(2, "Bakamuna", "බකමුණ");
                    pakshiRepository.save(bakamuna);
                    Pakshi kaka = new Pakshi(3, "Kaka", "කාක");
                    pakshiRepository.save(kaka);
                    Pakshi kukkuta = new Pakshi(4, "Kukkuta", "කුක්කුට");
                    pakshiRepository.save(kukkuta);
                    Pakshi mayura = new Pakshi(5, "Mayura", "මයුර");
                    pakshiRepository.save(mayura);
                }

                if (gothraRepository.findAll().size() < 1) {
                    Gothra marichi = new Gothra(1, "Marichi", "මරිචි");
                    gothraRepository.save(marichi);
                    Gothra athri = new Gothra(2, "Athri", "අත්‍රි");
                    gothraRepository.save(athri);
                    Gothra washishta = new Gothra(3, "Washishta", "වශිෂ්ට");
                    gothraRepository.save(washishta);
                    Gothra angira = new Gothra(4, "Angira", "අන්ගිර");
                    gothraRepository.save(angira);
                    Gothra pulasthi = new Gothra(5, "Pulasthi", "පුලස්ති");
                    gothraRepository.save(pulasthi);
                    Gothra pulanga = new Gothra(6, "Pulanga", "පුලඟ");
                    gothraRepository.save(pulanga);
                    Gothra kruthu = new Gothra(7, "Kruthu", "කෘතු");
                    gothraRepository.save(kruthu);
                }

                if (warnaRepository.findAll().size() < 1) {
                    Warna brahmana = new Warna(1, "Brahmana", "බ්‍රාහ්මණ");
                    warnaRepository.save(brahmana);
                    Warna kshathreeya = new Warna(2, "Kshathreeya", "ක්ෂත්‍රීය");
                    warnaRepository.save(kshathreeya);
                    Warna waishya = new Warna(3, "Waishya", "වෛශ්‍ය");
                    warnaRepository.save(waishya);
                    Warna shudra = new Warna(4, "Shudra", "ශුද්‍ර");
                    warnaRepository.save(shudra);
                    Warna panchama = new Warna(5, "Panchama", "පංචම");
                    warnaRepository.save(panchama);
                    Warna sankara = new Warna(6, "Sankara", "සංකර");
                    warnaRepository.save(sankara);
                }

                if (rajjuRepository.findAll().size() < 1) {
                    Rajju pada = new Rajju(1, "Pada", "පාද");
                    rajjuRepository.save(pada);
                    Rajju katiya = new Rajju(2, "Katiya", "කටිය");
                    rajjuRepository.save(katiya);
                    Rajju nabhi = new Rajju(3, "Nabhi", "නාභි");
                    rajjuRepository.save(nabhi);
                    Rajju bahu = new Rajju(4, "Bahu", "බාහු");
                    rajjuRepository.save(bahu);
                    Rajju shiro = new Rajju(5, "Shiro", "ශිරෝ");
                    rajjuRepository.save(shiro);
                }

                if (bhoothaRepository.findAll().size() < 1) {
                    Bhootha patavi = new Bhootha(1, "Patavi", "පඨවි");
                    bhoothaRepository.save(patavi);
                    Bhootha aapo = new Bhootha(2, "Aapo", "ආපෝ");
                    bhoothaRepository.save(aapo);
                    Bhootha thejo = new Bhootha(3, "Thejo", "තේජෝ");
                    bhoothaRepository.save(thejo);
                    Bhootha waayo = new Bhootha(4, "Waayo", "වායෝ");
                    bhoothaRepository.save(waayo);
                    Bhootha akasha = new Bhootha(5, "Akasha", "ආකාශ");
                    bhoothaRepository.save(akasha);
                }
                kendaraService.calc();

                Initialize new matchmaking system data dataInitializationService.initializeAllData();
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
