package lk.bandana.config; import org.springframework.context.annotation.Bean; import org.springframework.context.annotation.ComponentScan; import org.springframework.context.annotation.Configuration; import org.springframework.data.domain.AuditorAware; import org.springframework.data.mongodb.config.EnableMongoAuditing; import org.springframework.data.mongodb.repository.config.EnableMongoRepositories; import org.springframework.transaction.annotation.EnableTransactionManagement; /** * Created by <PERSON><PERSON><PERSON> on 7/27/2017. * Updated for MongoDB integration */ @Configuration @EnableTransactionManagement @EnableMongoAuditing @ComponentScan(basePackages = { "lk.bandana.user", "lk.bandana.profile", "lk.bandana.subscription", "lk.bandana.matching", "lk.bandana.location", "lk.bandana.configuration", "lk.bandana.common", "lk.bandana.business.kendara", "lk.bandana.management", "lk.bandana.agent", "lk.bandana.public" }) @EnableMongoRepositories(basePackages = { "lk.bandana.user.repository", "lk.bandana.profile.repository", "lk.bandana.subscription.repository", "lk.bandana.matching.repository", "lk.bandana.location.repository", "lk.bandana.configuration.repository", "lk.bandana.business.repository" }) public class DbConfig { @Bean public AuditorAware<String> auditorProvider() { return new SpringSecurityAuditAwareImpl(); } } 