package lk.bandana.agent.controller; import lk.bandana.core.entity.CompatibilityMatch; import lk.bandana.core.entity.UserProfile; import lk.bandana.core.service.MatchingService; import lk.bandana.core.service.SubscriptionService; import lk.bandana.core.service.UserProfileService; import org.springframework.beans.factory.annotation.Autowired; import org.springframework.data.domain.Page; import org.springframework.data.domain.PageRequest; import org.springframework.data.domain.Pageable; import org.springframework.http.HttpStatus; import org.springframework.http.ResponseEntity; import org.springframework.security.access.prepost.PreAuthorize; import org.springframework.security.core.Authentication; import org.springframework.web.bind.annotation.*; import java.util.List; import java.util.Map; /** * Controller for agent matching functionality * Accessible only by agents via bandana-agent-ui */ @RestController @RequestMapping("/api/agent/matching") @PreAuthorize("hasRole('AGENT')") public class AgentMatchingController { @Autowired private MatchingService matchingService; @Autowired private UserProfileService userProfileService; @Autowired private SubscriptionService subscriptionService; /** * Get matches for agent's client profiles */ @GetMapping("/client-matches") public ResponseEntity<List<CompatibilityMatch>> getClientMatches( Authentication authentication, @RequestParam(required = false) Integer minScore) { try { Long agentId = getCurrentUserId(authentication); // Check if agent has access to cross-agent matching if (!subscriptionService.canAccessCrossAgentMatches(agentId)) { return ResponseEntity.status(HttpStatus.FORBIDDEN).build(); } // Get the agent's matching threshold Integer threshold = minScore != null ? minScore : subscriptionService.getMatchingThreshold(agentId); List<CompatibilityMatch> matches = matchingService.findMatchesForAgentClients(agentId, threshold); return ResponseEntity.ok(matches); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get cross-agent matches (matches between different agents' clients) */ @GetMapping("/cross-agent-matches") public ResponseEntity<List<CompatibilityMatch>> getCrossAgentMatches( Authentication authentication, @RequestParam(required = false) Integer minScore) { try { Long agentId = getCurrentUserId(authentication); // Check if agent has access to cross-agent matching if (!subscriptionService.canAccessCrossAgentMatches(agentId)) { return ResponseEntity.status(HttpStatus.FORBIDDEN).build(); } // Get the agent's matching threshold Integer threshold = minScore != null ? minScore : subscriptionService.getMatchingThreshold(agentId); List<CompatibilityMatch> matches = matchingService.findCrossAgentMatches(threshold); return ResponseEntity.ok(matches); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get matches for a specific client profile */ @GetMapping("/profile/{profileId}/matches") public ResponseEntity<List<CompatibilityMatch>> getMatchesForProfile( @PathVariable Long profileId, Authentication authentication, @RequestParam(required = false) Integer minScore) { try { Long agentId = getCurrentUserId(authentication); // Verify that the profile belongs to this agent UserProfile profile = userProfileService.findById(profileId).orElse(null); if (profile == null || !isProfileManagedByAgent(profile, agentId)) { return ResponseEntity.status(HttpStatus.FORBIDDEN).build(); } Integer threshold = minScore != null ? minScore : subscriptionService.getMatchingThreshold(agentId); List<CompatibilityMatch> matches = matchingService.findHighQualityMatches(profileId, threshold); return ResponseEntity.ok(matches); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Send contact request for a match */ @PostMapping("/matches/{matchId}/contact-request") public ResponseEntity<CompatibilityMatch> sendContactRequest( @PathVariable Long matchId, @RequestBody Map<String, Long> request, Authentication authentication) { try { Long agentId = getCurrentUserId(authentication); Long senderProfileId = request.get("senderProfileId"); // Verify that the sender profile belongs to this agent UserProfile senderProfile = userProfileService.findById(senderProfileId).orElse(null); if (senderProfile == null || !isProfileManagedByAgent(senderProfile, agentId)) { return ResponseEntity.status(HttpStatus.FORBIDDEN).build(); } // Check if agent can access this match if (!matchingService.isMatchVisibleToAgent(matchId, agentId)) { return ResponseEntity.status(HttpStatus.FORBIDDEN).build(); } CompatibilityMatch updatedMatch = matchingService.sendContactRequest(matchId, senderProfileId); return ResponseEntity.ok(updatedMatch); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Mark profile as viewed in a match */ @PostMapping("/matches/{matchId}/view") public ResponseEntity<CompatibilityMatch> markProfileViewed( @PathVariable Long matchId, @RequestBody Map<String, Long> request, Authentication authentication) { try { Long agentId = getCurrentUserId(authentication); Long viewerProfileId = request.get("viewerProfileId"); // Verify that the viewer profile belongs to this agent UserProfile viewerProfile = userProfileService.findById(viewerProfileId).orElse(null); if (viewerProfile == null || !isProfileManagedByAgent(viewerProfile, agentId)) { return ResponseEntity.status(HttpStatus.FORBIDDEN).build(); } // Check if agent can access this match if (!matchingService.isMatchVisibleToAgent(matchId, agentId)) { return ResponseEntity.status(HttpStatus.FORBIDDEN).build(); } CompatibilityMatch updatedMatch = matchingService.markProfileViewed(matchId, viewerProfileId); return ResponseEntity.ok(updatedMatch); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get agent's client profiles */ @GetMapping("/client-profiles") public ResponseEntity<List<UserProfile>> getClientProfiles(Authentication authentication) { try { Long agentId = getCurrentUserId(authentication); List<UserProfile> clientProfiles = userProfileService.findProfilesManagedByAgent(agentId); return ResponseEntity.ok(clientProfiles); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Check if agent can add more client profiles */ @GetMapping("/can-add-client") public ResponseEntity<Map<String, Object>> canAddClient(Authentication authentication) { try { Long agentId = getCurrentUserId(authentication); boolean canAdd = !subscriptionService.hasReachedClientLimit(agentId); int maxClients = subscriptionService.getMaxClientProfiles(agentId); int currentClients = userProfileService.findProfilesManagedByAgent(agentId).size(); Map<String, Object> result = Map.of( "canAdd", canAdd, "maxClients", maxClients, "currentClients", currentClients, "remaining", maxClients - currentClients ); return ResponseEntity.ok(result); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Get agent's matching threshold */ @GetMapping("/threshold") public ResponseEntity<Map<String, Integer>> getMatchingThreshold(Authentication authentication) { try { Long agentId = getCurrentUserId(authentication); Integer threshold = subscriptionService.getMatchingThreshold(agentId); return ResponseEntity.ok(Map.of("threshold", threshold)); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Search for potential matches with filters */ @GetMapping("/search") public ResponseEntity<Page<UserProfile>> searchPotentialMatches( @RequestParam(required = false) String gender, @RequestParam(required = false) Integer minAge, @RequestParam(required = false) Integer maxAge, @RequestParam(required = false) Integer minHeight, @RequestParam(required = false) Integer maxHeight, @RequestParam(required = false) String province, @RequestParam(required = false) String district, @RequestParam(required = false) String education, @RequestParam(required = false) String occupation, @RequestParam(required = false) String religion, @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int size, Authentication authentication) { try { Long agentId = getCurrentUserId(authentication); // Check if agent has access to cross-agent matching if (!subscriptionService.canAccessCrossAgentMatches(agentId)) { return ResponseEntity.status(HttpStatus.FORBIDDEN).build(); } Pageable pageable = PageRequest.of(page, size); Page<UserProfile> profiles = userProfileService.searchProfiles( gender, minAge, maxAge, minHeight, maxHeight, province, district, education, occupation, religion, pageable); return ResponseEntity.ok(profiles); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } /** * Calculate compatibility between two profiles */ @PostMapping("/calculate-compatibility") public ResponseEntity<CompatibilityMatch> calculateCompatibility( @RequestBody Map<String, Long> request, Authentication authentication) { try { Long agentId = getCurrentUserId(authentication); Long profileId1 = request.get("profileId1"); Long profileId2 = request.get("profileId2"); // Verify that at least one profile belongs to this agent UserProfile profile1 = userProfileService.findById(profileId1).orElse(null); UserProfile profile2 = userProfileService.findById(profileId2).orElse(null); if (profile1 == null || profile2 == null) { return ResponseEntity.badRequest().build(); } boolean hasAccess = isProfileManagedByAgent(profile1, agentId) || isProfileManagedByAgent(profile2, agentId); if (!hasAccess) { return ResponseEntity.status(HttpStatus.FORBIDDEN).build(); } CompatibilityMatch match = matchingService.calculateCompatibility(profileId1, profileId2); return ResponseEntity.ok(match); } catch (Exception e) { return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build(); } } // Helper methods private Long getCurrentUserId(Authentication authentication) { // Extract user ID from authentication // This is a simplified implementation return 1L; // TODO: Implement proper user ID extraction } private boolean isProfileManagedByAgent(UserProfile profile, Long agentId) { // Check if the profile is managed by the specified agent return profile.getIsAgentManaged() && profile.getManagedByAgent() != null && profile.getManagedByAgent().getId().equals(agentId); } } 