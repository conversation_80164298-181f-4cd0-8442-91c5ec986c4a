package lk.bandana.user.repository;

import lk.bandana.user.entity.UserRole;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRoleRepository extends MongoRepository<UserRole, String> {
    Optional<UserRole> findByName(String name);

    List<UserRole> findByActiveTrueOrderByLevel();

    List<UserRole> findByLevelGreaterThanEqual(Integer level);

    List<UserRole> findByLevelLessThanEqual(Integer level);
}