package lk.bandana.user.repository; import lk.bandana.user.entity.User; import org.springframework.data.mongodb.repository.MongoRepository; import org.springframework.data.mongodb.repository.Query; import org.springframework.data.repository.query.Param; import org.springframework.stereotype.Repository; import java.util.List; import java.util.Optional; @Repository public interface UserRepository extends MongoRepository<User, String> { Optional<User> findByUsername(String username); Optional<User> findByEmail(String email); Optional<User> findByPhoneNumber(String phoneNumber); List<User> findByEnabledTrue(); List<User> findByActiveTrue(); @Query("{'userRoles.name': ?0}") List<User> findByRole(String roleName); @Query("{'phoneVerified': false, 'phoneNumber': {$ne: null}}") List<User> findUsersWithUnverifiedPhones(); @Query("{'verificationCodeExpiry': {$lt: ?0}}") List<User> findUsersWithExpiredVerificationCodes(String now); boolean existsByUsername(String username); boolean existsByEmail(String email); boolean existsByPhoneNumber(String phoneNumber); } 