package lk.bandana.public.controller;

import lk.bandana.core.entity.UserProfile;
import lk.bandana.core.service.UserProfileService;
import lk.bandana.core.service.SubscriptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controller for public user profile management
 * Accessible by regular users via bandana-public-ui
 */
@RestController
@RequestMapping("/api/public/profile")
@PreAuthorize("hasRole('USER')")
public class PublicProfileController {

    @Autowired
    private UserProfileService userProfileService;

    @Autowired
    private SubscriptionService subscriptionService;

    /**
     * Create or update user profile
     */
    @PostMapping
    public ResponseEntity<UserProfile> createOrUpdateProfile(
            @RequestBody UserProfile userProfile,
            Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            
            // Set the user relationship
            // userProfile.setUser(userService.findById(userId));
            
            // Validate profile data
            if (!userProfileService.validateProfileData(userProfile)) {
                return ResponseEntity.badRequest().build();
            }
            
            UserProfile savedProfile = userProfileService.save(userProfile);
            return ResponseEntity.ok(savedProfile);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get current user's profile
     */
    @GetMapping("/my-profile")
    public ResponseEntity<UserProfile> getMyProfile(Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            // Find profile by user ID
            // This would require a method in UserProfileService to find by user ID
            // For now, returning a placeholder response
            return ResponseEntity.ok(new UserProfile());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update profile photo
     */
    @PostMapping("/photo")
    public ResponseEntity<UserProfile> updateProfilePhoto(
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            String photoUrl = request.get("photoUrl");
            Long profileId = Long.parseLong(request.get("profileId"));
            
            // Verify profile ownership
            UserProfile profile = userProfileService.findById(profileId).orElse(null);
            if (profile == null || !isProfileOwnedByUser(profile, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            
            // Check subscription limits
            if (!subscriptionService.canUploadPhoto(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .header("X-Error-Message", "Photo upload limit reached")
                    .build();
            }
            
            UserProfile updatedProfile = userProfileService.updateProfilePhoto(profileId, photoUrl);
            subscriptionService.incrementPhotoUploadUsage(userId);
            
            return ResponseEntity.ok(updatedProfile);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Add additional photo
     */
    @PostMapping("/additional-photo")
    public ResponseEntity<UserProfile> addAdditionalPhoto(
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            String photoUrl = request.get("photoUrl");
            Long profileId = Long.parseLong(request.get("profileId"));
            
            // Verify profile ownership
            UserProfile profile = userProfileService.findById(profileId).orElse(null);
            if (profile == null || !isProfileOwnedByUser(profile, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            
            // Check subscription limits
            if (!subscriptionService.canUploadPhoto(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .header("X-Error-Message", "Photo upload limit reached")
                    .build();
            }
            
            UserProfile updatedProfile = userProfileService.addAdditionalPhoto(profileId, photoUrl);
            subscriptionService.incrementPhotoUploadUsage(userId);
            
            return ResponseEntity.ok(updatedProfile);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update contact information
     */
    @PutMapping("/{profileId}/contact")
    public ResponseEntity<UserProfile> updateContactInformation(
            @PathVariable Long profileId,
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            
            // Verify profile ownership
            UserProfile profile = userProfileService.findById(profileId).orElse(null);
            if (profile == null || !isProfileOwnedByUser(profile, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            
            String email = request.get("email");
            String phone = request.get("phone");
            String whatsapp = request.get("whatsapp");
            
            UserProfile updatedProfile = userProfileService.updateContactInformation(
                profileId, email, phone, whatsapp);
            
            return ResponseEntity.ok(updatedProfile);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update privacy settings
     */
    @PutMapping("/{profileId}/privacy")
    public ResponseEntity<UserProfile> updatePrivacySettings(
            @PathVariable Long profileId,
            @RequestBody Map<String, Boolean> request,
            Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            
            // Verify profile ownership
            UserProfile profile = userProfileService.findById(profileId).orElse(null);
            if (profile == null || !isProfileOwnedByUser(profile, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            
            Boolean profileVisible = request.get("profileVisible");
            Boolean contactInfoVisible = request.get("contactInfoVisible");
            Boolean photoVisible = request.get("photoVisible");
            Boolean casteVisible = request.get("casteVisible");
            
            UserProfile updatedProfile = userProfileService.updatePrivacySettings(
                profileId, profileVisible, contactInfoVisible, photoVisible, casteVisible);
            
            return ResponseEntity.ok(updatedProfile);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get profile completion status
     */
    @GetMapping("/{profileId}/completion")
    public ResponseEntity<Map<String, Object>> getProfileCompletion(
            @PathVariable Long profileId,
            Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            
            // Verify profile ownership
            UserProfile profile = userProfileService.findById(profileId).orElse(null);
            if (profile == null || !isProfileOwnedByUser(profile, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            
            UserProfile updatedProfile = userProfileService.calculateProfileCompletion(profileId);
            List<String> validationErrors = userProfileService.getProfileValidationErrors(updatedProfile);
            
            Map<String, Object> completion = Map.of(
                "completionPercentage", updatedProfile.getCompletionPercentage(),
                "qualityScore", updatedProfile.getProfileQualityScore(),
                "status", updatedProfile.getStatus(),
                "validationErrors", validationErrors,
                "isComplete", validationErrors.isEmpty()
            );
            
            return ResponseEntity.ok(completion);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Search profiles (limited by subscription)
     */
    @GetMapping("/search")
    public ResponseEntity<Page<UserProfile>> searchProfiles(
            @RequestParam(required = false) String gender,
            @RequestParam(required = false) Integer minAge,
            @RequestParam(required = false) Integer maxAge,
            @RequestParam(required = false) Integer minHeight,
            @RequestParam(required = false) Integer maxHeight,
            @RequestParam(required = false) String province,
            @RequestParam(required = false) String district,
            @RequestParam(required = false) String education,
            @RequestParam(required = false) String occupation,
            @RequestParam(required = false) String religion,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            
            // Check subscription limits
            if (!subscriptionService.canViewProfile(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .header("X-Error-Message", "Profile view limit reached")
                    .build();
            }
            
            Pageable pageable = PageRequest.of(page, size);
            Page<UserProfile> profiles = userProfileService.searchProfiles(
                gender, minAge, maxAge, minHeight, maxHeight, province, district, 
                education, occupation, religion, pageable);
            
            // Filter profiles based on subscription tier
            // High-quality profiles are only accessible to premium subscribers
            if (!subscriptionService.hasAccessToHighQualityProfiles(userId)) {
                // Filter out high-quality profiles
                // This would require additional logic in the service layer
            }
            
            subscriptionService.incrementProfileViewUsage(userId);
            
            return ResponseEntity.ok(profiles);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get profile by ID (limited by subscription)
     */
    @GetMapping("/{profileId}")
    public ResponseEntity<UserProfile> getProfile(
            @PathVariable Long profileId,
            Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            
            // Check subscription limits
            if (!subscriptionService.canViewProfile(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .header("X-Error-Message", "Profile view limit reached")
                    .build();
            }
            
            UserProfile profile = userProfileService.findById(profileId).orElse(null);
            if (profile == null || profile.getStatus() != UserProfile.ProfileStatus.APPROVED) {
                return ResponseEntity.notFound().build();
            }
            
            // Check if user has access to high-quality profiles
            if (profile.getProfileQualityScore() >= 80 && 
                !subscriptionService.hasAccessToHighQualityProfiles(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .header("X-Error-Message", "Premium subscription required for high-quality profiles")
                    .build();
            }
            
            subscriptionService.incrementProfileViewUsage(userId);
            
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get subscription status and limits
     */
    @GetMapping("/subscription-status")
    public ResponseEntity<Map<String, Object>> getSubscriptionStatus(Authentication authentication) {
        try {
            Long userId = getCurrentUserId(authentication);
            
            boolean hasActiveSubscription = subscriptionService.hasActiveSubscription(userId);
            boolean canViewProfile = subscriptionService.canViewProfile(userId);
            boolean canSendContactRequest = subscriptionService.canSendContactRequest(userId);
            boolean canUploadPhoto = subscriptionService.canUploadPhoto(userId);
            boolean hasAccessToHighQuality = subscriptionService.hasAccessToHighQualityProfiles(userId);
            
            Map<String, Object> status = Map.of(
                "hasActiveSubscription", hasActiveSubscription,
                "canViewProfile", canViewProfile,
                "canSendContactRequest", canSendContactRequest,
                "canUploadPhoto", canUploadPhoto,
                "hasAccessToHighQualityProfiles", hasAccessToHighQuality
            );
            
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Helper methods
    private Long getCurrentUserId(Authentication authentication) {
        // Extract user ID from authentication
        // This is a simplified implementation
        return 1L; // TODO: Implement proper user ID extraction
    }

    private boolean isProfileOwnedByUser(UserProfile profile, Long userId) {
        // Check if the profile belongs to the specified user
        // This would require a user relationship in UserProfile
        return true; // TODO: Implement proper ownership check
    }
}
