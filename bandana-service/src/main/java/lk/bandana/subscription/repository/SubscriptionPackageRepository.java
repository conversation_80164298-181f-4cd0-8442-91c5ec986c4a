package lk.bandana.subscription.repository;

import lk.bandana.subscription.entity.SubscriptionPackage;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SubscriptionPackageRepository extends MongoRepository<SubscriptionPackage, String> {

    // Find active packages
    List<SubscriptionPackage> findByActiveTrue();

    // Find packages by type
    List<SubscriptionPackage> findByPackageType(SubscriptionPackage.PackageType packageType);

    // Find packages by target user type
    List<SubscriptionPackage> findByTargetUserType(SubscriptionPackage.UserType targetUserType);

    // Find active packages for specific user type
    @Query("{'active': true, 'targetUserType': ?0}")
    List<SubscriptionPackage> findActivePackagesForUserType(String userType);

    // Find featured packages
    List<SubscriptionPackage> findByFeaturedTrueAndActiveTrueOrderBySortOrder();

    // Find packages by billing cycle
    List<SubscriptionPackage> findByBillingCycleAndActiveTrue(SubscriptionPackage.BillingCycle billingCycle);

    // Find packages by quality tier
    @Query("{'active': true, 'qualityTier': {$gte: ?0}}")
    List<SubscriptionPackage> findByMinimumQualityTier(Integer minTier);

    // Find agent packages
    @Query("{'active': true, 'targetUserType': 'AGENT'}")
    List<SubscriptionPackage> findAgentPackages();

    // Find regular user packages
    @Query("{'active': true, 'targetUserType': 'REGULAR_USER'}")
    List<SubscriptionPackage> findRegularUserPackages();
}
