package lk.bandana.horoscope.entity;

import lk.bandana.horoscope.entity.helper.DashaBalance;
import lk.bandana.horoscope.entity.helper.LagnaSquare;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Property;
import org.springframework.data.neo4j.core.schema.Relationship;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 4/14/2023
 */

@Component
@Node
@Getter
@Setter
@ToString
public class Kendara {

    @Id
    private Long id;

    @Relationship("OWNS")
    private Person person;

    private long personId;

    @Property("Name")
    private String personName;

    private String lagna;

    private String birthDay;

    private List<LagnaSquare> sq1;

    private List<LagnaSquare> sq2;

    private List<LagnaSquare> sq3;

    private List<LagnaSquare> sq4;

    private List<LagnaSquare> sq5;

    private List<LagnaSquare> sq6;

    private List<LagnaSquare> sq7;

    private List<LagnaSquare> sq8;

    private List<LagnaSquare> sq9;

    private List<LagnaSquare> sq10;

    private List<LagnaSquare> sq11;

    private List<LagnaSquare> sq12;

    private String nekatha;

    private int nekathPada;

    private double ayanamsa;

    private double obliquity;

    private double siderealTime;

    private double lagnaSputa;

    private List<DashaBalance> dashaBalances;

}
