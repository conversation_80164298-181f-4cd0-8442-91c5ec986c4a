package lk.bandana.horoscope.entity.porondam;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.stereotype.Component;

@Node
@Component
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class Rajju {

    @Id
    private Integer id;

    private String name;

    private String snName;

}
