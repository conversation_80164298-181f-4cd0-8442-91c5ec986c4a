# Bandana Service Migration: Neo4j to MongoDB + Lombok to Modern Java

This migration script transforms the Bandana Service codebase from Neo4j to MongoDB and replaces Lombok annotations with modern Java 21 features.

## What This Migration Does

### 1. Database Migration (Neo4j → MongoDB)
- **Annotations**: `@Node` → `@Document`, `@Relationship` → `@DBRef`
- **Repositories**: `Neo4jRepository` → `MongoRepository`
- **Configuration**: Updates `@EnableNeo4jRepositories` → `@EnableMongoRepositories`
- **Dependencies**: Removes Neo4j dependencies from `pom.xml`
- **Properties**: Updates `application.properties` for MongoDB configuration

### 2. Code Modernization (Lombok → Modern Java)
- **@Data/@Getter/@Setter**: Replaced with explicit getter/setter methods
- **@Builder**: Replaced with fluent builder pattern using modern Java
- **@AllArgsConstructor/@NoArgsConstructor**: Replaced with explicit constructors
- **Imports**: Removes all Lombok imports

### 3. Files Affected
- All entity classes in `lk.bandana.*.entity` packages
- All repository interfaces in `lk.bandana.*.repository` packages
- Configuration classes (`DbConfig.java`, `InitDataRunner.java`)
- `pom.xml` dependencies
- `application.properties` configuration

## Prerequisites

- Python 3.6 or higher
- Java 21 LTS
- MongoDB 5.x or higher
- Maven 3.6+

## How to Run the Migration

### Option 1: Windows (Batch Script)
```cmd
# Navigate to your project root directory
cd G:\Projects\bandana

# Run the migration
run-migration.bat
```

### Option 2: Unix/Linux/Mac (Shell Script)
```bash
# Navigate to your project root directory
cd /path/to/bandana

# Make script executable (if needed)
chmod +x run-migration.sh

# Run the migration
./run-migration.sh
```

### Option 3: Direct Python Execution
```bash
# Navigate to your project root directory
cd /path/to/bandana

# Run the migration script directly
python bandana-migration-script.py .
```

## What the Script Changes

### Entity Classes Example
**Before (Neo4j + Lombok):**
```java
@Node
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Person {
    @Id
    private Long id;
    
    private String firstName;
    private String lastName;
    
    @Relationship
    private MetaData personType;
}
```

**After (MongoDB + Modern Java):**
```java
@Document
public class Person {
    @Id
    private Long id;
    
    private String firstName;
    private String lastName;
    
    @DBRef
    private MetaData personType;
    
    public Person() {
    }
    
    public Person(Long id, String firstName, String lastName, MetaData personType) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.personType = personType;
    }
    
    public Long getId() {
        return this.id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getFirstName() {
        return this.firstName;
    }
    
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    
    // ... other getters and setters
}
```

### Repository Interfaces Example
**Before:**
```java
@Repository
public interface PersonRepository extends Neo4jRepository<Person, Long> {
    List<Person> findPersonByFirstNameLike(String name);
}
```

**After:**
```java
@Repository
public interface PersonRepository extends MongoRepository<Person, Long> {
    List<Person> findPersonByFirstNameLike(String name);
}
```

## Post-Migration Steps

### 1. Verify Compilation
```bash
cd bandana-service
mvn clean compile
```

### 2. Update MongoDB Configuration
Edit `src/main/resources/application.properties`:
```properties
# MongoDB Configuration
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=bandana
spring.data.mongodb.auto-index-creation=true

# Optional: Authentication
# spring.data.mongodb.username=your_username
# spring.data.mongodb.password=your_password
# spring.data.mongodb.authentication-database=admin
```

### 3. Install and Start MongoDB
```bash
# Install MongoDB 5.x
# Start MongoDB service
mongod --dbpath /path/to/your/data/directory

# Or use Docker
docker run -d -p 27017:27017 --name mongodb mongo:5
```

### 4. Update Custom Queries
Review and update any custom database queries:
- Neo4j Cypher queries → MongoDB queries
- Graph relationships → Document references or embedded documents
- Update service layer methods that used Neo4j-specific features

### 5. Test the Application
```bash
# Run tests
mvn test

# Start the application
mvn spring-boot:run
```

## Important Notes

### Data Migration
This script only migrates the **code structure**. You'll need to migrate your actual data separately:

1. **Export data from Neo4j**
2. **Transform the data structure** (graph → document)
3. **Import data into MongoDB**

### Relationship Handling
- `@Relationship` annotations are replaced with `@DBRef`
- Consider whether you want references (`@DBRef`) or embedded documents
- Update your data model design for document-based storage

### Performance Considerations
- MongoDB uses different indexing strategies than Neo4j
- Review and create appropriate indexes for your queries
- Consider denormalization for frequently accessed data

## Troubleshooting

### Common Issues

1. **Compilation Errors**: Check for missing imports or incorrect method signatures
2. **Runtime Errors**: Verify MongoDB connection and database configuration
3. **Data Access Issues**: Update repository method implementations if needed

### Getting Help

If you encounter issues:
1. Check the console output for specific error messages
2. Verify all dependencies are correctly updated in `pom.xml`
3. Ensure MongoDB is running and accessible
4. Review the generated getter/setter methods for correctness

## Rollback Plan

Before running the migration:
1. **Commit all changes** to version control
2. **Create a backup** of your project
3. **Test the migration** on a copy first

To rollback:
```bash
git checkout -- .
# or
git reset --hard HEAD
```

## Next Steps After Migration

1. **Review generated code** for correctness
2. **Update unit tests** to work with MongoDB
3. **Optimize data model** for document storage
4. **Add MongoDB-specific indexes**
5. **Update documentation** to reflect the new architecture
