#!/usr/bin/env python3
"""
Apply Java 17 modern features to the Bandana Service codebase
- Records (Java 14+)
- Pattern Matching for instanceof (Java 16+)
- Switch Expressions (Java 14+)
- Text Blocks (Java 15+)
- Sealed Classes (Java 17+)
"""

import os
import re
from pathlib import Path

def update_response_for_java17():
    """Update Response record for Java 17 compatibility"""
    response_file = Path("bandana-service/src/main/java/lk/bandana/core/entity/Response.java")
    
    if not response_file.exists():
        print("Response.java not found")
        return
    
    java17_response = '''package lk.bandana.core.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import java.time.Instant;
import java.time.format.DateTimeFormatter;

/**
 * Response record using Java 17 features
 */
@Document
public record Response<T>(
    @Id String id,
    boolean success,
    String message,
    T data,
    String errorCode,
    Instant timestamp
) {
    
    // Compact constructor with validation
    public Response {
        if (timestamp == null) {
            timestamp = Instant.now();
        }
        
        if (message == null) {
            message = success ? "Operation completed successfully" : "Operation failed";
        }
    }
    
    // Static factory methods
    public static <T> Response<T> success(T data) {
        return new Response<>(
            null,
            true,
            "Successfully processed data of type " + data.getClass().getSimpleName(),
            data,
            null,
            Instant.now()
        );
    }
    
    public static <T> Response<T> success(String operation, T data) {
        return new Response<>(
            null,
            true,
            operation + " completed successfully",
            data,
            null,
            Instant.now()
        );
    }
    
    public static <T> Response<T> error(String message, String errorCode) {
        return new Response<>(
            null,
            false,
            "Error [" + errorCode + "]: " + message,
            null,
            errorCode,
            Instant.now()
        );
    }
    
    // Pattern matching methods using Java 17 features
    public String getFormattedMessage() {
        var timeStr = DateTimeFormatter.ISO_INSTANT.format(timestamp);
        return success ? 
            "✓ " + message + " at " + timeStr :
            "✗ [" + errorCode + "] " + message + " at " + timeStr;
    }
    
    public boolean hasValidData() {
        return success && data != null;
    }
    
    // Modern toString using text blocks (Java 15+)
    @Override
    public String toString() {
        return \"\"\"
            Response {
                success: %s
                message: "%s"
                hasData: %s
                timestamp: %s
            }
            \"\"\".formatted(success, message, data != null, timestamp);
    }
}
'''
    
    with open(response_file, 'w', encoding='utf-8') as f:
        f.write(java17_response)
    
    print("✓ Updated Response.java for Java 17 compatibility")

def create_java17_service():
    """Create a service using Java 17 features"""
    service_dir = Path("bandana-service/src/main/java/lk/bandana/core/service/modern")
    service_dir.mkdir(parents=True, exist_ok=True)
    
    java17_service = '''package lk.bandana.core.service.modern;

import org.springframework.stereotype.Service;
import java.util.concurrent.CompletableFuture;
import java.util.List;
import java.util.concurrent.Executors;

/**
 * Modern service using Java 17 features
 */
@Service
public class ModernMatchingService {
    
    private final var executor = Executors.newVirtualThreadPerTaskExecutor();
    
    /**
     * Process multiple profile matches using modern Java patterns
     */
    public CompletableFuture<List<MatchResult>> processMatches(List<String> profileIds) {
        return CompletableFuture.supplyAsync(() -> {
            return profileIds.stream()
                .map(this::processMatch)
                .toList();
        }, executor);
    }
    
    /**
     * Process a single match using pattern matching
     */
    private MatchResult processMatch(String profileId) {
        // Simulate processing
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return new MatchResult.Failed(profileId, "Interrupted");
        }
        
        // Use switch expression (Java 14+)
        return switch (profileId.length() % 3) {
            case 0 -> new MatchResult.HighCompatibility(profileId, 95);
            case 1 -> new MatchResult.MediumCompatibility(profileId, 75);
            default -> new MatchResult.LowCompatibility(profileId, 45);
        };
    }
    
    /**
     * Sealed interface for match results (Java 17+)
     */
    public sealed interface MatchResult 
        permits MatchResult.HighCompatibility, 
                MatchResult.MediumCompatibility, 
                MatchResult.LowCompatibility, 
                MatchResult.Failed {
        
        String profileId();
        
        record HighCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return "High compatibility match (%d%%) for profile %s".formatted(score, profileId);
            }
        }
        
        record MediumCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return "Medium compatibility match (%d%%) for profile %s".formatted(score, profileId);
            }
        }
        
        record LowCompatibility(String profileId, int score) implements MatchResult {
            public String getDescription() {
                return "Low compatibility match (%d%%) for profile %s".formatted(score, profileId);
            }
        }
        
        record Failed(String profileId, String reason) implements MatchResult {
            public String getDescription() {
                return "Match failed for profile %s: %s".formatted(profileId, reason);
            }
        }
        
        // Pattern matching helper using instanceof (Java 16+)
        default String getFormattedResult() {
            if (this instanceof HighCompatibility high) {
                return "🎯 Excellent match for " + high.profileId() + " (" + high.score() + "%)";
            } else if (this instanceof MediumCompatibility medium) {
                return "👍 Good match for " + medium.profileId() + " (" + medium.score() + "%)";
            } else if (this instanceof LowCompatibility low) {
                return "👌 Possible match for " + low.profileId() + " (" + low.score() + "%)";
            } else if (this instanceof Failed failed) {
                return "❌ Failed to match " + failed.profileId() + ": " + failed.reason();
            }
            return "Unknown result";
        }
    }
}
'''
    
    with open(service_dir / "ModernMatchingService.java", 'w', encoding='utf-8') as f:
        f.write(java17_service)
    
    print("✓ Created ModernMatchingService with Java 17 features")

def update_controller_for_java17():
    """Update controller with Java 17 patterns"""
    controller_file = Path("bandana-service/src/main/java/lk/bandana/general/controller/GeneralProfileController.java")
    
    if not controller_file.exists():
        print("GeneralProfileController.java not found")
        return
    
    try:
        with open(controller_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add modern error handling method using Java 17 features
        modern_error_handling = '''
    
    /**
     * Modern error handling using Java 17 pattern matching
     */
    private ResponseEntity<Response<Object>> handleError(Exception e, String operation) {
        var errorResponse = createErrorResponse(e, operation);
        var httpStatus = determineHttpStatus(e);
        
        return ResponseEntity.status(httpStatus).body(errorResponse);
    }
    
    private Response<Object> createErrorResponse(Exception e, String operation) {
        return switch (e) {
            case IllegalArgumentException iae -> 
                Response.error("Invalid argument in " + operation + ": " + iae.getMessage(), "INVALID_ARGUMENT");
            case SecurityException se -> 
                Response.error("Access denied for " + operation + ": " + se.getMessage(), "ACCESS_DENIED");
            case RuntimeException re -> 
                Response.error("Runtime error in " + operation + ": " + re.getMessage(), "RUNTIME_ERROR");
            default -> 
                Response.error("Unexpected error in " + operation + ": " + e.getMessage(), "UNKNOWN_ERROR");
        };
    }
    
    private HttpStatus determineHttpStatus(Exception e) {
        return switch (e) {
            case IllegalArgumentException ignored -> HttpStatus.BAD_REQUEST;
            case SecurityException ignored -> HttpStatus.FORBIDDEN;
            default -> HttpStatus.INTERNAL_SERVER_ERROR;
        };
    }
    
    /**
     * Validate request using pattern matching (Java 16+)
     */
    private ValidationResult validateRequest(Object request) {
        if (request instanceof UserProfile profile) {
            if (profile.getFirstName() == null || profile.getFirstName().isBlank()) {
                return new ValidationResult(false, "First name is required");
            }
            if (profile.getLastName() == null || profile.getLastName().isBlank()) {
                return new ValidationResult(false, "Last name is required");
            }
            return new ValidationResult(true, "Valid profile");
        } else if (request instanceof java.util.Map<?, ?> map) {
            if (map.isEmpty()) {
                return new ValidationResult(false, "Request data cannot be empty");
            }
            return new ValidationResult(true, "Valid request");
        } else if (request == null) {
            return new ValidationResult(false, "Request cannot be null");
        } else {
            return new ValidationResult(false, "Unsupported request type: " + request.getClass().getSimpleName());
        }
    }
    
    record ValidationResult(boolean isValid, String message) {}'''
        
        # Add before the last closing brace
        content = content.rstrip()
        if content.endswith('}'):
            content = content[:-1] + modern_error_handling + '\n}'
        
        with open(controller_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ Updated GeneralProfileController with Java 17 features")
    
    except Exception as e:
        print(f"✗ Error updating controller: {e}")

def create_java17_examples():
    """Create examples showcasing Java 17 features"""
    examples_dir = Path("bandana-service/src/main/java/lk/bandana/examples")
    examples_dir.mkdir(parents=True, exist_ok=True)
    
    # Create Java 17 features example
    java17_example = '''package lk.bandana.examples;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Examples of Java 17 features
 */
public class Java17Examples {
    
    // Sealed interface (Java 17)
    public sealed interface UserType 
        permits RegularUser, PremiumUser, AgentUser {
        String getUserId();
    }
    
    public record RegularUser(String userId, int profileViews) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    public record PremiumUser(String userId, int profileViews, List<String> features) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    public record AgentUser(String userId, String agencyName, int clientCount) implements UserType {
        @Override
        public String getUserId() { return userId; }
    }
    
    // Switch expressions (Java 14+)
    public static String getUserDescription(UserType user) {
        return switch (user) {
            case RegularUser(var id, var views) -> 
                "Regular user %s with %d profile views".formatted(id, views);
            case PremiumUser(var id, var views, var features) -> 
                "Premium user %s with %d views and features: %s".formatted(id, views, String.join(", ", features));
            case AgentUser(var id, var agency, var clients) -> 
                "Agent %s from %s managing %d clients".formatted(id, agency, clients);
        };
    }
    
    // Pattern matching for instanceof (Java 16+)
    public static String getAccessLevel(UserType user) {
        if (user instanceof RegularUser regular && regular.profileViews() > 100) {
            return "High Activity Regular";
        } else if (user instanceof RegularUser) {
            return "Standard Regular";
        } else if (user instanceof PremiumUser premium && premium.features().contains("unlimited_views")) {
            return "Premium Unlimited";
        } else if (user instanceof PremiumUser) {
            return "Premium Limited";
        } else if (user instanceof AgentUser agent && agent.clientCount() > 50) {
            return "Senior Agent";
        } else if (user instanceof AgentUser) {
            return "Junior Agent";
        }
        return "Unknown";
    }
    
    // Text blocks (Java 15+)
    public static String generateProfileSummary(String name, int age, String location) {
        return \"\"\"
            Profile Summary:
            Name: %s
            Age: %d years old
            Location: %s
            Last updated: %s
            \"\"\".formatted(name, age, location, LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
    }
    
    // Records with validation
    public record UserNotification(String userName, String message, LocalDateTime timestamp) {
        public UserNotification {
            if (userName == null || userName.isBlank()) {
                throw new IllegalArgumentException("User name cannot be null or blank");
            }
            if (message == null || message.isBlank()) {
                throw new IllegalArgumentException("Message cannot be null or blank");
            }
            if (timestamp == null) {
                timestamp = LocalDateTime.now();
            }
        }
        
        public String getFormattedNotification() {
            return \"\"\"
                📧 Notification for %s
                Message: %s
                Sent: %s
                \"\"\".formatted(userName, message, timestamp.format(DateTimeFormatter.ofPattern("MMM dd, yyyy 'at' HH:mm")));
        }
    }
}
'''
    
    with open(examples_dir / "Java17Examples.java", 'w', encoding='utf-8') as f:
        f.write(java17_example)
    
    print("✓ Created Java 17 feature examples")

def main():
    print("Applying Java 17 modern features...")
    
    # Update Response record
    update_response_for_java17()
    
    # Create modern service
    create_java17_service()
    
    # Update controller
    update_controller_for_java17()
    
    # Create examples
    create_java17_examples()
    
    print("\n🚀 Java 17 modernization completed!")
    print("\nModern features applied:")
    print("- Records for immutable data classes")
    print("- Sealed interfaces for type safety")
    print("- Pattern matching for instanceof")
    print("- Switch expressions")
    print("- Text blocks for multi-line strings")
    print("- Modern validation patterns")

if __name__ == "__main__":
    main()
