#!/usr/bin/env python3
"""
Fix compilation issues after migration
"""

import os
import re
from pathlib import Path

def fix_java_files(project_root):
    """Fix common compilation issues in Java files"""
    bandana_service = Path(project_root) / "bandana-service"
    src_main_java = bandana_service / "src" / "main" / "java"
    
    java_files = list(src_main_java.rglob("*.java"))
    
    for java_file in java_files:
        try:
            with open(java_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Fix common issues
            # 1. Remove any BOM characters
            if content.startswith('\ufeff'):
                content = content[1:]
            
            # 2. Fix getter/setter method names for camelCase fields
            content = fix_getter_setter_names(content)
            
            # 3. Fix @DBRef annotation usage
            content = fix_dbref_annotations(content)
            
            # 4. Add missing imports
            content = add_missing_imports(content)
            
            # 5. Fix any malformed method signatures
            content = fix_method_signatures(content)
            
            # Write back if changed
            if content != original_content:
                with open(java_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"Fixed: {java_file}")
        
        except Exception as e:
            print(f"Error processing {java_file}: {e}")

def fix_getter_setter_names(content):
    """Fix getter/setter method names for camelCase fields"""
    
    # Find camelCase fields and fix their getter/setter names
    field_pattern = r'private\s+(\w+(?:<[^>]+>)?)\s+([a-z][a-zA-Z0-9]*);'
    fields = re.findall(field_pattern, content)
    
    for field_type, field_name in fields:
        # Fix getter name
        wrong_getter = f"get{field_name.lower()}"
        correct_getter = f"get{field_name.capitalize()}"
        content = content.replace(f"public {field_type} {wrong_getter}()", f"public {field_type} {correct_getter}()")
        
        # Fix setter name
        wrong_setter = f"set{field_name.lower()}"
        correct_setter = f"set{field_name.capitalize()}"
        content = content.replace(f"public void {wrong_setter}({field_type} {field_name})", f"public void {correct_setter}({field_type} {field_name})")
        
        # Fix boolean getter
        if field_type.lower() == 'boolean':
            wrong_is_getter = f"is{field_name.lower()}"
            correct_is_getter = f"is{field_name.capitalize()}"
            content = content.replace(f"public {field_type} {wrong_is_getter}()", f"public {field_type} {correct_is_getter}()")
    
    return content

def fix_dbref_annotations(content):
    """Fix @DBRef annotation usage"""
    
    # @DBRef should not have parameters like @Relationship did
    content = re.sub(r'@DBRef\([^)]*\)', '@DBRef', content)
    
    return content

def add_missing_imports(content):
    """Add missing imports"""
    
    imports_to_add = []
    
    # Check for missing imports
    if '@Document' in content and 'import org.springframework.data.mongodb.core.mapping.Document;' not in content:
        imports_to_add.append('org.springframework.data.mongodb.core.mapping.Document')
    
    if '@DBRef' in content and 'import org.springframework.data.mongodb.core.mapping.DBRef;' not in content:
        imports_to_add.append('org.springframework.data.mongodb.core.mapping.DBRef')
    
    if '@Id' in content and 'import org.springframework.data.annotation.Id;' not in content:
        imports_to_add.append('org.springframework.data.annotation.Id')
    
    if 'MongoRepository' in content and 'import org.springframework.data.mongodb.repository.MongoRepository;' not in content:
        imports_to_add.append('org.springframework.data.mongodb.repository.MongoRepository')
    
    # Add imports
    for import_statement in imports_to_add:
        import_line = f'import {import_statement};\n'
        
        # Find the last import statement
        import_pattern = r'(import .*?;\n)'
        imports = re.findall(import_pattern, content)
        
        if imports:
            last_import = imports[-1]
            content = content.replace(last_import, last_import + import_line)
        else:
            # Add after package statement
            package_pattern = r'(package .*?;\n)'
            content = re.sub(package_pattern, r'\1\n' + import_line, content)
    
    return content

def fix_method_signatures(content):
    """Fix any malformed method signatures"""
    
    # Fix any double spaces or formatting issues
    content = re.sub(r'\s+', ' ', content)
    content = re.sub(r'\s*\n\s*', '\n', content)
    
    return content

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python fix-compilation-issues.py <project_root_path>")
        sys.exit(1)
    
    project_root = sys.argv[1]
    
    if not os.path.exists(project_root):
        print(f"Project root path does not exist: {project_root}")
        sys.exit(1)
    
    print("Fixing compilation issues...")
    fix_java_files(project_root)
    print("Done!")

if __name__ == "__main__":
    main()
